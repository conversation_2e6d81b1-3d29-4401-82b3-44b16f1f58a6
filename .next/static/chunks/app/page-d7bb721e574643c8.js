(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{1648:function(e,r,t){Promise.resolve().then(t.bind(t,1028))},1028:function(e,r,t){"use strict";t.d(r,{default:function(){return x}});var a=t(7437),s=t(2265);function n(e){let{value:r,onChange:t,placeholder:s="e.g., A cute owl focusing on a book, vibrant colors",disabled:n=!1}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{htmlFor:"promptInput",className:"block text-gray-700 text-lg font-medium mb-2",children:"Describe your app icon:"}),(0,a.jsx)("input",{id:"promptInput",type:"text",value:r,onChange:e=>t(e.target.value),placeholder:s,disabled:n,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed"})]})}function l(e){let{value:r,onChange:t,onAnalyze:s,placeholder:n="e.g., https://example.com or example.com",disabled:l=!1,isAnalyzing:i=!1}=e;return(0,a.jsxs)("div",{className:"w-full space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"urlInput",className:"block text-gray-700 text-lg font-medium mb-2",children:"Enter website URL:"}),(0,a.jsx)("input",{id:"urlInput",type:"url",value:r,onChange:e=>t(e.target.value),onKeyPress:e=>{"Enter"===e.key&&!l&&!i&&r.trim()&&s()},placeholder:n,disabled:l,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed"})]}),(0,a.jsx)("button",{onClick:s,disabled:l||i||!r.trim(),className:"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"}),"Analyzing Website..."]}):(0,a.jsx)(a.Fragment,{children:"\uD83D\uDD0D Analyze Website"})})]})}function i(e){let r,t,{websiteData:s,generatedPrompt:n,onPromptChange:l,onGenerateIcon:i,isGenerating:o=!1}=e;return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-6 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[s.favicon&&(0,a.jsx)("img",{src:s.favicon,alt:"Website favicon",className:"w-8 h-8 rounded",onError:e=>{e.target.style.display="none"}}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:s.siteName||s.title||"Website"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 break-all",children:s.url})]})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Description:"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-3",children:s.description})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Category:"}),(0,a.jsx)("span",{className:"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:(r=s.category||"other").charAt(0).toUpperCase()+r.slice(1)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Style:"}),(0,a.jsx)("span",{className:"inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full",children:(t=s.visualStyle||"modern").charAt(0).toUpperCase()+t.slice(1)})]})]}),s.primaryColors&&s.primaryColors.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Primary Colors:"}),(0,a.jsx)("div",{className:"flex gap-2 flex-wrap",children:s.primaryColors.slice(0,5).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e},title:e}),(0,a.jsx)("span",{className:"text-xs text-gray-600",children:e})]},r))})]}),s.keywords&&s.keywords.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Keywords:"}),(0,a.jsx)("div",{className:"flex gap-1 flex-wrap",children:s.keywords.slice(0,8).map((e,r)=>(0,a.jsx)("span",{className:"inline-block bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded",children:e},r))})]}),(0,a.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"promptEditor",className:"block text-sm font-medium text-gray-700 mb-2",children:"Generated Prompt (editable):"}),(0,a.jsx)("textarea",{id:"promptEditor",value:n,onChange:e=>l(e.target.value),disabled:o,rows:6,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm font-mono",placeholder:"Generated prompt will appear here..."}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"You can edit this prompt before generating the icon to fine-tune the results."})]}),(0,a.jsx)("button",{onClick:i,disabled:o||!n.trim(),className:"w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"}),"Generating Icon..."]}):(0,a.jsx)(a.Fragment,{children:"✨ Generate Icon from Website"})})]})]})}var o=t(3145);function d(e){let{imageUrl:r,isLoading:t,placeholder:s="Your generated icon will appear here."}=e;return(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-2xl min-h-[200px] w-full flex justify-center items-center overflow-hidden bg-gray-50",children:t?(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("div",{className:"border-4 border-gray-200 border-t-indigo-600 rounded-full w-10 h-10 animate-spin"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Generating your icon..."})]}):r?(0,a.jsx)("div",{className:"relative w-full h-full min-h-[200px] flex items-center justify-center p-4",children:(0,a.jsx)(o.default,{src:r,alt:"Generated App Icon",width:300,height:300,className:"max-w-full max-h-full object-contain rounded-xl shadow-lg",priority:!0})}):(0,a.jsx)("p",{className:"text-gray-500 text-center px-4",children:s})})}function c(e){let{message:r}=e;return r.visible?(0,a.jsx)("div",{className:"rounded-xl border px-4 py-3 text-center font-medium transition-all duration-300 ".concat((()=>{switch(r.type){case"error":return"bg-red-100 text-red-800 border-red-200";case"success":return"bg-green-100 text-green-800 border-green-200";default:return"bg-yellow-100 text-yellow-800 border-yellow-200"}})()),role:"alert",children:r.text}):null}function x(){let[e,r]=(0,s.useState)("text"),[t,o]=(0,s.useState)(""),[x,u]=(0,s.useState)(""),[m,p]=(0,s.useState)(null),[g,h]=(0,s.useState)(""),[b,y]=(0,s.useState)(!1),[f,j]=(0,s.useState)(),[w,v]=(0,s.useState)(!1),[N,C]=(0,s.useState)({text:"",type:"info",visible:!1}),P=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";C({text:e,type:r,visible:!0})},k=()=>{C(e=>({...e,visible:!1}))},S=e=>{r(e),k(),j(void 0),"text"===e?(u(""),p(null),h("")):o("")},F=async()=>{if(!x.trim()){P("Please enter a valid URL.","error");return}k(),y(!0),p(null),h("");try{let e=await fetch("/api/analyze-website",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:x.trim()})}),r=await e.json();r.success&&r.data?(p(r.data),h(r.generatedPrompt||""),P("Website analyzed successfully! Review the details and prompt below, then generate your icon.","success")):P(r.error||"Failed to analyze website. Please try again.","error")}catch(e){console.error("Error analyzing website:",e),P("Failed to analyze website. Please check the URL and try again.","error")}finally{y(!1)}},D=async()=>{if(!m||!g.trim()){P("Please analyze a website and ensure the prompt is not empty.","error");return}k(),v(!0),j(void 0);try{let e=await fetch("/api/generate-icon",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:g})}),r=await e.json();r.success&&r.imageUrl?(j(r.imageUrl),P("Icon generated successfully from website analysis!","success")):P(r.error||"Failed to generate icon. Please try again.","error")}catch(e){console.error("Error generating icon from website:",e),P("Failed to generate icon from website. Please try again.","error")}finally{v(!1)}},I=async()=>{k(),v(!0);try{let e=await fetch("/api/enhance-prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userIdea:t})}),r=await e.json();r.success&&r.enhancedPrompt?(o(r.enhancedPrompt),P("Prompt enhanced successfully! Now try generating an icon.","success")):P(r.error||"Could not enhance prompt. Please try again.","error")}catch(e){console.error("Error enhancing prompt:",e),P("Failed to enhance prompt. Please try again.","error")}finally{v(!1)}},E=async()=>{if(!t.trim()){P("Please enter a description for the app icon.","error");return}k(),v(!0),j(void 0);try{let e=await fetch("/api/generate-icon",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:t})}),r=await e.json();r.success&&r.imageUrl?(j(r.imageUrl),P("Icon generated successfully!","success")):P(r.error||"Failed to generate icon. Please try again.","error")}catch(e){console.error("Error generating icon:",e),P("Failed to generate icon. Please try again.","error")}finally{v(!1)}};return(0,a.jsx)("div",{className:"w-full max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"App Icon Generator"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Create stunning app icons with AI"})]}),(0,a.jsxs)("div",{className:"flex bg-gray-100 rounded-xl p-1",children:[(0,a.jsx)("button",{onClick:()=>S("text"),className:"flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ".concat("text"===e?"bg-white text-indigo-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"\uD83D\uDCDD Text Prompt"}),(0,a.jsx)("button",{onClick:()=>S("url"),className:"flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ".concat("url"===e?"bg-white text-indigo-600 shadow-sm":"text-gray-600 hover:text-gray-800"),children:"\uD83C\uDF10 From Website"})]}),"text"===e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n,{value:t,onChange:o,disabled:w}),(0,a.jsxs)("div",{className:"flex gap-4 flex-col sm:flex-row",children:[(0,a.jsx)("button",{onClick:I,disabled:w,className:"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"✨ Enhance Prompt ✨"}),(0,a.jsx)("button",{onClick:E,disabled:w,className:"flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"Generate Icon"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l,{value:x,onChange:u,onAnalyze:F,disabled:w||b,isAnalyzing:b}),m&&(0,a.jsx)(i,{websiteData:m,generatedPrompt:g,onPromptChange:h,onGenerateIcon:D,isGenerating:w})]}),(0,a.jsx)(d,{imageUrl:f,isLoading:w}),(0,a.jsx)(c,{message:N})]})})}}},function(e){e.O(0,[145,971,117,744],function(){return e(e.s=1648)}),_N_E=e.O()}]);