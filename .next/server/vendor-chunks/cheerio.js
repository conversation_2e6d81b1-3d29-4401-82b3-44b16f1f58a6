"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cheerio";
exports.ids = ["vendor-chunks/cheerio"];
exports.modules = {

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js":
/*!*********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/attributes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   attr: () => (/* binding */ attr),\n/* harmony export */   data: () => (/* binding */ data),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   prop: () => (/* binding */ prop),\n/* harmony export */   removeAttr: () => (/* binding */ removeAttr),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   toggleClass: () => (/* binding */ toggleClass),\n/* harmony export */   val: () => (/* binding */ val)\n/* harmony export */ });\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/**\n * Methods for getting and modifying attributes.\n *\n * @module cheerio/attributes\n */\nvar _a;\n\n\n\n\n\nconst hasOwn = \n// @ts-expect-error `hasOwn` is a standard object method\n(_a = Object.hasOwn) !== null && _a !== void 0 ? _a : ((object, prop) => Object.prototype.hasOwnProperty.call(object, prop));\nconst rspace = /\\s+/;\nconst dataAttrPrefix = 'data-';\n// Attributes that are booleans\nconst rboolean = /^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i;\n// Matches strings that look like JSON objects or arrays\nconst rbrace = /^{[^]*}$|^\\[[^]*]$/;\nfunction getAttr(elem, name, xmlMode) {\n    var _a;\n    if (!elem || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n        return undefined;\n    (_a = elem.attribs) !== null && _a !== void 0 ? _a : (elem.attribs = {});\n    // Return the entire attribs object if no attribute specified\n    if (!name) {\n        return elem.attribs;\n    }\n    if (hasOwn(elem.attribs, name)) {\n        // Get the (decoded) attribute\n        return !xmlMode && rboolean.test(name) ? name : elem.attribs[name];\n    }\n    // Mimic the DOM and return text content as value for `option's`\n    if (elem.name === 'option' && name === 'value') {\n        return (0,_static_js__WEBPACK_IMPORTED_MODULE_0__.text)(elem.children);\n    }\n    // Mimic DOM with default value for radios/checkboxes\n    if (elem.name === 'input' &&\n        (elem.attribs['type'] === 'radio' || elem.attribs['type'] === 'checkbox') &&\n        name === 'value') {\n        return 'on';\n    }\n    return undefined;\n}\n/**\n * Sets the value of an attribute. The attribute will be deleted if the value is\n * `null`.\n *\n * @private\n * @param el - The element to set the attribute on.\n * @param name - The attribute's name.\n * @param value - The attribute's value.\n */\nfunction setAttr(el, name, value) {\n    if (value === null) {\n        removeAttribute(el, name);\n    }\n    else {\n        el.attribs[name] = `${value}`;\n    }\n}\nfunction attr(name, value) {\n    // Set the value (with attr map support)\n    if (typeof name === 'object' || value !== undefined) {\n        if (typeof value === 'function') {\n            if (typeof name !== 'string') {\n                {\n                    throw new Error('Bad combination of arguments.');\n                }\n            }\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    setAttr(el, name, value.call(el, i, el.attribs[name]));\n            });\n        }\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                return;\n            if (typeof name === 'object') {\n                for (const objName of Object.keys(name)) {\n                    const objValue = name[objName];\n                    setAttr(el, objName, objValue);\n                }\n            }\n            else {\n                setAttr(el, name, value);\n            }\n        });\n    }\n    return arguments.length > 1\n        ? this\n        : getAttr(this[0], name, this.options.xmlMode);\n}\n/**\n * Gets a node's prop.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the prop of.\n * @param name - Name of the prop.\n * @param xmlMode - Disable handling of special HTML attributes.\n * @returns The prop's value.\n */\nfunction getProp(el, name, xmlMode) {\n    return name in el\n        ? // @ts-expect-error TS doesn't like us accessing the value directly here.\n            el[name]\n        : !xmlMode && rboolean.test(name)\n            ? getAttr(el, name, false) !== undefined\n            : getAttr(el, name, xmlMode);\n}\n/**\n * Sets the value of a prop.\n *\n * @private\n * @param el - The element to set the prop on.\n * @param name - The prop's name.\n * @param value - The prop's value.\n * @param xmlMode - Disable handling of special HTML attributes.\n */\nfunction setProp(el, name, value, xmlMode) {\n    if (name in el) {\n        // @ts-expect-error Overriding value\n        el[name] = value;\n    }\n    else {\n        setAttr(el, name, !xmlMode && rboolean.test(name)\n            ? value\n                ? ''\n                : null\n            : `${value}`);\n    }\n}\nfunction prop(name, value) {\n    var _a;\n    if (typeof name === 'string' && value === undefined) {\n        const el = this[0];\n        if (!el)\n            return undefined;\n        switch (name) {\n            case 'style': {\n                const property = this.css();\n                const keys = Object.keys(property);\n                for (let i = 0; i < keys.length; i++) {\n                    property[i] = keys[i];\n                }\n                property.length = keys.length;\n                return property;\n            }\n            case 'tagName':\n            case 'nodeName': {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                return el.name.toUpperCase();\n            }\n            case 'href':\n            case 'src': {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                const prop = (_a = el.attribs) === null || _a === void 0 ? void 0 : _a[name];\n                if (typeof URL !== 'undefined' &&\n                    ((name === 'href' && (el.tagName === 'a' || el.tagName === 'link')) ||\n                        (name === 'src' &&\n                            (el.tagName === 'img' ||\n                                el.tagName === 'iframe' ||\n                                el.tagName === 'audio' ||\n                                el.tagName === 'video' ||\n                                el.tagName === 'source'))) &&\n                    prop !== undefined &&\n                    this.options.baseURI) {\n                    return new URL(prop, this.options.baseURI).href;\n                }\n                return prop;\n            }\n            case 'innerText': {\n                return (0,domutils__WEBPACK_IMPORTED_MODULE_3__.innerText)(el);\n            }\n            case 'textContent': {\n                return (0,domutils__WEBPACK_IMPORTED_MODULE_3__.textContent)(el);\n            }\n            case 'outerHTML': {\n                if (el.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Root)\n                    return this.html();\n                return this.clone().wrap('<container />').parent().html();\n            }\n            case 'innerHTML': {\n                return this.html();\n            }\n            default: {\n                if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                    return undefined;\n                return getProp(el, name, this.options.xmlMode);\n            }\n        }\n    }\n    if (typeof name === 'object' || value !== undefined) {\n        if (typeof value === 'function') {\n            if (typeof name === 'object') {\n                throw new TypeError('Bad combination of arguments.');\n            }\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                    setProp(el, name, value.call(el, i, getProp(el, name, this.options.xmlMode)), this.options.xmlMode);\n                }\n            });\n        }\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n                return;\n            if (typeof name === 'object') {\n                for (const key of Object.keys(name)) {\n                    const val = name[key];\n                    setProp(el, key, val, this.options.xmlMode);\n                }\n            }\n            else {\n                setProp(el, name, value, this.options.xmlMode);\n            }\n        });\n    }\n    return undefined;\n}\n/**\n * Sets the value of a data attribute.\n *\n * @private\n * @param elem - The element to set the data attribute on.\n * @param name - The data attribute's name.\n * @param value - The data attribute's value.\n */\nfunction setData(elem, name, value) {\n    var _a;\n    (_a = elem.data) !== null && _a !== void 0 ? _a : (elem.data = {});\n    if (typeof name === 'object')\n        Object.assign(elem.data, name);\n    else if (typeof name === 'string' && value !== undefined) {\n        elem.data[name] = value;\n    }\n}\n/**\n * Read _all_ HTML5 `data-*` attributes from the equivalent HTML5 `data-*`\n * attribute, and cache the value in the node's internal data store.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the data attribute of.\n * @returns A map with all of the data attributes.\n */\nfunction readAllData(el) {\n    for (const domName of Object.keys(el.attribs)) {\n        if (!domName.startsWith(dataAttrPrefix)) {\n            continue;\n        }\n        const jsName = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.camelCase)(domName.slice(dataAttrPrefix.length));\n        if (!hasOwn(el.data, jsName)) {\n            el.data[jsName] = parseDataValue(el.attribs[domName]);\n        }\n    }\n    return el.data;\n}\n/**\n * Read the specified attribute from the equivalent HTML5 `data-*` attribute,\n * and (if present) cache the value in the node's internal data store.\n *\n * @private\n * @category Attributes\n * @param el - Element to get the data attribute of.\n * @param name - Name of the data attribute.\n * @returns The data attribute's value.\n */\nfunction readData(el, name) {\n    const domName = dataAttrPrefix + (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.cssCase)(name);\n    const data = el.data;\n    if (hasOwn(data, name)) {\n        return data[name];\n    }\n    if (hasOwn(el.attribs, domName)) {\n        return (data[name] = parseDataValue(el.attribs[domName]));\n    }\n    return undefined;\n}\n/**\n * Coerce string data-* attributes to their corresponding JavaScript primitives.\n *\n * @private\n * @category Attributes\n * @param value - The value to parse.\n * @returns The parsed value.\n */\nfunction parseDataValue(value) {\n    if (value === 'null')\n        return null;\n    if (value === 'true')\n        return true;\n    if (value === 'false')\n        return false;\n    const num = Number(value);\n    if (value === String(num))\n        return num;\n    if (rbrace.test(value)) {\n        try {\n            return JSON.parse(value);\n        }\n        catch {\n            /* Ignore */\n        }\n    }\n    return value;\n}\nfunction data(name, value) {\n    var _a;\n    const elem = this[0];\n    if (!elem || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n        return;\n    const dataEl = elem;\n    (_a = dataEl.data) !== null && _a !== void 0 ? _a : (dataEl.data = {});\n    // Return the entire data object if no data specified\n    if (name == null) {\n        return readAllData(dataEl);\n    }\n    // Set the value (with attr map support)\n    if (typeof name === 'object' || value !== undefined) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                if (typeof name === 'object')\n                    setData(el, name);\n                else\n                    setData(el, name, value);\n            }\n        });\n        return this;\n    }\n    return readData(dataEl, name);\n}\nfunction val(value) {\n    const querying = arguments.length === 0;\n    const element = this[0];\n    if (!element || !(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(element))\n        return querying ? undefined : this;\n    switch (element.name) {\n        case 'textarea': {\n            return this.text(value);\n        }\n        case 'select': {\n            const option = this.find('option:selected');\n            if (!querying) {\n                if (this.attr('multiple') == null && typeof value === 'object') {\n                    return this;\n                }\n                this.find('option').removeAttr('selected');\n                const values = typeof value === 'object' ? value : [value];\n                for (const val of values) {\n                    this.find(`option[value=\"${val}\"]`).attr('selected', '');\n                }\n                return this;\n            }\n            return this.attr('multiple')\n                ? option.toArray().map((el) => (0,_static_js__WEBPACK_IMPORTED_MODULE_0__.text)(el.children))\n                : option.attr('value');\n        }\n        case 'input':\n        case 'option': {\n            return querying\n                ? this.attr('value')\n                : this.attr('value', value);\n        }\n    }\n    return undefined;\n}\n/**\n * Remove an attribute.\n *\n * @private\n * @param elem - Node to remove attribute from.\n * @param name - Name of the attribute to remove.\n */\nfunction removeAttribute(elem, name) {\n    if (!elem.attribs || !hasOwn(elem.attribs, name))\n        return;\n    delete elem.attribs[name];\n}\n/**\n * Splits a space-separated list of names to individual names.\n *\n * @category Attributes\n * @param names - Names to split.\n * @returns - Split names.\n */\nfunction splitNames(names) {\n    return names ? names.trim().split(rspace) : [];\n}\n/**\n * Method for removing attributes by `name`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').removeAttr('class').prop('outerHTML');\n * //=> <li>Pear</li>\n *\n * $('.apple').attr('id', 'favorite');\n * $('.apple').removeAttr('id class').prop('outerHTML');\n * //=> <li>Apple</li>\n * ```\n *\n * @param name - Name of the attribute.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/removeAttr/}\n */\nfunction removeAttr(name) {\n    const attrNames = splitNames(name);\n    for (const attrName of attrNames) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (elem) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem))\n                removeAttribute(elem, attrName);\n        });\n    }\n    return this;\n}\n/**\n * Check to see if _any_ of the matched elements have the given `className`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').hasClass('pear');\n * //=> true\n *\n * $('apple').hasClass('fruit');\n * //=> false\n *\n * $('li').hasClass('pear');\n * //=> true\n * ```\n *\n * @param className - Name of the class.\n * @returns Indicates if an element has the given `className`.\n * @see {@link https://api.jquery.com/hasClass/}\n */\nfunction hasClass(className) {\n    return this.toArray().some((elem) => {\n        const clazz = (0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(elem) && elem.attribs['class'];\n        let idx = -1;\n        if (clazz && className.length > 0) {\n            while ((idx = clazz.indexOf(className, idx + 1)) > -1) {\n                const end = idx + className.length;\n                if ((idx === 0 || rspace.test(clazz[idx - 1])) &&\n                    (end === clazz.length || rspace.test(clazz[end]))) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    });\n}\n/**\n * Adds class(es) to all of the matched elements. Also accepts a `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').addClass('fruit').prop('outerHTML');\n * //=> <li class=\"pear fruit\">Pear</li>\n *\n * $('.apple').addClass('fruit red').prop('outerHTML');\n * //=> <li class=\"apple fruit red\">Apple</li>\n * ```\n *\n * @param value - Name of new class.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/addClass/}\n */\nfunction addClass(value) {\n    // Support functions\n    if (typeof value === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                const className = el.attribs['class'] || '';\n                addClass.call([el], value.call(el, i, className));\n            }\n        });\n    }\n    // Return if no value or not a string or function\n    if (!value || typeof value !== 'string')\n        return this;\n    const classNames = value.split(rspace);\n    const numElements = this.length;\n    for (let i = 0; i < numElements; i++) {\n        const el = this[i];\n        // If selected element isn't a tag, move on\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            continue;\n        // If we don't already have classes — always set xmlMode to false here, as it doesn't matter for classes\n        const className = getAttr(el, 'class', false);\n        if (className) {\n            let setClass = ` ${className} `;\n            // Check if class already exists\n            for (const cn of classNames) {\n                const appendClass = `${cn} `;\n                if (!setClass.includes(` ${appendClass}`))\n                    setClass += appendClass;\n            }\n            setAttr(el, 'class', setClass.trim());\n        }\n        else {\n            setAttr(el, 'class', classNames.join(' ').trim());\n        }\n    }\n    return this;\n}\n/**\n * Removes one or more space-separated classes from the selected elements. If no\n * `className` is defined, all classes will be removed. Also accepts a\n * `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.pear').removeClass('pear').prop('outerHTML');\n * //=> <li class=\"\">Pear</li>\n *\n * $('.apple').addClass('red').removeClass().prop('outerHTML');\n * //=> <li class=\"\">Apple</li>\n * ```\n *\n * @param name - Name of the class. If not specified, removes all elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/removeClass/}\n */\nfunction removeClass(name) {\n    // Handle if value is a function\n    if (typeof name === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                removeClass.call([el], name.call(el, i, el.attribs['class'] || ''));\n            }\n        });\n    }\n    const classes = splitNames(name);\n    const numClasses = classes.length;\n    const removeAll = arguments.length === 0;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            return;\n        if (removeAll) {\n            // Short circuit the remove all case as this is the nice one\n            el.attribs['class'] = '';\n        }\n        else {\n            const elClasses = splitNames(el.attribs['class']);\n            let changed = false;\n            for (let j = 0; j < numClasses; j++) {\n                const index = elClasses.indexOf(classes[j]);\n                if (index !== -1) {\n                    elClasses.splice(index, 1);\n                    changed = true;\n                    /*\n                     * We have to do another pass to ensure that there are not duplicate\n                     * classes listed\n                     */\n                    j--;\n                }\n            }\n            if (changed) {\n                el.attribs['class'] = elClasses.join(' ');\n            }\n        }\n    });\n}\n/**\n * Add or remove class(es) from the matched elements, depending on either the\n * class's presence or the value of the switch argument. Also accepts a\n * `function`.\n *\n * @category Attributes\n * @example\n *\n * ```js\n * $('.apple.green').toggleClass('fruit green red').prop('outerHTML');\n * //=> <li class=\"apple fruit red\">Apple</li>\n *\n * $('.apple.green').toggleClass('fruit green red', true).prop('outerHTML');\n * //=> <li class=\"apple green fruit red\">Apple</li>\n * ```\n *\n * @param value - Name of the class. Can also be a function.\n * @param stateVal - If specified the state of the class.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/toggleClass/}\n */\nfunction toggleClass(value, stateVal) {\n    // Support functions\n    if (typeof value === 'function') {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el)) {\n                toggleClass.call([el], value.call(el, i, el.attribs['class'] || '', stateVal), stateVal);\n            }\n        });\n    }\n    // Return if no value or not a string or function\n    if (!value || typeof value !== 'string')\n        return this;\n    const classNames = value.split(rspace);\n    const numClasses = classNames.length;\n    const state = typeof stateVal === 'boolean' ? (stateVal ? 1 : -1) : 0;\n    const numElements = this.length;\n    for (let i = 0; i < numElements; i++) {\n        const el = this[i];\n        // If selected element isn't a tag, move on\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_2__.isTag)(el))\n            continue;\n        const elementClasses = splitNames(el.attribs['class']);\n        // Check if class already exists\n        for (let j = 0; j < numClasses; j++) {\n            // Check if the class name is currently defined\n            const index = elementClasses.indexOf(classNames[j]);\n            // Add if stateValue === true or we are toggling and there is no value\n            if (state >= 0 && index === -1) {\n                elementClasses.push(classNames[j]);\n            }\n            else if (state <= 0 && index !== -1) {\n                // Otherwise remove but only if the item exists\n                elementClasses.splice(index, 1);\n            }\n        }\n        el.attribs['class'] = elementClasses.join(' ');\n    }\n    return this;\n}\n//# sourceMappingURL=attributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/css.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/css.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n/**\n * Set multiple CSS properties for every matched element.\n *\n * @category CSS\n * @param prop - The names of the properties.\n * @param val - The new values.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/css/}\n */\nfunction css(prop, val) {\n    if ((prop != null && val != null) ||\n        // When `prop` is a \"plain\" object\n        (typeof prop === 'object' && !Array.isArray(prop))) {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.domEach)(this, (el, i) => {\n            if ((0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag)(el)) {\n                // `prop` can't be an array here anymore.\n                setCss(el, prop, val, i);\n            }\n        });\n    }\n    if (this.length === 0) {\n        return undefined;\n    }\n    return getCss(this[0], prop);\n}\n/**\n * Set styles of all elements.\n *\n * @private\n * @param el - Element to set style of.\n * @param prop - Name of property.\n * @param value - Value to set property to.\n * @param idx - Optional index within the selection.\n */\nfunction setCss(el, prop, value, idx) {\n    if (typeof prop === 'string') {\n        const styles = getCss(el);\n        const val = typeof value === 'function' ? value.call(el, idx, styles[prop]) : value;\n        if (val === '') {\n            delete styles[prop];\n        }\n        else if (val != null) {\n            styles[prop] = val;\n        }\n        el.attribs['style'] = stringify(styles);\n    }\n    else if (typeof prop === 'object') {\n        const keys = Object.keys(prop);\n        for (let i = 0; i < keys.length; i++) {\n            const k = keys[i];\n            setCss(el, k, prop[k], i);\n        }\n    }\n}\nfunction getCss(el, prop) {\n    if (!el || !(0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isTag)(el))\n        return;\n    const styles = parse(el.attribs['style']);\n    if (typeof prop === 'string') {\n        return styles[prop];\n    }\n    if (Array.isArray(prop)) {\n        const newStyles = {};\n        for (const item of prop) {\n            if (styles[item] != null) {\n                newStyles[item] = styles[item];\n            }\n        }\n        return newStyles;\n    }\n    return styles;\n}\n/**\n * Stringify `obj` to styles.\n *\n * @private\n * @category CSS\n * @param obj - Object to stringify.\n * @returns The serialized styles.\n */\nfunction stringify(obj) {\n    return Object.keys(obj).reduce((str, prop) => `${str}${str ? ' ' : ''}${prop}: ${obj[prop]};`, '');\n}\n/**\n * Parse `styles`.\n *\n * @private\n * @category CSS\n * @param styles - Styles to be parsed.\n * @returns The parsed styles.\n */\nfunction parse(styles) {\n    styles = (styles || '').trim();\n    if (!styles)\n        return {};\n    const obj = {};\n    let key;\n    for (const str of styles.split(';')) {\n        const n = str.indexOf(':');\n        // If there is no :, or if it is the first/last character, add to the previous item's value\n        if (n < 1 || n === str.length - 1) {\n            const trimmed = str.trimEnd();\n            if (trimmed.length > 0 && key !== undefined) {\n                obj[key] += `;${trimmed}`;\n            }\n        }\n        else {\n            key = str.slice(0, n).trim();\n            obj[key] = str.slice(n + 1).trim();\n        }\n    }\n    return obj;\n}\n//# sourceMappingURL=css.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/css.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/extract.js":
/*!******************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/extract.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extract: () => (/* binding */ extract)\n/* harmony export */ });\nfunction getExtractDescr(descr) {\n    var _a;\n    if (typeof descr === 'string') {\n        return { selector: descr, value: 'textContent' };\n    }\n    return {\n        selector: descr.selector,\n        value: (_a = descr.value) !== null && _a !== void 0 ? _a : 'textContent',\n    };\n}\n/**\n * Extract multiple values from a document, and store them in an object.\n *\n * @param map - An object containing key-value pairs. The keys are the names of\n *   the properties to be created on the object, and the values are the\n *   selectors to be used to extract the values.\n * @returns An object containing the extracted values.\n */\nfunction extract(map) {\n    const ret = {};\n    for (const key in map) {\n        const descr = map[key];\n        const isArray = Array.isArray(descr);\n        const { selector, value } = getExtractDescr(isArray ? descr[0] : descr);\n        const fn = typeof value === 'function'\n            ? value\n            : typeof value === 'string'\n                ? (el) => this._make(el).prop(value)\n                : (el) => this._make(el).extract(value);\n        if (isArray) {\n            ret[key] = this._findBySelector(selector, Number.POSITIVE_INFINITY)\n                .map((_, el) => fn(el, key, ret))\n                .get();\n        }\n        else {\n            const $ = this._findBySelector(selector, 1);\n            ret[key] = $.length > 0 ? fn($[0], key, ret) : undefined;\n        }\n    }\n    return ret;\n}\n//# sourceMappingURL=extract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/extract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/forms.js":
/*!****************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/forms.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeArray: () => (/* binding */ serializeArray)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n/*\n * https://github.com/jquery/jquery/blob/2.1.3/src/manipulation/var/rcheckableType.js\n * https://github.com/jquery/jquery/blob/2.1.3/src/serialize.js\n */\nconst submittableSelector = 'input,select,textarea,keygen';\nconst r20 = /%20/g;\nconst rCRLF = /\\r?\\n/g;\n/**\n * Encode a set of form elements as a string for submission.\n *\n * @category Forms\n * @example\n *\n * ```js\n * $('<form><input name=\"foo\" value=\"bar\" /></form>').serialize();\n * //=> 'foo=bar'\n * ```\n *\n * @returns The serialized form.\n * @see {@link https://api.jquery.com/serialize/}\n */\nfunction serialize() {\n    // Convert form elements into name/value objects\n    const arr = this.serializeArray();\n    // Serialize each element into a key/value string\n    const retArr = arr.map((data) => `${encodeURIComponent(data.name)}=${encodeURIComponent(data.value)}`);\n    // Return the resulting serialization\n    return retArr.join('&').replace(r20, '+');\n}\n/**\n * Encode a set of form elements as an array of names and values.\n *\n * @category Forms\n * @example\n *\n * ```js\n * $('<form><input name=\"foo\" value=\"bar\" /></form>').serializeArray();\n * //=> [ { name: 'foo', value: 'bar' } ]\n * ```\n *\n * @returns The serialized form.\n * @see {@link https://api.jquery.com/serializeArray/}\n */\nfunction serializeArray() {\n    // Resolve all form elements from either forms or collections of form elements\n    return this.map((_, elem) => {\n        const $elem = this._make(elem);\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === 'form') {\n            return $elem.find(submittableSelector).toArray();\n        }\n        return $elem.filter(submittableSelector).toArray();\n    })\n        .filter(\n    // Verify elements have a name (`attr.name`) and are not disabled (`:enabled`)\n    '[name!=\"\"]:enabled' +\n        // And cannot be clicked (`[type=submit]`) or are used in `x-www-form-urlencoded` (`[type=file]`)\n        ':not(:submit, :button, :image, :reset, :file)' +\n        // And are either checked/don't have a checkable state\n        ':matches([checked], :not(:checkbox, :radio))')\n        .map((_, elem) => {\n        var _a;\n        const $elem = this._make(elem);\n        const name = $elem.attr('name'); // We have filtered for elements with a name before.\n        // If there is no value set (e.g. `undefined`, `null`), then default value to empty\n        const value = (_a = $elem.val()) !== null && _a !== void 0 ? _a : '';\n        // If we have an array of values (e.g. `<select multiple>`), return an array of key/value pairs\n        if (Array.isArray(value)) {\n            return value.map((val) => \n            /*\n             * We trim replace any line endings (e.g. `\\r` or `\\r\\n` with `\\r\\n`) to guarantee consistency across platforms\n             * These can occur inside of `<textarea>'s`\n             */\n            ({ name, value: val.replace(rCRLF, '\\r\\n') }));\n        }\n        // Otherwise (e.g. `<input type=\"text\">`, return only one key/value pair\n        return { name, value: value.replace(rCRLF, '\\r\\n') };\n    })\n        .toArray();\n}\n//# sourceMappingURL=forms.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/forms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js":
/*!***********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/manipulation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _makeDomArray: () => (/* binding */ _makeDomArray),\n/* harmony export */   after: () => (/* binding */ after),\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   before: () => (/* binding */ before),\n/* harmony export */   clone: () => (/* binding */ clone),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   insertAfter: () => (/* binding */ insertAfter),\n/* harmony export */   insertBefore: () => (/* binding */ insertBefore),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependTo: () => (/* binding */ prependTo),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   replaceWith: () => (/* binding */ replaceWith),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap),\n/* harmony export */   wrapAll: () => (/* binding */ wrapAll),\n/* harmony export */   wrapInner: () => (/* binding */ wrapInner)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parse.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/**\n * Methods for modifying the DOM structure.\n *\n * @module cheerio/manipulation\n */\n\n\n\n\n\n\n/**\n * Create an array of nodes, recursing into arrays and parsing strings if\n * necessary.\n *\n * @private\n * @category Manipulation\n * @param elem - Elements to make an array of.\n * @param clone - Optionally clone nodes.\n * @returns The array of nodes.\n */\nfunction _makeDomArray(elem, clone) {\n    if (elem == null) {\n        return [];\n    }\n    if (typeof elem === 'string') {\n        return this._parse(elem, this.options, false, null).children.slice(0);\n    }\n    if ('length' in elem) {\n        if (elem.length === 1) {\n            return this._makeDomArray(elem[0], clone);\n        }\n        const result = [];\n        for (let i = 0; i < elem.length; i++) {\n            const el = elem[i];\n            if (typeof el === 'object') {\n                if (el == null) {\n                    continue;\n                }\n                if (!('length' in el)) {\n                    result.push(clone ? (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(el, true) : el);\n                    continue;\n                }\n            }\n            result.push(...this._makeDomArray(el, clone));\n        }\n        return result;\n    }\n    return [clone ? (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(elem, true) : elem];\n}\nfunction _insert(concatenator) {\n    return function (...elems) {\n        const lastIdx = this.length - 1;\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n            if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n                return;\n            const domSrc = typeof elems[0] === 'function'\n                ? elems[0].call(el, i, this._render(el.children))\n                : elems;\n            const dom = this._makeDomArray(domSrc, i < lastIdx);\n            concatenator(dom, el.children, el);\n        });\n    };\n}\n/**\n * Modify an array in-place, removing some number of elements and adding new\n * elements directly following them.\n *\n * @private\n * @category Manipulation\n * @param array - Target array to splice.\n * @param spliceIdx - Index at which to begin changing the array.\n * @param spliceCount - Number of elements to remove from the array.\n * @param newElems - Elements to insert into the array.\n * @param parent - The parent of the node.\n * @returns The spliced array.\n */\nfunction uniqueSplice(array, spliceIdx, spliceCount, newElems, parent) {\n    var _a, _b;\n    const spliceArgs = [\n        spliceIdx,\n        spliceCount,\n        ...newElems,\n    ];\n    const prev = spliceIdx === 0 ? null : array[spliceIdx - 1];\n    const next = spliceIdx + spliceCount >= array.length\n        ? null\n        : array[spliceIdx + spliceCount];\n    /*\n     * Before splicing in new elements, ensure they do not already appear in the\n     * current array.\n     */\n    for (let idx = 0; idx < newElems.length; ++idx) {\n        const node = newElems[idx];\n        const oldParent = node.parent;\n        if (oldParent) {\n            const oldSiblings = oldParent.children;\n            const prevIdx = oldSiblings.indexOf(node);\n            if (prevIdx !== -1) {\n                oldParent.children.splice(prevIdx, 1);\n                if (parent === oldParent && spliceIdx > prevIdx) {\n                    spliceArgs[0]--;\n                }\n            }\n        }\n        node.parent = parent;\n        if (node.prev) {\n            node.prev.next = (_a = node.next) !== null && _a !== void 0 ? _a : null;\n        }\n        if (node.next) {\n            node.next.prev = (_b = node.prev) !== null && _b !== void 0 ? _b : null;\n        }\n        node.prev = idx === 0 ? prev : newElems[idx - 1];\n        node.next = idx === newElems.length - 1 ? next : newElems[idx + 1];\n    }\n    if (prev) {\n        prev.next = newElems[0];\n    }\n    if (next) {\n        next.prev = newElems[newElems.length - 1];\n    }\n    return array.splice(...spliceArgs);\n}\n/**\n * Insert every element in the set of matched elements to the end of the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').appendTo('#fruits');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //      <li class=\"plum\">Plum</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to append elements to.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/appendTo/}\n */\nfunction appendTo(target) {\n    const appendTarget = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(target) ? target : this._make(target);\n    appendTarget.append(this);\n    return this;\n}\n/**\n * Insert every element in the set of matched elements to the beginning of the\n * target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').prependTo('#fruits');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to prepend elements to.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/prependTo/}\n */\nfunction prependTo(target) {\n    const prependTarget = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(target) ? target : this._make(target);\n    prependTarget.prepend(this);\n    return this;\n}\n/**\n * Inserts content as the _last_ child of each of the selected elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').append('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //      <li class=\"plum\">Plum</li>\n * //    </ul>\n * ```\n *\n * @see {@link https://api.jquery.com/append/}\n */\nconst append = _insert((dom, children, parent) => {\n    uniqueSplice(children, children.length, 0, dom, parent);\n});\n/**\n * Inserts content as the _first_ child of each of the selected elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').prepend('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @see {@link https://api.jquery.com/prepend/}\n */\nconst prepend = _insert((dom, children, parent) => {\n    uniqueSplice(children, 0, 0, dom, parent);\n});\nfunction _wrap(insert) {\n    return function (wrapper) {\n        const lastIdx = this.length - 1;\n        const lastParent = this.parents().last();\n        for (let i = 0; i < this.length; i++) {\n            const el = this[i];\n            const wrap = typeof wrapper === 'function'\n                ? wrapper.call(el, i, el)\n                : typeof wrapper === 'string' && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(wrapper)\n                    ? lastParent.find(wrapper).clone()\n                    : wrapper;\n            const [wrapperDom] = this._makeDomArray(wrap, i < lastIdx);\n            if (!wrapperDom || !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(wrapperDom))\n                continue;\n            let elInsertLocation = wrapperDom;\n            /*\n             * Find the deepest child. Only consider the first tag child of each node\n             * (ignore text); stop if no children are found.\n             */\n            let j = 0;\n            while (j < elInsertLocation.children.length) {\n                const child = elInsertLocation.children[j];\n                if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(child)) {\n                    elInsertLocation = child;\n                    j = 0;\n                }\n                else {\n                    j++;\n                }\n            }\n            insert(el, elInsertLocation, [wrapperDom]);\n        }\n        return this;\n    };\n}\n/**\n * The .wrap() function can take any string or object that could be passed to\n * the $() factory function to specify a DOM structure. This structure may be\n * nested several levels deep, but should contain only one inmost element. A\n * copy of this structure will be wrapped around each of the elements in the set\n * of matched elements. This method returns the original set of elements for\n * chaining purposes.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const redFruit = $('<div class=\"red-fruit\"></div>');\n * $('.apple').wrap(redFruit);\n *\n * //=> <ul id=\"fruits\">\n * //     <div class=\"red-fruit\">\n * //      <li class=\"apple\">Apple</li>\n * //     </div>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"plum\">Plum</li>\n * //   </ul>\n *\n * const healthy = $('<div class=\"healthy\"></div>');\n * $('li').wrap(healthy);\n *\n * //=> <ul id=\"fruits\">\n * //     <div class=\"healthy\">\n * //       <li class=\"apple\">Apple</li>\n * //     </div>\n * //     <div class=\"healthy\">\n * //       <li class=\"orange\">Orange</li>\n * //     </div>\n * //     <div class=\"healthy\">\n * //        <li class=\"plum\">Plum</li>\n * //     </div>\n * //   </ul>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around each element in the\n *   selection.\n * @see {@link https://api.jquery.com/wrap/}\n */\nconst wrap = _wrap((el, elInsertLocation, wrapperDom) => {\n    const { parent } = el;\n    if (!parent)\n        return;\n    const siblings = parent.children;\n    const index = siblings.indexOf(el);\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)([el], elInsertLocation);\n    /*\n     * The previous operation removed the current element from the `siblings`\n     * array, so the `dom` array can be inserted without removing any\n     * additional elements.\n     */\n    uniqueSplice(siblings, index, 0, wrapperDom, parent);\n});\n/**\n * The .wrapInner() function can take any string or object that could be passed\n * to the $() factory function to specify a DOM structure. This structure may be\n * nested several levels deep, but should contain only one inmost element. The\n * structure will be wrapped around the content of each of the elements in the\n * set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const redFruit = $('<div class=\"red-fruit\"></div>');\n * $('.apple').wrapInner(redFruit);\n *\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">\n * //       <div class=\"red-fruit\">Apple</div>\n * //     </li>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"pear\">Pear</li>\n * //   </ul>\n *\n * const healthy = $('<div class=\"healthy\"></div>');\n * $('li').wrapInner(healthy);\n *\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">\n * //       <div class=\"healthy\">Apple</div>\n * //     </li>\n * //     <li class=\"orange\">\n * //       <div class=\"healthy\">Orange</div>\n * //     </li>\n * //     <li class=\"pear\">\n * //       <div class=\"healthy\">Pear</div>\n * //     </li>\n * //   </ul>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around the content of each element\n *   in the selection.\n * @returns The instance itself, for chaining.\n * @see {@link https://api.jquery.com/wrapInner/}\n */\nconst wrapInner = _wrap((el, elInsertLocation, wrapperDom) => {\n    if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n        return;\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(el.children, elInsertLocation);\n    (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(wrapperDom, el);\n});\n/**\n * The .unwrap() function, removes the parents of the set of matched elements\n * from the DOM, leaving the matched elements in their place.\n *\n * @category Manipulation\n * @example <caption>without selector</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div id=test>\\n  <div><p>Hello</p></div>\\n  <div><p>World</p></div>\\n</div>',\n * );\n * $('#test p').unwrap();\n *\n * //=> <div id=test>\n * //     <p>Hello</p>\n * //     <p>World</p>\n * //   </div>\n * ```\n *\n * @example <caption>with selector</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div id=test>\\n  <p>Hello</p>\\n  <b><p>World</p></b>\\n</div>',\n * );\n * $('#test p').unwrap('b');\n *\n * //=> <div id=test>\n * //     <p>Hello</p>\n * //     <p>World</p>\n * //   </div>\n * ```\n *\n * @param selector - A selector to check the parent element against. If an\n *   element's parent does not match the selector, the element won't be\n *   unwrapped.\n * @returns The instance itself, for chaining.\n * @see {@link https://api.jquery.com/unwrap/}\n */\nfunction unwrap(selector) {\n    this.parent(selector)\n        .not('body')\n        .each((_, el) => {\n        this._make(el).replaceWith(el.children);\n    });\n    return this;\n}\n/**\n * The .wrapAll() function can take any string or object that could be passed to\n * the $() function to specify a DOM structure. This structure may be nested\n * several levels deep, but should contain only one inmost element. The\n * structure will be wrapped around all of the elements in the set of matched\n * elements, as a single group.\n *\n * @category Manipulation\n * @example <caption>With markup passed to `wrapAll`</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<div class=\"container\"><div class=\"inner\">First</div><div class=\"inner\">Second</div></div>',\n * );\n * $('.inner').wrapAll(\"<div class='new'></div>\");\n *\n * //=> <div class=\"container\">\n * //     <div class='new'>\n * //       <div class=\"inner\">First</div>\n * //       <div class=\"inner\">Second</div>\n * //     </div>\n * //   </div>\n * ```\n *\n * @example <caption>With an existing cheerio instance</caption>\n *\n * ```js\n * const $ = cheerio.load(\n *   '<span>Span 1</span><strong>Strong</strong><span>Span 2</span>',\n * );\n * const wrap = $('<div><p><em><b></b></em></p></div>');\n * $('span').wrapAll(wrap);\n *\n * //=> <div>\n * //     <p>\n * //       <em>\n * //         <b>\n * //           <span>Span 1</span>\n * //           <span>Span 2</span>\n * //         </b>\n * //       </em>\n * //     </p>\n * //   </div>\n * //   <strong>Strong</strong>\n * ```\n *\n * @param wrapper - The DOM structure to wrap around all matched elements in the\n *   selection.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/wrapAll/}\n */\nfunction wrapAll(wrapper) {\n    const el = this[0];\n    if (el) {\n        const wrap = this._make(typeof wrapper === 'function' ? wrapper.call(el, 0, el) : wrapper).insertBefore(el);\n        // If html is given as wrapper, wrap may contain text elements\n        let elInsertLocation;\n        for (let i = 0; i < wrap.length; i++) {\n            if (wrap[i].type === htmlparser2__WEBPACK_IMPORTED_MODULE_5__.ElementType.Tag) {\n                elInsertLocation = wrap[i];\n            }\n        }\n        let j = 0;\n        /*\n         * Find the deepest child. Only consider the first tag child of each node\n         * (ignore text); stop if no children are found.\n         */\n        while (elInsertLocation && j < elInsertLocation.children.length) {\n            const child = elInsertLocation.children[j];\n            if (child.type === htmlparser2__WEBPACK_IMPORTED_MODULE_5__.ElementType.Tag) {\n                elInsertLocation = child;\n                j = 0;\n            }\n            else {\n                j++;\n            }\n        }\n        if (elInsertLocation)\n            this._make(elInsertLocation).append(this);\n    }\n    return this;\n}\n/**\n * Insert content next to each element in the set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.apple').after('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param elems - HTML string, DOM element, array of DOM elements or Cheerio to\n *   insert after each element in the set of matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/after/}\n */\nfunction after(...elems) {\n    const lastIdx = this.length - 1;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el) || !el.parent) {\n            return;\n        }\n        const siblings = el.parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        const domSrc = typeof elems[0] === 'function'\n            ? elems[0].call(el, i, this._render(el.children))\n            : elems;\n        const dom = this._makeDomArray(domSrc, i < lastIdx);\n        // Add element after `this` element\n        uniqueSplice(siblings, index + 1, 0, dom, el.parent);\n    });\n}\n/**\n * Insert every element in the set of matched elements after the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').insertAfter('.apple');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to insert elements after.\n * @returns The set of newly inserted elements.\n * @see {@link https://api.jquery.com/insertAfter/}\n */\nfunction insertAfter(target) {\n    if (typeof target === 'string') {\n        target = this._make(target);\n    }\n    this.remove();\n    const clones = [];\n    for (const el of this._makeDomArray(target)) {\n        const clonedSelf = this.clone().toArray();\n        const { parent } = el;\n        if (!parent) {\n            continue;\n        }\n        const siblings = parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            continue;\n        // Add cloned `this` element(s) after target element\n        uniqueSplice(siblings, index + 1, 0, clonedSelf, parent);\n        clones.push(...clonedSelf);\n    }\n    return this._make(clones);\n}\n/**\n * Insert content previous to each element in the set of matched elements.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.apple').before('<li class=\"plum\">Plum</li>');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param elems - HTML string, DOM element, array of DOM elements or Cheerio to\n *   insert before each element in the set of matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/before/}\n */\nfunction before(...elems) {\n    const lastIdx = this.length - 1;\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el) || !el.parent) {\n            return;\n        }\n        const siblings = el.parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        const domSrc = typeof elems[0] === 'function'\n            ? elems[0].call(el, i, this._render(el.children))\n            : elems;\n        const dom = this._makeDomArray(domSrc, i < lastIdx);\n        // Add element before `el` element\n        uniqueSplice(siblings, index, 0, dom, el.parent);\n    });\n}\n/**\n * Insert every element in the set of matched elements before the target.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('<li class=\"plum\">Plum</li>').insertBefore('.apple');\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"plum\">Plum</li>\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //      <li class=\"pear\">Pear</li>\n * //    </ul>\n * ```\n *\n * @param target - Element to insert elements before.\n * @returns The set of newly inserted elements.\n * @see {@link https://api.jquery.com/insertBefore/}\n */\nfunction insertBefore(target) {\n    const targetArr = this._make(target);\n    this.remove();\n    const clones = [];\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(targetArr, (el) => {\n        const clonedSelf = this.clone().toArray();\n        const { parent } = el;\n        if (!parent) {\n            return;\n        }\n        const siblings = parent.children;\n        const index = siblings.indexOf(el);\n        // If not found, move on\n        /* istanbul ignore next */\n        if (index === -1)\n            return;\n        // Add cloned `this` element(s) after target element\n        uniqueSplice(siblings, index, 0, clonedSelf, parent);\n        clones.push(...clonedSelf);\n    });\n    return this._make(clones);\n}\n/**\n * Removes the set of matched elements from the DOM and all their children.\n * `selector` filters the set of matched elements to be removed.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('.pear').remove();\n * $.html();\n * //=>  <ul id=\"fruits\">\n * //      <li class=\"apple\">Apple</li>\n * //      <li class=\"orange\">Orange</li>\n * //    </ul>\n * ```\n *\n * @param selector - Optional selector for elements to remove.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/remove/}\n */\nfunction remove(selector) {\n    // Filter if we have selector\n    const elems = selector ? this.filter(selector) : this;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(elems, (el) => {\n        (0,domutils__WEBPACK_IMPORTED_MODULE_4__.removeElement)(el);\n        el.prev = el.next = el.parent = null;\n    });\n    return this;\n}\n/**\n * Replaces matched elements with `content`.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const plum = $('<li class=\"plum\">Plum</li>');\n * $('.pear').replaceWith(plum);\n * $.html();\n * //=> <ul id=\"fruits\">\n * //     <li class=\"apple\">Apple</li>\n * //     <li class=\"orange\">Orange</li>\n * //     <li class=\"plum\">Plum</li>\n * //   </ul>\n * ```\n *\n * @param content - Replacement for matched elements.\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/replaceWith/}\n */\nfunction replaceWith(content) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => {\n        const { parent } = el;\n        if (!parent) {\n            return;\n        }\n        const siblings = parent.children;\n        const cont = typeof content === 'function' ? content.call(el, i, el) : content;\n        const dom = this._makeDomArray(cont);\n        /*\n         * In the case that `dom` contains nodes that already exist in other\n         * structures, ensure those nodes are properly removed.\n         */\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(dom, null);\n        const index = siblings.indexOf(el);\n        // Completely remove old element\n        uniqueSplice(siblings, index, 1, dom, parent);\n        if (!dom.includes(el)) {\n            el.parent = el.prev = el.next = null;\n        }\n    });\n}\n/**\n * Removes all children from each item in the selection. Text nodes and comment\n * nodes are left as is.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * $('ul').empty();\n * $.html();\n * //=>  <ul id=\"fruits\"></ul>\n * ```\n *\n * @returns The instance itself.\n * @see {@link https://api.jquery.com/empty/}\n */\nfunction empty() {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        el.children.length = 0;\n    });\n}\nfunction html(str) {\n    if (str === undefined) {\n        const el = this[0];\n        if (!el || !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return null;\n        return this._render(el.children);\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        const content = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(str)\n            ? str.toArray()\n            : this._parse(`${str}`, this.options, false, el).children;\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(content, el);\n    });\n}\n/**\n * Turns the collection to a string. Alias for `.html()`.\n *\n * @category Manipulation\n * @returns The rendered document.\n */\nfunction toString() {\n    return this._render(this);\n}\nfunction text(str) {\n    // If `str` is undefined, act as a \"getter\"\n    if (str === undefined) {\n        return (0,_static_js__WEBPACK_IMPORTED_MODULE_2__.text)(this);\n    }\n    if (typeof str === 'function') {\n        // Function support\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el, i) => this._make(el).text(str.call(el, i, (0,_static_js__WEBPACK_IMPORTED_MODULE_2__.text)([el]))));\n    }\n    // Append text node to each selected elements\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.domEach)(this, (el) => {\n        if (!(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(el))\n            return;\n        for (const child of el.children) {\n            child.next = child.prev = child.parent = null;\n        }\n        const textNode = new domhandler__WEBPACK_IMPORTED_MODULE_0__.Text(`${str}`);\n        (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.update)(textNode, el);\n    });\n}\n/**\n * Clone the cheerio object.\n *\n * @category Manipulation\n * @example\n *\n * ```js\n * const moreFruit = $('#fruits').clone();\n * ```\n *\n * @returns The cloned object.\n * @see {@link https://api.jquery.com/clone/}\n */\nfunction clone() {\n    const clone = Array.prototype.map.call(this.get(), (el) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(el, true));\n    // Add a root node around the cloned nodes\n    const root = new domhandler__WEBPACK_IMPORTED_MODULE_0__.Document(clone);\n    for (const node of clone) {\n        node.parent = root;\n    }\n    return this._make(clone);\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js":
/*!*********************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/api/traversing.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _findBySelector: () => (/* binding */ _findBySelector),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   addBack: () => (/* binding */ addBack),\n/* harmony export */   children: () => (/* binding */ children),\n/* harmony export */   closest: () => (/* binding */ closest),\n/* harmony export */   contents: () => (/* binding */ contents),\n/* harmony export */   each: () => (/* binding */ each),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   eq: () => (/* binding */ eq),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   filterArray: () => (/* binding */ filterArray),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   first: () => (/* binding */ first),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   has: () => (/* binding */ has),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   last: () => (/* binding */ last),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   next: () => (/* binding */ next),\n/* harmony export */   nextAll: () => (/* binding */ nextAll),\n/* harmony export */   nextUntil: () => (/* binding */ nextUntil),\n/* harmony export */   not: () => (/* binding */ not),\n/* harmony export */   parent: () => (/* binding */ parent),\n/* harmony export */   parents: () => (/* binding */ parents),\n/* harmony export */   parentsUntil: () => (/* binding */ parentsUntil),\n/* harmony export */   prev: () => (/* binding */ prev),\n/* harmony export */   prevAll: () => (/* binding */ prevAll),\n/* harmony export */   prevUntil: () => (/* binding */ prevUntil),\n/* harmony export */   siblings: () => (/* binding */ siblings),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var cheerio_select__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cheerio-select */ \"(rsc)/./node_modules/cheerio-select/lib/esm/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/**\n * Methods for traversing the DOM structure.\n *\n * @module cheerio/traversing\n */\n\n\n\n\n\nconst reSiblingSelector = /^\\s*[+~]/;\n/**\n * Get the descendants of each element in the current set of matched elements,\n * filtered by a selector, jQuery object, or element.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').find('li').length;\n * //=> 3\n * $('#fruits').find($('.apple')).length;\n * //=> 1\n * ```\n *\n * @param selectorOrHaystack - Element to look for.\n * @returns The found elements.\n * @see {@link https://api.jquery.com/find/}\n */\nfunction find(selectorOrHaystack) {\n    if (!selectorOrHaystack) {\n        return this._make([]);\n    }\n    if (typeof selectorOrHaystack !== 'string') {\n        const haystack = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(selectorOrHaystack)\n            ? selectorOrHaystack.toArray()\n            : [selectorOrHaystack];\n        const context = this.toArray();\n        return this._make(haystack.filter((elem) => context.some((node) => (0,_static_js__WEBPACK_IMPORTED_MODULE_3__.contains)(node, elem))));\n    }\n    return this._findBySelector(selectorOrHaystack, Number.POSITIVE_INFINITY);\n}\n/**\n * Find elements by a specific selector.\n *\n * @private\n * @category Traversing\n * @param selector - Selector to filter by.\n * @param limit - Maximum number of elements to match.\n * @returns The found elements.\n */\nfunction _findBySelector(selector, limit) {\n    var _a;\n    const context = this.toArray();\n    const elems = reSiblingSelector.test(selector)\n        ? context\n        : this.children().toArray();\n    const options = {\n        context,\n        root: (_a = this._root) === null || _a === void 0 ? void 0 : _a[0],\n        // Pass options that are recognized by `cheerio-select`\n        xmlMode: this.options.xmlMode,\n        lowerCaseTags: this.options.lowerCaseTags,\n        lowerCaseAttributeNames: this.options.lowerCaseAttributeNames,\n        pseudos: this.options.pseudos,\n        quirksMode: this.options.quirksMode,\n    };\n    return this._make(cheerio_select__WEBPACK_IMPORTED_MODULE_1__.select(selector, elems, options, limit));\n}\n/**\n * Creates a matcher, using a particular mapping function. Matchers provide a\n * function that finds elements using a generating function, supporting\n * filtering.\n *\n * @private\n * @param matchMap - Mapping function.\n * @returns - Function for wrapping generating functions.\n */\nfunction _getMatcher(matchMap) {\n    return function (fn, ...postFns) {\n        return function (selector) {\n            var _a;\n            let matched = matchMap(fn, this);\n            if (selector) {\n                matched = filterArray(matched, selector, this.options.xmlMode, (_a = this._root) === null || _a === void 0 ? void 0 : _a[0]);\n            }\n            return this._make(\n            // Post processing is only necessary if there is more than one element.\n            this.length > 1 && matched.length > 1\n                ? postFns.reduce((elems, fn) => fn(elems), matched)\n                : matched);\n        };\n    };\n}\n/** Matcher that adds multiple elements for each entry in the input. */\nconst _matcher = _getMatcher((fn, elems) => {\n    let ret = [];\n    for (let i = 0; i < elems.length; i++) {\n        const value = fn(elems[i]);\n        if (value.length > 0)\n            ret = ret.concat(value);\n    }\n    return ret;\n});\n/** Matcher that adds at most one element for each entry in the input. */\nconst _singleMatcher = _getMatcher((fn, elems) => {\n    const ret = [];\n    for (let i = 0; i < elems.length; i++) {\n        const value = fn(elems[i]);\n        if (value !== null) {\n            ret.push(value);\n        }\n    }\n    return ret;\n});\n/**\n * Matcher that supports traversing until a condition is met.\n *\n * @param nextElem - Function that returns the next element.\n * @param postFns - Post processing functions.\n * @returns A function usable for `*Until` methods.\n */\nfunction _matchUntil(nextElem, ...postFns) {\n    // We use a variable here that is used from within the matcher.\n    let matches = null;\n    const innerMatcher = _getMatcher((nextElem, elems) => {\n        const matched = [];\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.domEach)(elems, (elem) => {\n            for (let next; (next = nextElem(elem)); elem = next) {\n                // FIXME: `matched` might contain duplicates here and the index is too large.\n                if (matches === null || matches === void 0 ? void 0 : matches(next, matched.length))\n                    break;\n                matched.push(next);\n            }\n        });\n        return matched;\n    })(nextElem, ...postFns);\n    return function (selector, filterSelector) {\n        // Override `matches` variable with the new target.\n        matches =\n            typeof selector === 'string'\n                ? (elem) => cheerio_select__WEBPACK_IMPORTED_MODULE_1__.is(elem, selector, this.options)\n                : selector\n                    ? getFilterFn(selector)\n                    : null;\n        const ret = innerMatcher.call(this, filterSelector);\n        // Set `matches` to `null`, so we don't waste memory.\n        matches = null;\n        return ret;\n    };\n}\nfunction _removeDuplicates(elems) {\n    return elems.length > 1 ? Array.from(new Set(elems)) : elems;\n}\n/**\n * Get the parent of each element in the current set of matched elements,\n * optionally filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').parent().attr('id');\n * //=> fruits\n * ```\n *\n * @param selector - If specified filter for parent.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parent/}\n */\nconst parent = _singleMatcher(({ parent }) => (parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(parent) ? parent : null), _removeDuplicates);\n/**\n * Get a set of parents filtered by `selector` of each element in the current\n * set of match elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').parents().length;\n * //=> 2\n * $('.orange').parents('#fruits').length;\n * //=> 1\n * ```\n *\n * @param selector - If specified filter for parents.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parents/}\n */\nconst parents = _matcher((elem) => {\n    const matched = [];\n    while (elem.parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(elem.parent)) {\n        matched.push(elem.parent);\n        elem = elem.parent;\n    }\n    return matched;\n}, domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort, (elems) => elems.reverse());\n/**\n * Get the ancestors of each element in the current set of matched elements, up\n * to but not including the element matched by the selector, DOM node, or\n * cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').parentsUntil('#food').length;\n * //=> 1\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - Optional filter for parents.\n * @returns The parents.\n * @see {@link https://api.jquery.com/parentsUntil/}\n */\nconst parentsUntil = _matchUntil(({ parent }) => (parent && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(parent) ? parent : null), domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort, (elems) => elems.reverse());\n/**\n * For each element in the set, get the first element that matches the selector\n * by testing the element itself and traversing up through its ancestors in the\n * DOM tree.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').closest();\n * //=> []\n *\n * $('.orange').closest('.apple');\n * // => []\n *\n * $('.orange').closest('li');\n * //=> [<li class=\"orange\">Orange</li>]\n *\n * $('.orange').closest('#fruits');\n * //=> [<ul id=\"fruits\"> ... </ul>]\n * ```\n *\n * @param selector - Selector for the element to find.\n * @returns The closest nodes.\n * @see {@link https://api.jquery.com/closest/}\n */\nfunction closest(selector) {\n    var _a;\n    const set = [];\n    if (!selector) {\n        return this._make(set);\n    }\n    const selectOpts = {\n        xmlMode: this.options.xmlMode,\n        root: (_a = this._root) === null || _a === void 0 ? void 0 : _a[0],\n    };\n    const selectFn = typeof selector === 'string'\n        ? (elem) => cheerio_select__WEBPACK_IMPORTED_MODULE_1__.is(elem, selector, selectOpts)\n        : getFilterFn(selector);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.domEach)(this, (elem) => {\n        if (elem && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(elem) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem)) {\n            elem = elem.parent;\n        }\n        while (elem && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem)) {\n            if (selectFn(elem, 0)) {\n                // Do not add duplicate elements to the set\n                if (!set.includes(elem)) {\n                    set.push(elem);\n                }\n                break;\n            }\n            elem = elem.parent;\n        }\n    });\n    return this._make(set);\n}\n/**\n * Gets the next sibling of each selected element, optionally filtered by a\n * selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').next().hasClass('orange');\n * //=> true\n * ```\n *\n * @param selector - If specified filter for sibling.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/next/}\n */\nconst next = _singleMatcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.nextElementSibling)(elem));\n/**\n * Gets all the following siblings of the each selected element, optionally\n * filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').nextAll();\n * //=> [<li class=\"orange\">Orange</li>, <li class=\"pear\">Pear</li>]\n * $('.apple').nextAll('.orange');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/nextAll/}\n */\nconst nextAll = _matcher((elem) => {\n    const matched = [];\n    while (elem.next) {\n        elem = elem.next;\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem))\n            matched.push(elem);\n    }\n    return matched;\n}, _removeDuplicates);\n/**\n * Gets all the following siblings up to but not including the element matched\n * by the selector, optionally filtered by another selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').nextUntil('.pear');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - If specified filter for siblings.\n * @returns The next nodes.\n * @see {@link https://api.jquery.com/nextUntil/}\n */\nconst nextUntil = _matchUntil((el) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.nextElementSibling)(el), _removeDuplicates);\n/**\n * Gets the previous sibling of each selected element optionally filtered by a\n * selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.orange').prev().hasClass('apple');\n * //=> true\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prev/}\n */\nconst prev = _singleMatcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.prevElementSibling)(elem));\n/**\n * Gets all the preceding siblings of each selected element, optionally filtered\n * by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').prevAll();\n * //=> [<li class=\"orange\">Orange</li>, <li class=\"apple\">Apple</li>]\n *\n * $('.pear').prevAll('.orange');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prevAll/}\n */\nconst prevAll = _matcher((elem) => {\n    const matched = [];\n    while (elem.prev) {\n        elem = elem.prev;\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem))\n            matched.push(elem);\n    }\n    return matched;\n}, _removeDuplicates);\n/**\n * Gets all the preceding siblings up to but not including the element matched\n * by the selector, optionally filtered by another selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').prevUntil('.apple');\n * //=> [<li class=\"orange\">Orange</li>]\n * ```\n *\n * @param selector - Selector for element to stop at.\n * @param filterSelector - If specified filter for siblings.\n * @returns The previous nodes.\n * @see {@link https://api.jquery.com/prevUntil/}\n */\nconst prevUntil = _matchUntil((el) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.prevElementSibling)(el), _removeDuplicates);\n/**\n * Get the siblings of each element (excluding the element) in the set of\n * matched elements, optionally filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').siblings().length;\n * //=> 2\n *\n * $('.pear').siblings('.orange').length;\n * //=> 1\n * ```\n *\n * @param selector - If specified filter for siblings.\n * @returns The siblings.\n * @see {@link https://api.jquery.com/siblings/}\n */\nconst siblings = _matcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getSiblings)(elem).filter((el) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(el) && el !== elem), domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort);\n/**\n * Gets the element children of each element in the set of matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().length;\n * //=> 3\n *\n * $('#fruits').children('.pear').text();\n * //=> Pear\n * ```\n *\n * @param selector - If specified filter for children.\n * @returns The children.\n * @see {@link https://api.jquery.com/children/}\n */\nconst children = _matcher((elem) => (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getChildren)(elem).filter(domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag), _removeDuplicates);\n/**\n * Gets the children of each element in the set of matched elements, including\n * text and comment nodes.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').contents().length;\n * //=> 3\n * ```\n *\n * @returns The children.\n * @see {@link https://api.jquery.com/contents/}\n */\nfunction contents() {\n    const elems = this.toArray().reduce((newElems, elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? newElems.concat(elem.children) : newElems, []);\n    return this._make(elems);\n}\n/**\n * Iterates over a cheerio object, executing a function for each matched\n * element. When the callback is fired, the function is fired in the context of\n * the DOM element, so `this` refers to the current element, which is equivalent\n * to the function parameter `element`. To break out of the `each` loop early,\n * return with `false`.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * const fruits = [];\n *\n * $('li').each(function (i, elem) {\n *   fruits[i] = $(this).text();\n * });\n *\n * fruits.join(', ');\n * //=> Apple, Orange, Pear\n * ```\n *\n * @param fn - Function to execute.\n * @returns The instance itself, useful for chaining.\n * @see {@link https://api.jquery.com/each/}\n */\nfunction each(fn) {\n    let i = 0;\n    const len = this.length;\n    while (i < len && fn.call(this[i], i, this[i]) !== false)\n        ++i;\n    return this;\n}\n/**\n * Pass each element in the current matched set through a function, producing a\n * new Cheerio object containing the return values. The function can return an\n * individual data item or an array of data items to be inserted into the\n * resulting set. If an array is returned, the elements inside the array are\n * inserted into the set. If the function returns null or undefined, no element\n * will be inserted.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li')\n *   .map(function (i, el) {\n *     // this === el\n *     return $(this).text();\n *   })\n *   .toArray()\n *   .join(' ');\n * //=> \"apple orange pear\"\n * ```\n *\n * @param fn - Function to execute.\n * @returns The mapped elements, wrapped in a Cheerio collection.\n * @see {@link https://api.jquery.com/map/}\n */\nfunction map(fn) {\n    let elems = [];\n    for (let i = 0; i < this.length; i++) {\n        const el = this[i];\n        const val = fn.call(el, i, el);\n        if (val != null) {\n            elems = elems.concat(val);\n        }\n    }\n    return this._make(elems);\n}\n/**\n * Creates a function to test if a filter is matched.\n *\n * @param match - A filter.\n * @returns A function that determines if a filter has been matched.\n */\nfunction getFilterFn(match) {\n    if (typeof match === 'function') {\n        return (el, i) => match.call(el, i, el);\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(match)) {\n        return (el) => Array.prototype.includes.call(match, el);\n    }\n    return function (el) {\n        return match === el;\n    };\n}\nfunction filter(match) {\n    var _a;\n    return this._make(filterArray(this.toArray(), match, this.options.xmlMode, (_a = this._root) === null || _a === void 0 ? void 0 : _a[0]));\n}\nfunction filterArray(nodes, match, xmlMode, root) {\n    return typeof match === 'string'\n        ? cheerio_select__WEBPACK_IMPORTED_MODULE_1__.filter(match, nodes, { xmlMode, root })\n        : nodes.filter(getFilterFn(match));\n}\n/**\n * Checks the current list of elements and returns `true` if _any_ of the\n * elements match the selector. If using an element or Cheerio selection,\n * returns `true` if _any_ of the elements match. If using a predicate function,\n * the function is executed in the context of the selected element, so `this`\n * refers to the current element.\n *\n * @category Traversing\n * @param selector - Selector for the selection.\n * @returns Whether or not the selector matches an element of the instance.\n * @see {@link https://api.jquery.com/is/}\n */\nfunction is(selector) {\n    const nodes = this.toArray();\n    return typeof selector === 'string'\n        ? cheerio_select__WEBPACK_IMPORTED_MODULE_1__.some(nodes.filter(domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag), selector, this.options)\n        : selector\n            ? nodes.some(getFilterFn(selector))\n            : false;\n}\n/**\n * Remove elements from the set of matched elements. Given a Cheerio object that\n * represents a set of DOM elements, the `.not()` method constructs a new\n * Cheerio object from a subset of the matching elements. The supplied selector\n * is tested against each element; the elements that don't match the selector\n * will be included in the result.\n *\n * The `.not()` method can take a function as its argument in the same way that\n * `.filter()` does. Elements for which the function returns `true` are excluded\n * from the filtered set; all other elements are included.\n *\n * @category Traversing\n * @example <caption>Selector</caption>\n *\n * ```js\n * $('li').not('.apple').length;\n * //=> 2\n * ```\n *\n * @example <caption>Function</caption>\n *\n * ```js\n * $('li').not(function (i, el) {\n *   // this === el\n *   return $(this).attr('class') === 'orange';\n * }).length; //=> 2\n * ```\n *\n * @param match - Value to look for, following the rules above.\n * @returns The filtered collection.\n * @see {@link https://api.jquery.com/not/}\n */\nfunction not(match) {\n    let nodes = this.toArray();\n    if (typeof match === 'string') {\n        const matches = new Set(cheerio_select__WEBPACK_IMPORTED_MODULE_1__.filter(match, nodes, this.options));\n        nodes = nodes.filter((el) => !matches.has(el));\n    }\n    else {\n        const filterFn = getFilterFn(match);\n        nodes = nodes.filter((el, i) => !filterFn(el, i));\n    }\n    return this._make(nodes);\n}\n/**\n * Filters the set of matched elements to only those which have the given DOM\n * element as a descendant or which have a descendant that matches the given\n * selector. Equivalent to `.filter(':has(selector)')`.\n *\n * @category Traversing\n * @example <caption>Selector</caption>\n *\n * ```js\n * $('ul').has('.pear').attr('id');\n * //=> fruits\n * ```\n *\n * @example <caption>Element</caption>\n *\n * ```js\n * $('ul').has($('.pear')[0]).attr('id');\n * //=> fruits\n * ```\n *\n * @param selectorOrHaystack - Element to look for.\n * @returns The filtered collection.\n * @see {@link https://api.jquery.com/has/}\n */\nfunction has(selectorOrHaystack) {\n    return this.filter(typeof selectorOrHaystack === 'string'\n        ? // Using the `:has` selector here short-circuits searches.\n            `:has(${selectorOrHaystack})`\n        : (_, el) => this._make(el).find(selectorOrHaystack).length > 0);\n}\n/**\n * Will select the first element of a cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().first().text();\n * //=> Apple\n * ```\n *\n * @returns The first element.\n * @see {@link https://api.jquery.com/first/}\n */\nfunction first() {\n    return this.length > 1 ? this._make(this[0]) : this;\n}\n/**\n * Will select the last element of a cheerio object.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('#fruits').children().last().text();\n * //=> Pear\n * ```\n *\n * @returns The last element.\n * @see {@link https://api.jquery.com/last/}\n */\nfunction last() {\n    return this.length > 0 ? this._make(this[this.length - 1]) : this;\n}\n/**\n * Reduce the set of matched elements to the one at the specified index. Use\n * `.eq(-i)` to count backwards from the last selected element.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).text();\n * //=> Apple\n *\n * $('li').eq(-1).text();\n * //=> Pear\n * ```\n *\n * @param i - Index of the element to select.\n * @returns The element at the `i`th position.\n * @see {@link https://api.jquery.com/eq/}\n */\nfunction eq(i) {\n    var _a;\n    i = +i;\n    // Use the first identity optimization if possible\n    if (i === 0 && this.length <= 1)\n        return this;\n    if (i < 0)\n        i = this.length + i;\n    return this._make((_a = this[i]) !== null && _a !== void 0 ? _a : []);\n}\nfunction get(i) {\n    if (i == null) {\n        return this.toArray();\n    }\n    return this[i < 0 ? this.length + i : i];\n}\n/**\n * Retrieve all the DOM elements contained in the jQuery set as an array.\n *\n * @example\n *\n * ```js\n * $('li').toArray();\n * //=> [ {...}, {...}, {...} ]\n * ```\n *\n * @returns The contained items.\n */\nfunction toArray() {\n    return Array.prototype.slice.call(this);\n}\n/**\n * Search for a given element from among the matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.pear').index();\n * //=> 2 $('.orange').index('li');\n * //=> 1\n * $('.apple').index($('#fruit, li'));\n * //=> 1\n * ```\n *\n * @param selectorOrNeedle - Element to look for.\n * @returns The index of the element.\n * @see {@link https://api.jquery.com/index/}\n */\nfunction index(selectorOrNeedle) {\n    let $haystack;\n    let needle;\n    if (selectorOrNeedle == null) {\n        $haystack = this.parent().children();\n        needle = this[0];\n    }\n    else if (typeof selectorOrNeedle === 'string') {\n        $haystack = this._make(selectorOrNeedle);\n        needle = this[0];\n    }\n    else {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias, unicorn/no-this-assignment\n        $haystack = this;\n        needle = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCheerio)(selectorOrNeedle)\n            ? selectorOrNeedle[0]\n            : selectorOrNeedle;\n    }\n    return Array.prototype.indexOf.call($haystack, needle);\n}\n/**\n * Gets the elements matching the specified range (0-based position).\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').slice(1).eq(0).text();\n * //=> 'Orange'\n *\n * $('li').slice(1, 2).length;\n * //=> 1\n * ```\n *\n * @param start - A position at which the elements begin to be selected. If\n *   negative, it indicates an offset from the end of the set.\n * @param end - A position at which the elements stop being selected. If\n *   negative, it indicates an offset from the end of the set. If omitted, the\n *   range continues until the end of the set.\n * @returns The elements matching the specified range.\n * @see {@link https://api.jquery.com/slice/}\n */\nfunction slice(start, end) {\n    return this._make(Array.prototype.slice.call(this, start, end));\n}\n/**\n * End the most recent filtering operation in the current chain and return the\n * set of matched elements to its previous state.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).end().length;\n * //=> 3\n * ```\n *\n * @returns The previous state of the set of matched elements.\n * @see {@link https://api.jquery.com/end/}\n */\nfunction end() {\n    var _a;\n    return (_a = this.prevObject) !== null && _a !== void 0 ? _a : this._make([]);\n}\n/**\n * Add elements to the set of matched elements.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('.apple').add('.orange').length;\n * //=> 2\n * ```\n *\n * @param other - Elements to add.\n * @param context - Optionally the context of the new selection.\n * @returns The combined set.\n * @see {@link https://api.jquery.com/add/}\n */\nfunction add(other, context) {\n    const selection = this._make(other, context);\n    const contents = (0,domutils__WEBPACK_IMPORTED_MODULE_4__.uniqueSort)([...this.get(), ...selection.get()]);\n    return this._make(contents);\n}\n/**\n * Add the previous set of elements on the stack to the current set, optionally\n * filtered by a selector.\n *\n * @category Traversing\n * @example\n *\n * ```js\n * $('li').eq(0).addBack('.orange').length;\n * //=> 2\n * ```\n *\n * @param selector - Selector for the elements to add.\n * @returns The combined set.\n * @see {@link https://api.jquery.com/addBack/}\n */\nfunction addBack(selector) {\n    return this.prevObject\n        ? this.add(selector ? this.prevObject.filter(selector) : this.prevObject)\n        : this;\n}\n//# sourceMappingURL=traversing.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/cheerio.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/cheerio.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cheerio: () => (/* binding */ Cheerio)\n/* harmony export */ });\n/* harmony import */ var _api_attributes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/attributes.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/attributes.js\");\n/* harmony import */ var _api_traversing_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api/traversing.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/traversing.js\");\n/* harmony import */ var _api_manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api/manipulation.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/manipulation.js\");\n/* harmony import */ var _api_css_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api/css.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/css.js\");\n/* harmony import */ var _api_forms_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api/forms.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/forms.js\");\n/* harmony import */ var _api_extract_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./api/extract.js */ \"(rsc)/./node_modules/cheerio/dist/esm/api/extract.js\");\n\n\n\n\n\n\n/**\n * The cheerio class is the central class of the library. It wraps a set of\n * elements and provides an API for traversing, modifying, and interacting with\n * the set.\n *\n * Loading a document will return the Cheerio class bound to the root element of\n * the document. The class will be instantiated when querying the document (when\n * calling `$('selector')`).\n *\n * @example This is the HTML markup we will be using in all of the API examples:\n *\n * ```html\n * <ul id=\"fruits\">\n *   <li class=\"apple\">Apple</li>\n *   <li class=\"orange\">Orange</li>\n *   <li class=\"pear\">Pear</li>\n * </ul>\n * ```\n */\nclass Cheerio {\n    /**\n     * Instance of cheerio. Methods are specified in the modules. Usage of this\n     * constructor is not recommended. Please use `$.load` instead.\n     *\n     * @private\n     * @param elements - The new selection.\n     * @param root - Sets the root node.\n     * @param options - Options for the instance.\n     */\n    constructor(elements, root, options) {\n        this.length = 0;\n        this.options = options;\n        this._root = root;\n        if (elements) {\n            for (let idx = 0; idx < elements.length; idx++) {\n                this[idx] = elements[idx];\n            }\n            this.length = elements.length;\n        }\n    }\n}\n/** Set a signature of the object. */\nCheerio.prototype.cheerio = '[cheerio object]';\n/*\n * Make cheerio an array-like object\n */\nCheerio.prototype.splice = Array.prototype.splice;\n// Support for (const element of $(...)) iteration:\nCheerio.prototype[Symbol.iterator] = Array.prototype[Symbol.iterator];\n// Plug in the API\nObject.assign(Cheerio.prototype, _api_attributes_js__WEBPACK_IMPORTED_MODULE_0__, _api_traversing_js__WEBPACK_IMPORTED_MODULE_1__, _api_manipulation_js__WEBPACK_IMPORTED_MODULE_2__, _api_css_js__WEBPACK_IMPORTED_MODULE_3__, _api_forms_js__WEBPACK_IMPORTED_MODULE_4__, _api_extract_js__WEBPACK_IMPORTED_MODULE_5__);\n//# sourceMappingURL=cheerio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/cheerio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* reexport safe */ _static_js__WEBPACK_IMPORTED_MODULE_1__.contains),\n/* harmony export */   decodeStream: () => (/* binding */ decodeStream),\n/* harmony export */   fromURL: () => (/* binding */ fromURL),\n/* harmony export */   load: () => (/* reexport safe */ _load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load),\n/* harmony export */   loadBuffer: () => (/* binding */ loadBuffer),\n/* harmony export */   merge: () => (/* reexport safe */ _static_js__WEBPACK_IMPORTED_MODULE_1__.merge),\n/* harmony export */   stringStream: () => (/* binding */ stringStream)\n/* harmony export */ });\n/* harmony import */ var _load_parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./load-parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/load-parse.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! parse5-htmlparser2-tree-adapter */ \"(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n/* harmony import */ var parse5_parser_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! parse5-parser-stream */ \"(rsc)/./node_modules/parse5-parser-stream/dist/index.js\");\n/* harmony import */ var encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! encoding-sniffer */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\");\n/* harmony import */ var undici__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! undici */ \"(rsc)/./node_modules/undici/index.js\");\n/* harmony import */ var whatwg_mimetype__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! whatwg-mimetype */ \"(rsc)/./node_modules/whatwg-mimetype/lib/mime-type.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n/**\n * @file Batteries-included version of Cheerio. This module includes several\n *   convenience methods for loading documents from various sources.\n */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Sniffs the encoding of a buffer, then creates a querying function bound to a\n * document created from the buffer.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n *\n * const buffer = fs.readFileSync('index.html');\n * const $ = cheerio.loadBuffer(buffer);\n * ```\n *\n * @param buffer - The buffer to sniff the encoding of.\n * @param options - The options to pass to Cheerio.\n * @returns The loaded document.\n */\nfunction loadBuffer(buffer, options = {}) {\n    const opts = (0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(options);\n    const str = (0,encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__.decodeBuffer)(buffer, {\n        defaultEncoding: (opts === null || opts === void 0 ? void 0 : opts.xmlMode) ? 'utf8' : 'windows-1252',\n        ...options.encoding,\n    });\n    return (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(str, opts);\n}\nfunction _stringStream(options, cb) {\n    var _a;\n    if (options === null || options === void 0 ? void 0 : options._useHtmlParser2) {\n        const parser = htmlparser2__WEBPACK_IMPORTED_MODULE_3__.createDocumentStream((err, document) => cb(err, (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(document)), options);\n        return new node_stream__WEBPACK_IMPORTED_MODULE_8__.Writable({\n            decodeStrings: false,\n            write(chunk, _encoding, callback) {\n                if (typeof chunk !== 'string') {\n                    throw new TypeError('Expected a string');\n                }\n                parser.write(chunk);\n                callback();\n            },\n            final(callback) {\n                parser.end();\n                callback();\n            },\n        });\n    }\n    options !== null && options !== void 0 ? options : (options = {});\n    (_a = options.treeAdapter) !== null && _a !== void 0 ? _a : (options.treeAdapter = parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter);\n    if (options.scriptingEnabled !== false) {\n        options.scriptingEnabled = true;\n    }\n    const stream = new parse5_parser_stream__WEBPACK_IMPORTED_MODULE_4__.ParserStream(options);\n    (0,node_stream__WEBPACK_IMPORTED_MODULE_8__.finished)(stream, (err) => cb(err, (0,_load_parse_js__WEBPACK_IMPORTED_MODULE_0__.load)(stream.document)));\n    return stream;\n}\n/**\n * Creates a stream that parses a sequence of strings into a document.\n *\n * The stream is a `Writable` stream that accepts strings. When the stream is\n * finished, the callback is called with the loaded document.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n * import * as fs from 'fs';\n *\n * const writeStream = cheerio.stringStream({}, (err, $) => {\n *   if (err) {\n *     // Handle error\n *   }\n *\n *   console.log($('h1').text());\n *   // Output: Hello, world!\n * });\n *\n * fs.createReadStream('my-document.html', { encoding: 'utf8' }).pipe(\n *   writeStream,\n * );\n * ```\n *\n * @param options - The options to pass to Cheerio.\n * @param cb - The callback to call when the stream is finished.\n * @returns The writable stream.\n */\nfunction stringStream(options, cb) {\n    return _stringStream((0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(options), cb);\n}\n/**\n * Parses a stream of buffers into a document.\n *\n * The stream is a `Writable` stream that accepts buffers. When the stream is\n * finished, the callback is called with the loaded document.\n *\n * @category Loading\n * @param options - The options to pass to Cheerio.\n * @param cb - The callback to call when the stream is finished.\n * @returns The writable stream.\n */\nfunction decodeStream(options, cb) {\n    var _a;\n    const { encoding = {}, ...cheerioOptions } = options;\n    const opts = (0,_options_js__WEBPACK_IMPORTED_MODULE_9__.flattenOptions)(cheerioOptions);\n    // Set the default encoding to UTF-8 for XML mode\n    (_a = encoding.defaultEncoding) !== null && _a !== void 0 ? _a : (encoding.defaultEncoding = (opts === null || opts === void 0 ? void 0 : opts.xmlMode) ? 'utf8' : 'windows-1252');\n    const decodeStream = new encoding_sniffer__WEBPACK_IMPORTED_MODULE_5__.DecodeStream(encoding);\n    const loadStream = _stringStream(opts, cb);\n    decodeStream.pipe(loadStream);\n    return decodeStream;\n}\nconst defaultRequestOptions = {\n    method: 'GET',\n    // Allow redirects by default\n    maxRedirections: 5,\n    // Set an Accept header\n    headers: {\n        accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',\n    },\n};\n/**\n * `fromURL` loads a document from a URL.\n *\n * By default, redirects are allowed and non-2xx responses are rejected.\n *\n * @category Loading\n * @example\n *\n * ```js\n * import * as cheerio from 'cheerio';\n *\n * const $ = await cheerio.fromURL('https://example.com');\n * ```\n *\n * @param url - The URL to load the document from.\n * @param options - The options to pass to Cheerio.\n * @returns The loaded document.\n */\nasync function fromURL(url, options = {}) {\n    var _a;\n    const { requestOptions = defaultRequestOptions, encoding = {}, ...cheerioOptions } = options;\n    let undiciStream;\n    // Add headers if none were supplied.\n    (_a = requestOptions.headers) !== null && _a !== void 0 ? _a : (requestOptions.headers = defaultRequestOptions.headers);\n    const promise = new Promise((resolve, reject) => {\n        undiciStream = undici__WEBPACK_IMPORTED_MODULE_6__.stream(url, requestOptions, (res) => {\n            var _a, _b;\n            if (res.statusCode < 200 || res.statusCode >= 300) {\n                throw new undici__WEBPACK_IMPORTED_MODULE_6__.errors.ResponseError('Response Error', res.statusCode, {\n                    headers: res.headers,\n                });\n            }\n            const contentTypeHeader = (_a = res.headers['content-type']) !== null && _a !== void 0 ? _a : 'text/html';\n            const mimeType = new whatwg_mimetype__WEBPACK_IMPORTED_MODULE_7__(Array.isArray(contentTypeHeader)\n                ? contentTypeHeader[0]\n                : contentTypeHeader);\n            if (!mimeType.isHTML() && !mimeType.isXML()) {\n                throw new RangeError(`The content-type \"${mimeType.essence}\" is neither HTML nor XML.`);\n            }\n            // Forward the charset from the header to the decodeStream.\n            encoding.transportLayerEncodingLabel = mimeType.parameters.get('charset');\n            /*\n             * If we allow redirects, we will have entries in the history.\n             * The last entry will be the final URL.\n             */\n            const history = (_b = res.context) === null || _b === void 0 ? void 0 : _b.history;\n            const opts = {\n                encoding,\n                // Set XML mode based on the MIME type.\n                xmlMode: mimeType.isXML(),\n                // Set the `baseURL` to the final URL.\n                baseURL: history ? history[history.length - 1] : url,\n                ...cheerioOptions,\n            };\n            return decodeStream(opts, (err, $) => (err ? reject(err) : resolve($)));\n        });\n    });\n    // Let's make sure the request is completed before returning the promise.\n    await undiciStream;\n    return promise;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/load-parse.js":
/*!*****************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/load-parse.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   load: () => (/* binding */ load)\n/* harmony export */ });\n/* harmony import */ var _load_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./load.js */ \"(rsc)/./node_modules/cheerio/dist/esm/load.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parse.js\");\n/* harmony import */ var _parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parsers/parse5-adapter.js */ \"(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n\n\n\n\n\nconst parse = (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.getParse)((content, options, isDocument, context) => options._useHtmlParser2\n    ? (0,htmlparser2__WEBPACK_IMPORTED_MODULE_4__.parseDocument)(content, options)\n    : (0,_parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__.parseWithParse5)(content, options, isDocument, context));\n// Duplicate docs due to https://github.com/TypeStrong/typedoc/issues/1616\n/**\n * Create a querying function, bound to a document created from the provided\n * markup.\n *\n * Note that similar to web browser contexts, this operation may introduce\n * `<html>`, `<head>`, and `<body>` elements; set `isDocument` to `false` to\n * switch to fragment mode and disable this.\n *\n * @category Loading\n * @param content - Markup to be loaded.\n * @param options - Options for the created instance.\n * @param isDocument - Allows parser to be switched to fragment mode.\n * @returns The loaded document.\n * @see {@link https://cheerio.js.org/docs/basics/loading#load} for additional usage information.\n */\nconst load = (0,_load_js__WEBPACK_IMPORTED_MODULE_0__.getLoad)(parse, (dom, options) => options._useHtmlParser2\n    ? (0,dom_serializer__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dom, options)\n    : (0,_parsers_parse5_adapter_js__WEBPACK_IMPORTED_MODULE_2__.renderWithParse5)(dom));\n//# sourceMappingURL=load-parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/load-parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/load.js":
/*!***********************************************!*\
  !*** ./node_modules/cheerio/dist/esm/load.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLoad: () => (/* binding */ getLoad)\n/* harmony export */ });\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n/* harmony import */ var _static_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./static.js */ \"(rsc)/./node_modules/cheerio/dist/esm/static.js\");\n/* harmony import */ var _cheerio_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cheerio.js */ \"(rsc)/./node_modules/cheerio/dist/esm/cheerio.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/cheerio/dist/esm/utils.js\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/htmlparser2/dist/esm/index.js\");\n\n\n\n\n\nfunction getLoad(parse, render) {\n    /**\n     * Create a querying function, bound to a document created from the provided\n     * markup.\n     *\n     * Note that similar to web browser contexts, this operation may introduce\n     * `<html>`, `<head>`, and `<body>` elements; set `isDocument` to `false` to\n     * switch to fragment mode and disable this.\n     *\n     * @param content - Markup to be loaded.\n     * @param options - Options for the created instance.\n     * @param isDocument - Allows parser to be switched to fragment mode.\n     * @returns The loaded document.\n     * @see {@link https://cheerio.js.org/docs/basics/loading#load} for additional usage information.\n     */\n    return function load(content, options, isDocument = true) {\n        if (content == null) {\n            throw new Error('cheerio.load() expects a string');\n        }\n        const internalOpts = (0,_options_js__WEBPACK_IMPORTED_MODULE_0__.flattenOptions)(options);\n        const initialRoot = parse(content, internalOpts, isDocument, null);\n        /**\n         * Create an extended class here, so that extensions only live on one\n         * instance.\n         */\n        class LoadedCheerio extends _cheerio_js__WEBPACK_IMPORTED_MODULE_2__.Cheerio {\n            _make(selector, context) {\n                const cheerio = initialize(selector, context);\n                cheerio.prevObject = this;\n                return cheerio;\n            }\n            _parse(content, options, isDocument, context) {\n                return parse(content, options, isDocument, context);\n            }\n            _render(dom) {\n                return render(dom, this.options);\n            }\n        }\n        function initialize(selector, context, root = initialRoot, opts) {\n            // $($)\n            if (selector && (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(selector))\n                return selector;\n            const options = (0,_options_js__WEBPACK_IMPORTED_MODULE_0__.flattenOptions)(opts, internalOpts);\n            const r = typeof root === 'string'\n                ? [parse(root, options, false, null)]\n                : 'length' in root\n                    ? root\n                    : [root];\n            const rootInstance = (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(r)\n                ? r\n                : new LoadedCheerio(r, null, options);\n            // Add a cyclic reference, so that calling methods on `_root` never fails.\n            rootInstance._root = rootInstance;\n            // $(), $(null), $(undefined), $(false)\n            if (!selector) {\n                return new LoadedCheerio(undefined, rootInstance, options);\n            }\n            const elements = typeof selector === 'string' && (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(selector)\n                ? // $(<html>)\n                    parse(selector, options, false, null).children\n                : isNode(selector)\n                    ? // $(dom)\n                        [selector]\n                    : Array.isArray(selector)\n                        ? // $([dom])\n                            selector\n                        : undefined;\n            const instance = new LoadedCheerio(elements, rootInstance, options);\n            if (elements) {\n                return instance;\n            }\n            if (typeof selector !== 'string') {\n                throw new TypeError('Unexpected type of selector');\n            }\n            // We know that our selector is a string now.\n            let search = selector;\n            const searchContext = context\n                ? // If we don't have a context, maybe we have a root, from loading\n                    typeof context === 'string'\n                        ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHtml)(context)\n                            ? // $('li', '<ul>...</ul>')\n                                new LoadedCheerio([parse(context, options, false, null)], rootInstance, options)\n                            : // $('li', 'ul')\n                                ((search = `${context} ${search}`), rootInstance)\n                        : (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCheerio)(context)\n                            ? // $('li', $)\n                                context\n                            : // $('li', node), $('li', [nodes])\n                                new LoadedCheerio(Array.isArray(context) ? context : [context], rootInstance, options)\n                : rootInstance;\n            // If we still don't have a context, return\n            if (!searchContext)\n                return instance;\n            /*\n             * #id, .class, tag\n             */\n            return searchContext.find(search);\n        }\n        // Add in static methods & properties\n        Object.assign(initialize, _static_js__WEBPACK_IMPORTED_MODULE_1__, {\n            load,\n            // `_root` and `_options` are used in static methods.\n            _root: initialRoot,\n            _options: internalOpts,\n            // Add `fn` for plugins\n            fn: LoadedCheerio.prototype,\n            // Add the prototype here to maintain `instanceof` behavior.\n            prototype: LoadedCheerio.prototype,\n        });\n        return initialize;\n    };\n}\nfunction isNode(obj) {\n    return (\n    // @ts-expect-error: TS doesn't know about the `name` property.\n    !!obj.name ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Root ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Text ||\n        // @ts-expect-error: TS doesn't know about the `type` property.\n        obj.type === htmlparser2__WEBPACK_IMPORTED_MODULE_4__.ElementType.Comment);\n}\n//# sourceMappingURL=load.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/load.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/options.js":
/*!**************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/options.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenOptions: () => (/* binding */ flattenOptions)\n/* harmony export */ });\nconst defaultOpts = {\n    _useHtmlParser2: false,\n};\n/**\n * Flatten the options for Cheerio.\n *\n * This will set `_useHtmlParser2` to true if `xml` is set to true.\n *\n * @param options - The options to flatten.\n * @param baseOptions - The base options to use.\n * @returns The flattened options.\n */\nfunction flattenOptions(options, baseOptions) {\n    if (!options) {\n        return baseOptions !== null && baseOptions !== void 0 ? baseOptions : defaultOpts;\n    }\n    const opts = {\n        _useHtmlParser2: !!options.xmlMode,\n        ...baseOptions,\n        ...options,\n    };\n    if (options.xml) {\n        opts._useHtmlParser2 = true;\n        opts.xmlMode = true;\n        if (options.xml !== true) {\n            Object.assign(opts, options.xml);\n        }\n    }\n    else if (options.xmlMode) {\n        opts._useHtmlParser2 = true;\n    }\n    return opts;\n}\n//# sourceMappingURL=options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY2hlZXJpby9kaXN0L2VzbS9vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ljb24tZ2VuZXJhdG9yLy4vbm9kZV9tb2R1bGVzL2NoZWVyaW8vZGlzdC9lc20vb3B0aW9ucy5qcz82N2RkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGRlZmF1bHRPcHRzID0ge1xuICAgIF91c2VIdG1sUGFyc2VyMjogZmFsc2UsXG59O1xuLyoqXG4gKiBGbGF0dGVuIHRoZSBvcHRpb25zIGZvciBDaGVlcmlvLlxuICpcbiAqIFRoaXMgd2lsbCBzZXQgYF91c2VIdG1sUGFyc2VyMmAgdG8gdHJ1ZSBpZiBgeG1sYCBpcyBzZXQgdG8gdHJ1ZS5cbiAqXG4gKiBAcGFyYW0gb3B0aW9ucyAtIFRoZSBvcHRpb25zIHRvIGZsYXR0ZW4uXG4gKiBAcGFyYW0gYmFzZU9wdGlvbnMgLSBUaGUgYmFzZSBvcHRpb25zIHRvIHVzZS5cbiAqIEByZXR1cm5zIFRoZSBmbGF0dGVuZWQgb3B0aW9ucy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZsYXR0ZW5PcHRpb25zKG9wdGlvbnMsIGJhc2VPcHRpb25zKSB7XG4gICAgaWYgKCFvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBiYXNlT3B0aW9ucyAhPT0gbnVsbCAmJiBiYXNlT3B0aW9ucyAhPT0gdm9pZCAwID8gYmFzZU9wdGlvbnMgOiBkZWZhdWx0T3B0cztcbiAgICB9XG4gICAgY29uc3Qgb3B0cyA9IHtcbiAgICAgICAgX3VzZUh0bWxQYXJzZXIyOiAhIW9wdGlvbnMueG1sTW9kZSxcbiAgICAgICAgLi4uYmFzZU9wdGlvbnMsXG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgfTtcbiAgICBpZiAob3B0aW9ucy54bWwpIHtcbiAgICAgICAgb3B0cy5fdXNlSHRtbFBhcnNlcjIgPSB0cnVlO1xuICAgICAgICBvcHRzLnhtbE1vZGUgPSB0cnVlO1xuICAgICAgICBpZiAob3B0aW9ucy54bWwgIT09IHRydWUpIHtcbiAgICAgICAgICAgIE9iamVjdC5hc3NpZ24ob3B0cywgb3B0aW9ucy54bWwpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2UgaWYgKG9wdGlvbnMueG1sTW9kZSkge1xuICAgICAgICBvcHRzLl91c2VIdG1sUGFyc2VyMiA9IHRydWU7XG4gICAgfVxuICAgIHJldHVybiBvcHRzO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b3B0aW9ucy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/options.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/parse.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/parse.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getParse: () => (/* binding */ getParse),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n\n\n/**\n * Get the parse function with options.\n *\n * @param parser - The parser function.\n * @returns The parse function with options.\n */\nfunction getParse(parser) {\n    /**\n     * Parse a HTML string or a node.\n     *\n     * @param content - The HTML string or node.\n     * @param options - The parser options.\n     * @param isDocument - If `content` is a document.\n     * @param context - The context node in the DOM tree.\n     * @returns The parsed document node.\n     */\n    return function parse(content, options, isDocument, context) {\n        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(content)) {\n            content = content.toString();\n        }\n        if (typeof content === 'string') {\n            return parser(content, options, isDocument, context);\n        }\n        const doc = content;\n        if (!Array.isArray(doc) && (0,domhandler__WEBPACK_IMPORTED_MODULE_1__.isDocument)(doc)) {\n            // If `doc` is already a root, just return it\n            return doc;\n        }\n        // Add conent to new root element\n        const root = new domhandler__WEBPACK_IMPORTED_MODULE_1__.Document([]);\n        // Update the DOM using the root\n        update(doc, root);\n        return root;\n    };\n}\n/**\n * Update the dom structure, for one changed layer.\n *\n * @param newChilds - The new children.\n * @param parent - The new parent.\n * @returns The parent node.\n */\nfunction update(newChilds, parent) {\n    // Normalize\n    const arr = Array.isArray(newChilds) ? newChilds : [newChilds];\n    // Update parent\n    if (parent) {\n        parent.children = arr;\n    }\n    else {\n        parent = null;\n    }\n    // Update neighbors\n    for (let i = 0; i < arr.length; i++) {\n        const node = arr[i];\n        // Cleanly remove existing nodes from their previous structures.\n        if (node.parent && node.parent.children !== arr) {\n            (0,domutils__WEBPACK_IMPORTED_MODULE_0__.removeElement)(node);\n        }\n        if (parent) {\n            node.prev = arr[i - 1] || null;\n            node.next = arr[i + 1] || null;\n        }\n        else {\n            node.prev = node.next = null;\n        }\n        node.parent = parent;\n    }\n    return parent;\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js":
/*!*****************************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseWithParse5: () => (/* binding */ parseWithParse5),\n/* harmony export */   renderWithParse5: () => (/* binding */ renderWithParse5)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! parse5-htmlparser2-tree-adapter */ \"(rsc)/./node_modules/parse5-htmlparser2-tree-adapter/dist/index.js\");\n\n\n\n/**\n * Parse the content with `parse5` in the context of the given `ParentNode`.\n *\n * @param content - The content to parse.\n * @param options - A set of options to use to parse.\n * @param isDocument - Whether to parse the content as a full HTML document.\n * @param context - The context in which to parse the content.\n * @returns The parsed content.\n */\nfunction parseWithParse5(content, options, isDocument, context) {\n    var _a;\n    (_a = options.treeAdapter) !== null && _a !== void 0 ? _a : (options.treeAdapter = parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter);\n    if (options.scriptingEnabled !== false) {\n        options.scriptingEnabled = true;\n    }\n    return isDocument\n        ? (0,parse5__WEBPACK_IMPORTED_MODULE_1__.parse)(content, options)\n        : (0,parse5__WEBPACK_IMPORTED_MODULE_1__.parseFragment)(context, content, options);\n}\nconst renderOpts = { treeAdapter: parse5_htmlparser2_tree_adapter__WEBPACK_IMPORTED_MODULE_2__.adapter };\n/**\n * Renders the given DOM tree with `parse5` and returns the result as a string.\n *\n * @param dom - The DOM tree to render.\n * @returns The rendered document.\n */\nfunction renderWithParse5(dom) {\n    /*\n     * `dom-serializer` passes over the special \"root\" node and renders the\n     * node's children in its place. To mimic this behavior with `parse5`, an\n     * equivalent operation must be applied to the input array.\n     */\n    const nodes = 'length' in dom ? dom : [dom];\n    for (let index = 0; index < nodes.length; index += 1) {\n        const node = nodes[index];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isDocument)(node)) {\n            Array.prototype.splice.call(nodes, index, 1, ...node.children);\n        }\n    }\n    let result = '';\n    for (let index = 0; index < nodes.length; index += 1) {\n        const node = nodes[index];\n        result += (0,parse5__WEBPACK_IMPORTED_MODULE_1__.serializeOuter)(node, renderOpts);\n    }\n    return result;\n}\n//# sourceMappingURL=parse5-adapter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/parsers/parse5-adapter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/static.js":
/*!*************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/static.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* binding */ contains),\n/* harmony export */   extract: () => (/* binding */ extract),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   merge: () => (/* binding */ merge),\n/* harmony export */   parseHTML: () => (/* binding */ parseHTML),\n/* harmony export */   root: () => (/* binding */ root),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/domutils/lib/esm/index.js\");\n/* harmony import */ var _options_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./options.js */ \"(rsc)/./node_modules/cheerio/dist/esm/options.js\");\n\n\n/**\n * Helper function to render a DOM.\n *\n * @param that - Cheerio instance to render.\n * @param dom - The DOM to render. Defaults to `that`'s root.\n * @param options - Options for rendering.\n * @returns The rendered document.\n */\nfunction render(that, dom, options) {\n    if (!that)\n        return '';\n    return that(dom !== null && dom !== void 0 ? dom : that._root.children, null, undefined, options).toString();\n}\n/**\n * Checks if a passed object is an options object.\n *\n * @param dom - Object to check if it is an options object.\n * @param options - Options object.\n * @returns Whether the object is an options object.\n */\nfunction isOptions(dom, options) {\n    return (!options &&\n        typeof dom === 'object' &&\n        dom != null &&\n        !('length' in dom) &&\n        !('type' in dom));\n}\nfunction html(dom, options) {\n    /*\n     * Be flexible about parameters, sometimes we call html(),\n     * with options as only parameter\n     * check dom argument for dom element specific properties\n     * assume there is no 'length' or 'type' properties in the options object\n     */\n    const toRender = isOptions(dom) ? ((options = dom), undefined) : dom;\n    /*\n     * Sometimes `$.html()` is used without preloading html,\n     * so fallback non-existing options to the default ones.\n     */\n    const opts = {\n        ...this === null || this === void 0 ? void 0 : this._options,\n        ...(0,_options_js__WEBPACK_IMPORTED_MODULE_1__.flattenOptions)(options),\n    };\n    return render(this, toRender, opts);\n}\n/**\n * Render the document as XML.\n *\n * @category Static\n * @param dom - Element to render.\n * @returns THe rendered document.\n */\nfunction xml(dom) {\n    const options = { ...this._options, xmlMode: true };\n    return render(this, dom, options);\n}\n/**\n * Render the document as text.\n *\n * This returns the `textContent` of the passed elements. The result will\n * include the contents of `<script>` and `<style>` elements. To avoid this, use\n * `.prop('innerText')` instead.\n *\n * @category Static\n * @param elements - Elements to render.\n * @returns The rendered document.\n */\nfunction text(elements) {\n    const elems = elements !== null && elements !== void 0 ? elements : (this ? this.root() : []);\n    let ret = '';\n    for (let i = 0; i < elems.length; i++) {\n        ret += (0,domutils__WEBPACK_IMPORTED_MODULE_0__.textContent)(elems[i]);\n    }\n    return ret;\n}\nfunction parseHTML(data, context, keepScripts = typeof context === 'boolean' ? context : false) {\n    if (!data || typeof data !== 'string') {\n        return null;\n    }\n    if (typeof context === 'boolean') {\n        keepScripts = context;\n    }\n    const parsed = this.load(data, this._options, false);\n    if (!keepScripts) {\n        parsed('script').remove();\n    }\n    /*\n     * The `children` array is used by Cheerio internally to group elements that\n     * share the same parents. When nodes created through `parseHTML` are\n     * inserted into previously-existing DOM structures, they will be removed\n     * from the `children` array. The results of `parseHTML` should remain\n     * constant across these operations, so a shallow copy should be returned.\n     */\n    return [...parsed.root()[0].children];\n}\n/**\n * Sometimes you need to work with the top-level root element. To query it, you\n * can use `$.root()`.\n *\n * @category Static\n * @example\n *\n * ```js\n * $.root().append('<ul id=\"vegetables\"></ul>').html();\n * //=> <ul id=\"fruits\">...</ul><ul id=\"vegetables\"></ul>\n * ```\n *\n * @returns Cheerio instance wrapping the root node.\n * @alias Cheerio.root\n */\nfunction root() {\n    return this(this._root);\n}\n/**\n * Checks to see if the `contained` DOM element is a descendant of the\n * `container` DOM element.\n *\n * @category Static\n * @param container - Potential parent node.\n * @param contained - Potential child node.\n * @returns Indicates if the nodes contain one another.\n * @alias Cheerio.contains\n * @see {@link https://api.jquery.com/jQuery.contains/}\n */\nfunction contains(container, contained) {\n    // According to the jQuery API, an element does not \"contain\" itself\n    if (contained === container) {\n        return false;\n    }\n    /*\n     * Step up the descendants, stopping when the root element is reached\n     * (signaled by `.parent` returning a reference to the same object)\n     */\n    let next = contained;\n    while (next && next !== next.parent) {\n        next = next.parent;\n        if (next === container) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Extract multiple values from a document, and store them in an object.\n *\n * @category Static\n * @param map - An object containing key-value pairs. The keys are the names of\n *   the properties to be created on the object, and the values are the\n *   selectors to be used to extract the values.\n * @returns An object containing the extracted values.\n */\nfunction extract(map) {\n    return this.root().extract(map);\n}\n/**\n * $.merge().\n *\n * @category Static\n * @param arr1 - First array.\n * @param arr2 - Second array.\n * @returns `arr1`, with elements of `arr2` inserted.\n * @alias Cheerio.merge\n * @see {@link https://api.jquery.com/jQuery.merge/}\n */\nfunction merge(arr1, arr2) {\n    if (!isArrayLike(arr1) || !isArrayLike(arr2)) {\n        return;\n    }\n    let newLength = arr1.length;\n    const len = +arr2.length;\n    for (let i = 0; i < len; i++) {\n        arr1[newLength++] = arr2[i];\n    }\n    arr1.length = newLength;\n    return arr1;\n}\n/**\n * Checks if an object is array-like.\n *\n * @category Static\n * @param item - Item to check.\n * @returns Indicates if the item is array-like.\n */\nfunction isArrayLike(item) {\n    if (Array.isArray(item)) {\n        return true;\n    }\n    if (typeof item !== 'object' ||\n        item === null ||\n        !('length' in item) ||\n        typeof item.length !== 'number' ||\n        item.length < 0) {\n        return false;\n    }\n    for (let i = 0; i < item.length; i++) {\n        if (!(i in item)) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceMappingURL=static.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/static.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/cheerio/dist/esm/utils.js":
/*!************************************************!*\
  !*** ./node_modules/cheerio/dist/esm/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelCase: () => (/* binding */ camelCase),\n/* harmony export */   cssCase: () => (/* binding */ cssCase),\n/* harmony export */   domEach: () => (/* binding */ domEach),\n/* harmony export */   isCheerio: () => (/* binding */ isCheerio),\n/* harmony export */   isHtml: () => (/* binding */ isHtml)\n/* harmony export */ });\n/**\n * Checks if an object is a Cheerio instance.\n *\n * @category Utils\n * @param maybeCheerio - The object to check.\n * @returns Whether the object is a Cheerio instance.\n */\nfunction isCheerio(maybeCheerio) {\n    return maybeCheerio.cheerio != null;\n}\n/**\n * Convert a string to camel case notation.\n *\n * @private\n * @category Utils\n * @param str - The string to be converted.\n * @returns String in camel case notation.\n */\nfunction camelCase(str) {\n    return str.replace(/[._-](\\w|$)/g, (_, x) => x.toUpperCase());\n}\n/**\n * Convert a string from camel case to \"CSS case\", where word boundaries are\n * described by hyphens (\"-\") and all characters are lower-case.\n *\n * @private\n * @category Utils\n * @param str - The string to be converted.\n * @returns String in \"CSS case\".\n */\nfunction cssCase(str) {\n    return str.replace(/[A-Z]/g, '-$&').toLowerCase();\n}\n/**\n * Iterate over each DOM element without creating intermediary Cheerio\n * instances.\n *\n * This is indented for use internally to avoid otherwise unnecessary memory\n * pressure introduced by _make.\n *\n * @category Utils\n * @param array - The array to iterate over.\n * @param fn - Function to call.\n * @returns The original instance.\n */\nfunction domEach(array, fn) {\n    const len = array.length;\n    for (let i = 0; i < len; i++)\n        fn(array[i], i);\n    return array;\n}\nvar CharacterCode;\n(function (CharacterCode) {\n    CharacterCode[CharacterCode[\"LowerA\"] = 97] = \"LowerA\";\n    CharacterCode[CharacterCode[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharacterCode[CharacterCode[\"UpperA\"] = 65] = \"UpperA\";\n    CharacterCode[CharacterCode[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharacterCode[CharacterCode[\"Exclamation\"] = 33] = \"Exclamation\";\n})(CharacterCode || (CharacterCode = {}));\n/**\n * Check if string is HTML.\n *\n * Tests for a `<` within a string, immediate followed by a letter and\n * eventually followed by a `>`.\n *\n * @private\n * @category Utils\n * @param str - The string to check.\n * @returns Indicates if `str` is HTML.\n */\nfunction isHtml(str) {\n    const tagStart = str.indexOf('<');\n    if (tagStart === -1 || tagStart > str.length - 3)\n        return false;\n    const tagChar = str.charCodeAt(tagStart + 1);\n    return (((tagChar >= CharacterCode.LowerA && tagChar <= CharacterCode.LowerZ) ||\n        (tagChar >= CharacterCode.UpperA && tagChar <= CharacterCode.UpperZ) ||\n        tagChar === CharacterCode.Exclamation) &&\n        str.includes('>', tagStart + 2));\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cheerio/dist/esm/utils.js\n");

/***/ })

};
;