"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encoding-sniffer";
exports.ids = ["vendor-chunks/encoding-sniffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeStream: () => (/* binding */ DecodeStream),\n/* harmony export */   decodeBuffer: () => (/* binding */ decodeBuffer),\n/* harmony export */   getEncoding: () => (/* reexport safe */ _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var iconv_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\n/* harmony import */ var _sniffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sniffer.js */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\");\n\n\n\n/**\n * Sniff the encoding of a buffer, then decode it.\n *\n * @param buffer Buffer to be decoded\n * @param options Options for the sniffer\n * @returns The decoded buffer\n */\nfunction decodeBuffer(buffer, options = {}) {\n    return iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decode(buffer, (0,_sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)(buffer, options));\n}\n/**\n * Decodes a stream of buffers into a stream of strings.\n *\n * Reads the first 1024 bytes and passes them to the sniffer. Once an encoding\n * has been determined, it passes all data to iconv-lite's stream and outputs\n * the results.\n */\nclass DecodeStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Transform {\n    constructor(options) {\n        var _a;\n        super({ decodeStrings: false, encoding: \"utf-8\" });\n        this.buffers = [];\n        /** The iconv decode stream. If it is set, we have read more than `options.maxBytes` bytes. */\n        this.iconv = null;\n        this.readBytes = 0;\n        this.sniffer = new _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.Sniffer(options);\n        this.maxBytes = (_a = options === null || options === void 0 ? void 0 : options.maxBytes) !== null && _a !== void 0 ? _a : 1024;\n    }\n    _transform(chunk, _encoding, callback) {\n        if (this.readBytes < this.maxBytes) {\n            this.sniffer.write(chunk);\n            this.readBytes += chunk.length;\n            if (this.readBytes < this.maxBytes) {\n                this.buffers.push(chunk);\n                callback();\n                return;\n            }\n        }\n        this.getIconvStream().write(chunk, callback);\n    }\n    getIconvStream() {\n        if (this.iconv) {\n            return this.iconv;\n        }\n        const stream = iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decodeStream(this.sniffer.encoding);\n        stream.on(\"data\", (chunk) => this.push(chunk, \"utf-8\"));\n        stream.on(\"end\", () => this.push(null));\n        this.iconv = stream;\n        for (const buffer of this.buffers) {\n            stream.write(buffer);\n        }\n        this.buffers.length = 0;\n        return stream;\n    }\n    _flush(callback) {\n        this.getIconvStream().end(callback);\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js":
/*!***********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/sniffer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResultType: () => (/* binding */ ResultType),\n/* harmony export */   STRINGS: () => (/* binding */ STRINGS),\n/* harmony export */   Sniffer: () => (/* binding */ Sniffer),\n/* harmony export */   getEncoding: () => (/* binding */ getEncoding)\n/* harmony export */ });\n/* harmony import */ var whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! whatwg-encoding */ \"(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js\");\n\n// https://html.spec.whatwg.org/multipage/syntax.html#prescan-a-byte-stream-to-determine-its-encoding\nvar State;\n(function (State) {\n    // Before anything starts; can be any of BOM, UTF-16 XML declarations or meta tags\n    State[State[\"Begin\"] = 0] = \"Begin\";\n    // Inside of a BOM\n    State[State[\"BOM16BE\"] = 1] = \"BOM16BE\";\n    State[State[\"BOM16LE\"] = 2] = \"BOM16LE\";\n    State[State[\"BOM8\"] = 3] = \"BOM8\";\n    // XML prefix\n    State[State[\"UTF16LE_XML_PREFIX\"] = 4] = \"UTF16LE_XML_PREFIX\";\n    State[State[\"BeginLT\"] = 5] = \"BeginLT\";\n    State[State[\"UTF16BE_XML_PREFIX\"] = 6] = \"UTF16BE_XML_PREFIX\";\n    // Waiting for opening `<`\n    State[State[\"BeforeTag\"] = 7] = \"BeforeTag\";\n    // After the opening `<`\n    State[State[\"BeforeTagName\"] = 8] = \"BeforeTagName\";\n    // After `</`\n    State[State[\"BeforeCloseTagName\"] = 9] = \"BeforeCloseTagName\";\n    // Beginning of a comment\n    State[State[\"CommentStart\"] = 10] = \"CommentStart\";\n    // End of a comment\n    State[State[\"CommentEnd\"] = 11] = \"CommentEnd\";\n    // A tag name that could be `meta`\n    State[State[\"TagNameMeta\"] = 12] = \"TagNameMeta\";\n    // A tag name that is not `meta`\n    State[State[\"TagNameOther\"] = 13] = \"TagNameOther\";\n    // XML declaration\n    State[State[\"XMLDeclaration\"] = 14] = \"XMLDeclaration\";\n    State[State[\"XMLDeclarationBeforeEncoding\"] = 15] = \"XMLDeclarationBeforeEncoding\";\n    State[State[\"XMLDeclarationAfterEncoding\"] = 16] = \"XMLDeclarationAfterEncoding\";\n    State[State[\"XMLDeclarationBeforeValue\"] = 17] = \"XMLDeclarationBeforeValue\";\n    State[State[\"XMLDeclarationValue\"] = 18] = \"XMLDeclarationValue\";\n    // Anything that looks like a tag, but doesn't fit in the above categories\n    State[State[\"WeirdTag\"] = 19] = \"WeirdTag\";\n    State[State[\"BeforeAttribute\"] = 20] = \"BeforeAttribute\";\n    /*\n     * Attributes in meta tag — we compare them to our set here, and back out\n     * We care about four attributes: http-equiv, content-type, content, charset\n     */\n    State[State[\"MetaAttribHttpEquiv\"] = 21] = \"MetaAttribHttpEquiv\";\n    // The value has to be `content-type`\n    State[State[\"MetaAttribHttpEquivValue\"] = 22] = \"MetaAttribHttpEquivValue\";\n    State[State[\"MetaAttribC\"] = 23] = \"MetaAttribC\";\n    State[State[\"MetaAttribContent\"] = 24] = \"MetaAttribContent\";\n    State[State[\"MetaAttribCharset\"] = 25] = \"MetaAttribCharset\";\n    // Waiting for whitespace\n    State[State[\"MetaAttribAfterName\"] = 26] = \"MetaAttribAfterName\";\n    State[State[\"MetaContentValueQuotedBeforeEncoding\"] = 27] = \"MetaContentValueQuotedBeforeEncoding\";\n    State[State[\"MetaContentValueQuotedAfterEncoding\"] = 28] = \"MetaContentValueQuotedAfterEncoding\";\n    State[State[\"MetaContentValueQuotedBeforeValue\"] = 29] = \"MetaContentValueQuotedBeforeValue\";\n    State[State[\"MetaContentValueQuotedValueQuoted\"] = 30] = \"MetaContentValueQuotedValueQuoted\";\n    State[State[\"MetaContentValueQuotedValueUnquoted\"] = 31] = \"MetaContentValueQuotedValueUnquoted\";\n    State[State[\"MetaContentValueUnquotedBeforeEncoding\"] = 32] = \"MetaContentValueUnquotedBeforeEncoding\";\n    State[State[\"MetaContentValueUnquotedBeforeValue\"] = 33] = \"MetaContentValueUnquotedBeforeValue\";\n    State[State[\"MetaContentValueUnquotedValueQuoted\"] = 34] = \"MetaContentValueUnquotedValueQuoted\";\n    State[State[\"MetaContentValueUnquotedValueUnquoted\"] = 35] = \"MetaContentValueUnquotedValueUnquoted\";\n    State[State[\"AnyAttribName\"] = 36] = \"AnyAttribName\";\n    // After the name of an attribute, before the equals sign\n    State[State[\"AfterAttributeName\"] = 37] = \"AfterAttributeName\";\n    // After `=`\n    State[State[\"BeforeAttributeValue\"] = 38] = \"BeforeAttributeValue\";\n    State[State[\"AttributeValueQuoted\"] = 39] = \"AttributeValueQuoted\";\n    State[State[\"AttributeValueUnquoted\"] = 40] = \"AttributeValueUnquoted\";\n})(State || (State = {}));\nvar ResultType;\n(function (ResultType) {\n    // Byte order mark\n    ResultType[ResultType[\"BOM\"] = 0] = \"BOM\";\n    // User- or transport layer-defined\n    ResultType[ResultType[\"PASSED\"] = 1] = \"PASSED\";\n    // XML prefixes\n    ResultType[ResultType[\"XML_PREFIX\"] = 2] = \"XML_PREFIX\";\n    // Meta tag\n    ResultType[ResultType[\"META_TAG\"] = 3] = \"META_TAG\";\n    // XML encoding\n    ResultType[ResultType[\"XML_ENCODING\"] = 4] = \"XML_ENCODING\";\n    // Default\n    ResultType[ResultType[\"DEFAULT\"] = 5] = \"DEFAULT\";\n})(ResultType || (ResultType = {}));\nvar AttribType;\n(function (AttribType) {\n    AttribType[AttribType[\"None\"] = 0] = \"None\";\n    AttribType[AttribType[\"HttpEquiv\"] = 1] = \"HttpEquiv\";\n    AttribType[AttribType[\"Content\"] = 2] = \"Content\";\n    AttribType[AttribType[\"Charset\"] = 3] = \"Charset\";\n})(AttribType || (AttribType = {}));\nvar Chars;\n(function (Chars) {\n    Chars[Chars[\"NIL\"] = 0] = \"NIL\";\n    Chars[Chars[\"TAB\"] = 9] = \"TAB\";\n    Chars[Chars[\"LF\"] = 10] = \"LF\";\n    Chars[Chars[\"CR\"] = 13] = \"CR\";\n    Chars[Chars[\"SPACE\"] = 32] = \"SPACE\";\n    Chars[Chars[\"EXCLAMATION\"] = 33] = \"EXCLAMATION\";\n    Chars[Chars[\"DQUOTE\"] = 34] = \"DQUOTE\";\n    Chars[Chars[\"SQUOTE\"] = 39] = \"SQUOTE\";\n    Chars[Chars[\"DASH\"] = 45] = \"DASH\";\n    Chars[Chars[\"SLASH\"] = 47] = \"SLASH\";\n    Chars[Chars[\"SEMICOLON\"] = 59] = \"SEMICOLON\";\n    Chars[Chars[\"LT\"] = 60] = \"LT\";\n    Chars[Chars[\"EQUALS\"] = 61] = \"EQUALS\";\n    Chars[Chars[\"GT\"] = 62] = \"GT\";\n    Chars[Chars[\"QUESTION\"] = 63] = \"QUESTION\";\n    Chars[Chars[\"UpperA\"] = 65] = \"UpperA\";\n    Chars[Chars[\"UpperZ\"] = 90] = \"UpperZ\";\n    Chars[Chars[\"LowerA\"] = 97] = \"LowerA\";\n    Chars[Chars[\"LowerZ\"] = 122] = \"LowerZ\";\n})(Chars || (Chars = {}));\nconst SPACE_CHARACTERS = new Set([Chars.SPACE, Chars.LF, Chars.CR, Chars.TAB]);\nconst END_OF_UNQUOTED_ATTRIBUTE_VALUE = new Set([\n    Chars.SPACE,\n    Chars.LF,\n    Chars.CR,\n    Chars.TAB,\n    Chars.GT,\n]);\nfunction toUint8Array(str) {\n    const arr = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; i++) {\n        arr[i] = str.charCodeAt(i);\n    }\n    return arr;\n}\nconst STRINGS = {\n    UTF8_BOM: new Uint8Array([0xef, 0xbb, 0xbf]),\n    UTF16LE_BOM: new Uint8Array([0xff, 0xfe]),\n    UTF16BE_BOM: new Uint8Array([0xfe, 0xff]),\n    UTF16LE_XML_PREFIX: new Uint8Array([0x3c, 0x0, 0x3f, 0x0, 0x78, 0x0]),\n    UTF16BE_XML_PREFIX: new Uint8Array([0x0, 0x3c, 0x0, 0x3f, 0x0, 0x78]),\n    XML_DECLARATION: toUint8Array(\"<?xml\"),\n    ENCODING: toUint8Array(\"encoding\"),\n    META: toUint8Array(\"meta\"),\n    HTTP_EQUIV: toUint8Array(\"http-equiv\"),\n    CONTENT: toUint8Array(\"content\"),\n    CONTENT_TYPE: toUint8Array(\"content-type\"),\n    CHARSET: toUint8Array(\"charset\"),\n    COMMENT_START: toUint8Array(\"<!--\"),\n    COMMENT_END: toUint8Array(\"-->\"),\n};\nfunction isAsciiAlpha(c) {\n    return ((c >= Chars.UpperA && c <= Chars.UpperZ) ||\n        (c >= Chars.LowerA && c <= Chars.LowerZ));\n}\nfunction isQuote(c) {\n    return c === Chars.DQUOTE || c === Chars.SQUOTE;\n}\nclass Sniffer {\n    setResult(label, type) {\n        if (this.resultType === ResultType.DEFAULT || this.resultType > type) {\n            const encoding = (0,whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__.labelToName)(label);\n            if (encoding) {\n                this.encoding =\n                    // Check if we are in a meta tag and the encoding is `x-user-defined`\n                    type === ResultType.META_TAG &&\n                        encoding === \"x-user-defined\"\n                        ? \"windows-1252\"\n                        : // Check if we are in a meta tag or xml declaration, and the encoding is UTF-16\n                            (type === ResultType.META_TAG ||\n                                type === ResultType.XML_ENCODING) &&\n                                (encoding === \"UTF-16LE\" || encoding === \"UTF-16BE\")\n                                ? \"UTF-8\"\n                                : encoding;\n                this.resultType = type;\n            }\n        }\n    }\n    constructor({ maxBytes = 1024, userEncoding, transportLayerEncodingLabel, defaultEncoding, } = {}) {\n        /** The offset of the previous buffers. */\n        this.offset = 0;\n        this.state = State.Begin;\n        this.sectionIndex = 0;\n        this.attribType = AttribType.None;\n        /**\n         * Indicates if the `http-equiv` is `content-type`.\n         *\n         * Initially `null`, a boolean when a value is found.\n         */\n        this.gotPragma = null;\n        this.needsPragma = null;\n        this.inMetaTag = false;\n        this.encoding = \"windows-1252\";\n        this.resultType = ResultType.DEFAULT;\n        this.quoteCharacter = 0;\n        this.attributeValue = [];\n        this.maxBytes = maxBytes;\n        if (userEncoding) {\n            this.setResult(userEncoding, ResultType.PASSED);\n        }\n        if (transportLayerEncodingLabel) {\n            this.setResult(transportLayerEncodingLabel, ResultType.PASSED);\n        }\n        if (defaultEncoding) {\n            this.setResult(defaultEncoding, ResultType.DEFAULT);\n        }\n    }\n    stateBegin(c) {\n        switch (c) {\n            case STRINGS.UTF16BE_BOM[0]: {\n                this.state = State.BOM16BE;\n                break;\n            }\n            case STRINGS.UTF16LE_BOM[0]: {\n                this.state = State.BOM16LE;\n                break;\n            }\n            case STRINGS.UTF8_BOM[0]: {\n                this.sectionIndex = 1;\n                this.state = State.BOM8;\n                break;\n            }\n            case Chars.NIL: {\n                this.state = State.UTF16BE_XML_PREFIX;\n                this.sectionIndex = 1;\n                break;\n            }\n            case Chars.LT: {\n                this.state = State.BeginLT;\n                break;\n            }\n            default: {\n                this.state = State.BeforeTag;\n            }\n        }\n    }\n    stateBeginLT(c) {\n        if (c === Chars.NIL) {\n            this.state = State.UTF16LE_XML_PREFIX;\n            this.sectionIndex = 2;\n        }\n        else if (c === Chars.QUESTION) {\n            this.state = State.XMLDeclaration;\n            this.sectionIndex = 2;\n        }\n        else {\n            this.state = State.BeforeTagName;\n            this.stateBeforeTagName(c);\n        }\n    }\n    stateUTF16BE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16BE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16BE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16be\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateUTF16LE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16LE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16LE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16le\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16LE(c) {\n        if (c === STRINGS.UTF16LE_BOM[1]) {\n            this.setResult(\"utf-16le\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16BE(c) {\n        if (c === STRINGS.UTF16BE_BOM[1]) {\n            this.setResult(\"utf-16be\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM8(c) {\n        if (this.advanceSection(STRINGS.UTF8_BOM, c) &&\n            this.sectionIndex === STRINGS.UTF8_BOM.length) {\n            this.setResult(\"utf-8\", ResultType.BOM);\n        }\n    }\n    stateBeforeTag(c) {\n        if (c === Chars.LT) {\n            this.state = State.BeforeTagName;\n            this.inMetaTag = false;\n        }\n    }\n    /**\n     * We have seen a `<`, and now have to figure out what to do.\n     *\n     * Options:\n     *  - `<meta`\n     *  - Any other tag\n     *  - A closing tag\n     *  - `<!--`\n     *  - An XML declaration\n     *\n     */\n    stateBeforeTagName(c) {\n        if (isAsciiAlpha(c)) {\n            if ((c | 0x20) === STRINGS.META[0]) {\n                this.sectionIndex = 1;\n                this.state = State.TagNameMeta;\n            }\n            else {\n                this.state = State.TagNameOther;\n            }\n        }\n        else\n            switch (c) {\n                case Chars.SLASH: {\n                    this.state = State.BeforeCloseTagName;\n                    break;\n                }\n                case Chars.EXCLAMATION: {\n                    this.state = State.CommentStart;\n                    this.sectionIndex = 2;\n                    break;\n                }\n                case Chars.QUESTION: {\n                    this.state = State.WeirdTag;\n                    break;\n                }\n                default: {\n                    this.state = State.BeforeTag;\n                    this.stateBeforeTag(c);\n                }\n            }\n    }\n    stateBeforeCloseTagName(c) {\n        this.state = isAsciiAlpha(c)\n            ? // Switch to `TagNameOther`; the HTML spec allows attributes here as well.\n                State.TagNameOther\n            : State.WeirdTag;\n    }\n    stateCommentStart(c) {\n        if (this.advanceSection(STRINGS.COMMENT_START, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_START.length) {\n                this.state = State.CommentEnd;\n                // The -- of the comment start can be part of the end.\n                this.sectionIndex = 2;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateCommentEnd(c) {\n        if (this.advanceSection(STRINGS.COMMENT_END, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_END.length) {\n                this.state = State.BeforeTag;\n            }\n        }\n        else if (c === Chars.DASH) {\n            /*\n             * If we are here, we know we expected a `>` above.\n             * Set this to 2, to support many dashes before the closing `>`.\n             */\n            this.sectionIndex = 2;\n        }\n    }\n    /**\n     * Any section starting with `<!`, `<?`, `</`, without being a closing tag or comment.\n     */\n    stateWeirdTag(c) {\n        if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    /**\n     * Advances the section, ignoring upper/lower case.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSectionIC(section, c) {\n        return this.advanceSection(section, c | 0x20);\n    }\n    /**\n     * Advances the section.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSection(section, c) {\n        if (section[this.sectionIndex] === c) {\n            this.sectionIndex++;\n            return true;\n        }\n        this.sectionIndex = 0;\n        return false;\n    }\n    stateTagNameMeta(c) {\n        if (this.sectionIndex < STRINGS.META.length) {\n            if (this.advanceSectionIC(STRINGS.META, c)) {\n                return;\n            }\n        }\n        else if (SPACE_CHARACTERS.has(c)) {\n            this.inMetaTag = true;\n            this.gotPragma = null;\n            this.needsPragma = null;\n            this.state = State.BeforeAttribute;\n            return;\n        }\n        this.state = State.TagNameOther;\n        // Reconsume in case there is a `>`.\n        this.stateTagNameOther(c);\n    }\n    stateTagNameOther(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    stateBeforeAttribute(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (this.inMetaTag) {\n            const lower = c | 0x20;\n            if (lower === STRINGS.HTTP_EQUIV[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribHttpEquiv;\n                return;\n            }\n            else if (lower === STRINGS.CHARSET[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribC;\n                return;\n            }\n        }\n        this.state =\n            c === Chars.SLASH || c === Chars.GT\n                ? State.BeforeTag\n                : State.AnyAttribName;\n    }\n    handleMetaAttrib(c, section, type) {\n        if (this.advanceSectionIC(section, c)) {\n            if (this.sectionIndex === section.length) {\n                this.attribType = type;\n                this.state = State.MetaAttribAfterName;\n            }\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribHttpEquiv(c) {\n        this.handleMetaAttrib(c, STRINGS.HTTP_EQUIV, AttribType.HttpEquiv);\n    }\n    stateMetaAttribC(c) {\n        const lower = c | 0x20;\n        if (lower === STRINGS.CHARSET[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribCharset;\n        }\n        else if (lower === STRINGS.CONTENT[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribContent;\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribCharset(c) {\n        this.handleMetaAttrib(c, STRINGS.CHARSET, AttribType.Charset);\n    }\n    stateMetaAttribContent(c) {\n        this.handleMetaAttrib(c, STRINGS.CONTENT, AttribType.Content);\n    }\n    stateMetaAttribAfterName(c) {\n        if (SPACE_CHARACTERS.has(c) || c === Chars.EQUALS) {\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateAnyAttribName(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.attribType = AttribType.None;\n            this.state = State.AfterAttributeName;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else {\n            this.state = State.BeforeAttribute;\n            this.stateBeforeAttribute(c);\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        this.attributeValue.length = 0;\n        this.sectionIndex = 0;\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state =\n                this.attribType === AttribType.Content\n                    ? State.MetaContentValueQuotedBeforeEncoding\n                    : this.attribType === AttribType.HttpEquiv\n                        ? State.MetaAttribHttpEquivValue\n                        : State.AttributeValueQuoted;\n        }\n        else if (this.attribType === AttribType.Content) {\n            this.state = State.MetaContentValueUnquotedBeforeEncoding;\n            this.stateMetaContentValueUnquotedBeforeEncoding(c);\n        }\n        else if (this.attribType === AttribType.HttpEquiv) {\n            // We use `quoteCharacter = 0` to signify that the value is unquoted.\n            this.quoteCharacter = 0;\n            this.sectionIndex = 0;\n            this.state = State.MetaAttribHttpEquivValue;\n            this.stateMetaAttribHttpEquivValue(c);\n        }\n        else {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n    }\n    // The value has to be `content-type`\n    stateMetaAttribHttpEquivValue(c) {\n        if (this.sectionIndex === STRINGS.CONTENT_TYPE.length) {\n            if (this.quoteCharacter === 0\n                ? END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)\n                : c === this.quoteCharacter) {\n                if (this.needsPragma !== null) {\n                    this.setResult(this.needsPragma, ResultType.META_TAG);\n                }\n                else if (this.gotPragma === null) {\n                    this.gotPragma = true;\n                }\n                this.state = State.BeforeAttribute;\n                return;\n            }\n        }\n        else if (this.advanceSectionIC(STRINGS.CONTENT_TYPE, c)) {\n            return;\n        }\n        this.gotPragma = false;\n        if (this.quoteCharacter === 0) {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n    }\n    handleMetaContentValue() {\n        if (this.attributeValue.length === 0)\n            return;\n        const encoding = String.fromCharCode(...this.attributeValue);\n        if (this.gotPragma) {\n            this.setResult(encoding, ResultType.META_TAG);\n        }\n        else if (this.needsPragma === null) {\n            // Don't override a previous result.\n            this.needsPragma = encoding;\n        }\n        this.attributeValue.length = 0;\n    }\n    handleAttributeValue() {\n        if (this.attribType === AttribType.Charset) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.META_TAG);\n        }\n    }\n    stateAttributeValueUnquoted(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.handleAttributeValue();\n            this.state = State.BeforeTag;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    findMetaContentEncoding(c) {\n        if (this.advanceSectionIC(STRINGS.CHARSET, c)) {\n            if (this.sectionIndex === STRINGS.CHARSET.length) {\n                return true;\n            }\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.CHARSET[0]);\n        }\n        return false;\n    }\n    stateMetaContentValueUnquotedBeforeEncoding(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (this.sectionIndex === STRINGS.CHARSET.length) {\n            if (c === Chars.EQUALS) {\n                this.state = State.MetaContentValueUnquotedBeforeValue;\n            }\n        }\n        else {\n            this.findMetaContentEncoding(c);\n        }\n    }\n    stateMetaContentValueUnquotedBeforeValue(c) {\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state = State.MetaContentValueUnquotedValueQuoted;\n        }\n        else if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Can't have spaces here, as it would no longer be part of the attribute value.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.MetaContentValueUnquotedValueUnquoted;\n            this.stateMetaContentValueUnquotedValueUnquoted(c);\n        }\n    }\n    stateMetaContentValueUnquotedValueQuoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Quotes weren't matched, so we're done.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (c === this.quoteCharacter) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueUnquotedValueUnquoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueUnquoted(c) {\n        if (isQuote(c) || SPACE_CHARACTERS.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            // We are done with the value, but might not be at the end of the attribute\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueQuoted(c) {\n        if (isQuote(c)) {\n            // We have reached the end of our value.\n            if (c !== this.quoteCharacter) {\n                // Only handle the value if inner quotes were matched.\n                this.handleMetaContentValue();\n            }\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedBeforeEncoding(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (this.findMetaContentEncoding(c)) {\n            this.state = State.MetaContentValueQuotedAfterEncoding;\n        }\n    }\n    stateMetaContentValueQuotedAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.MetaContentValueQuotedBeforeValue;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            // Look for the next encoding\n            this.state = State.MetaContentValueQuotedBeforeEncoding;\n            this.stateMetaContentValueQuotedBeforeEncoding(c);\n        }\n    }\n    stateMetaContentValueQuotedBeforeValue(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (isQuote(c)) {\n            this.state = State.MetaContentValueQuotedValueQuoted;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            this.state = State.MetaContentValueQuotedValueUnquoted;\n            this.stateMetaContentValueQuotedValueUnquoted(c);\n        }\n    }\n    stateAttributeValueQuoted(c) {\n        if (c === this.quoteCharacter) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    // Read STRINGS.XML_DECLARATION\n    stateXMLDeclaration(c) {\n        if (this.advanceSection(STRINGS.XML_DECLARATION, c)) {\n            if (this.sectionIndex === STRINGS.XML_DECLARATION.length) {\n                this.sectionIndex = 0;\n                this.state = State.XMLDeclarationBeforeEncoding;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n        }\n    }\n    stateXMLDeclarationBeforeEncoding(c) {\n        if (this.advanceSection(STRINGS.ENCODING, c)) {\n            if (this.sectionIndex === STRINGS.ENCODING.length) {\n                this.state = State.XMLDeclarationAfterEncoding;\n            }\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.ENCODING[0]);\n        }\n    }\n    stateXMLDeclarationAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.XMLDeclarationBeforeValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationBeforeValue(c) {\n        if (isQuote(c)) {\n            this.attributeValue.length = 0;\n            this.state = State.XMLDeclarationValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationValue(c) {\n        if (isQuote(c)) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.XML_ENCODING);\n            this.state = State.WeirdTag;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c <= Chars.SPACE) {\n            this.state = State.WeirdTag;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    write(buffer) {\n        let index = 0;\n        for (; index < buffer.length && this.offset + index < this.maxBytes; index++) {\n            const c = buffer[index];\n            switch (this.state) {\n                case State.Begin: {\n                    this.stateBegin(c);\n                    break;\n                }\n                case State.BOM16BE: {\n                    this.stateBOM16BE(c);\n                    break;\n                }\n                case State.BOM16LE: {\n                    this.stateBOM16LE(c);\n                    break;\n                }\n                case State.BOM8: {\n                    this.stateBOM8(c);\n                    break;\n                }\n                case State.UTF16LE_XML_PREFIX: {\n                    this.stateUTF16LE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeginLT: {\n                    this.stateBeginLT(c);\n                    break;\n                }\n                case State.UTF16BE_XML_PREFIX: {\n                    this.stateUTF16BE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeforeTag: {\n                    // Optimization: Skip all characters until we find a `<`\n                    const idx = buffer.indexOf(Chars.LT, index);\n                    if (idx === -1) {\n                        // We are done with this buffer. Stay in the state and try on the next one.\n                        index = buffer.length;\n                    }\n                    else {\n                        index = idx;\n                        this.stateBeforeTag(Chars.LT);\n                    }\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.BeforeCloseTagName: {\n                    this.stateBeforeCloseTagName(c);\n                    break;\n                }\n                case State.CommentStart: {\n                    this.stateCommentStart(c);\n                    break;\n                }\n                case State.CommentEnd: {\n                    this.stateCommentEnd(c);\n                    break;\n                }\n                case State.TagNameMeta: {\n                    this.stateTagNameMeta(c);\n                    break;\n                }\n                case State.TagNameOther: {\n                    this.stateTagNameOther(c);\n                    break;\n                }\n                case State.XMLDeclaration: {\n                    this.stateXMLDeclaration(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeEncoding: {\n                    this.stateXMLDeclarationBeforeEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationAfterEncoding: {\n                    this.stateXMLDeclarationAfterEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeValue: {\n                    this.stateXMLDeclarationBeforeValue(c);\n                    break;\n                }\n                case State.XMLDeclarationValue: {\n                    this.stateXMLDeclarationValue(c);\n                    break;\n                }\n                case State.WeirdTag: {\n                    this.stateWeirdTag(c);\n                    break;\n                }\n                case State.BeforeAttribute: {\n                    this.stateBeforeAttribute(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquiv: {\n                    this.stateMetaAttribHttpEquiv(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquivValue: {\n                    this.stateMetaAttribHttpEquivValue(c);\n                    break;\n                }\n                case State.MetaAttribC: {\n                    this.stateMetaAttribC(c);\n                    break;\n                }\n                case State.MetaAttribContent: {\n                    this.stateMetaAttribContent(c);\n                    break;\n                }\n                case State.MetaAttribCharset: {\n                    this.stateMetaAttribCharset(c);\n                    break;\n                }\n                case State.MetaAttribAfterName: {\n                    this.stateMetaAttribAfterName(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeEncoding: {\n                    this.stateMetaContentValueQuotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedAfterEncoding: {\n                    this.stateMetaContentValueQuotedAfterEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeValue: {\n                    this.stateMetaContentValueQuotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueQuoted: {\n                    this.stateMetaContentValueQuotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueUnquoted: {\n                    this.stateMetaContentValueQuotedValueUnquoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeEncoding: {\n                    this.stateMetaContentValueUnquotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeValue: {\n                    this.stateMetaContentValueUnquotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueQuoted: {\n                    this.stateMetaContentValueUnquotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueUnquoted: {\n                    this.stateMetaContentValueUnquotedValueUnquoted(c);\n                    break;\n                }\n                case State.AnyAttribName: {\n                    this.stateAnyAttribName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.AttributeValueQuoted: {\n                    this.stateAttributeValueQuoted(c);\n                    break;\n                }\n                case State.AttributeValueUnquoted: {\n                    this.stateAttributeValueUnquoted(c);\n                    break;\n                }\n            }\n        }\n        this.offset += index;\n    }\n}\n/** Get the encoding for the passed buffer. */\nfunction getEncoding(buffer, options) {\n    const sniffer = new Sniffer(options);\n    sniffer.write(buffer);\n    return sniffer.encoding;\n}\n//# sourceMappingURL=sniffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZW5jb2Rpbmctc25pZmZlci9kaXN0L2VzbS9zbmlmZmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNCQUFzQjtBQUNoQjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxzQkFBc0I7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLDZCQUE2Qiw0REFBVztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwrRUFBK0UsSUFBSTtBQUNyRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSw4REFBOEQ7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pY29uLWdlbmVyYXRvci8uL25vZGVfbW9kdWxlcy9lbmNvZGluZy1zbmlmZmVyL2Rpc3QvZXNtL3NuaWZmZXIuanM/MDYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBsYWJlbFRvTmFtZSB9IGZyb20gXCJ3aGF0d2ctZW5jb2RpbmdcIjtcbi8vIGh0dHBzOi8vaHRtbC5zcGVjLndoYXR3Zy5vcmcvbXVsdGlwYWdlL3N5bnRheC5odG1sI3ByZXNjYW4tYS1ieXRlLXN0cmVhbS10by1kZXRlcm1pbmUtaXRzLWVuY29kaW5nXG52YXIgU3RhdGU7XG4oZnVuY3Rpb24gKFN0YXRlKSB7XG4gICAgLy8gQmVmb3JlIGFueXRoaW5nIHN0YXJ0czsgY2FuIGJlIGFueSBvZiBCT00sIFVURi0xNiBYTUwgZGVjbGFyYXRpb25zIG9yIG1ldGEgdGFnc1xuICAgIFN0YXRlW1N0YXRlW1wiQmVnaW5cIl0gPSAwXSA9IFwiQmVnaW5cIjtcbiAgICAvLyBJbnNpZGUgb2YgYSBCT01cbiAgICBTdGF0ZVtTdGF0ZVtcIkJPTTE2QkVcIl0gPSAxXSA9IFwiQk9NMTZCRVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiQk9NMTZMRVwiXSA9IDJdID0gXCJCT00xNkxFXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJCT004XCJdID0gM10gPSBcIkJPTThcIjtcbiAgICAvLyBYTUwgcHJlZml4XG4gICAgU3RhdGVbU3RhdGVbXCJVVEYxNkxFX1hNTF9QUkVGSVhcIl0gPSA0XSA9IFwiVVRGMTZMRV9YTUxfUFJFRklYXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJCZWdpbkxUXCJdID0gNV0gPSBcIkJlZ2luTFRcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIlVURjE2QkVfWE1MX1BSRUZJWFwiXSA9IDZdID0gXCJVVEYxNkJFX1hNTF9QUkVGSVhcIjtcbiAgICAvLyBXYWl0aW5nIGZvciBvcGVuaW5nIGA8YFxuICAgIFN0YXRlW1N0YXRlW1wiQmVmb3JlVGFnXCJdID0gN10gPSBcIkJlZm9yZVRhZ1wiO1xuICAgIC8vIEFmdGVyIHRoZSBvcGVuaW5nIGA8YFxuICAgIFN0YXRlW1N0YXRlW1wiQmVmb3JlVGFnTmFtZVwiXSA9IDhdID0gXCJCZWZvcmVUYWdOYW1lXCI7XG4gICAgLy8gQWZ0ZXIgYDwvYFxuICAgIFN0YXRlW1N0YXRlW1wiQmVmb3JlQ2xvc2VUYWdOYW1lXCJdID0gOV0gPSBcIkJlZm9yZUNsb3NlVGFnTmFtZVwiO1xuICAgIC8vIEJlZ2lubmluZyBvZiBhIGNvbW1lbnRcbiAgICBTdGF0ZVtTdGF0ZVtcIkNvbW1lbnRTdGFydFwiXSA9IDEwXSA9IFwiQ29tbWVudFN0YXJ0XCI7XG4gICAgLy8gRW5kIG9mIGEgY29tbWVudFxuICAgIFN0YXRlW1N0YXRlW1wiQ29tbWVudEVuZFwiXSA9IDExXSA9IFwiQ29tbWVudEVuZFwiO1xuICAgIC8vIEEgdGFnIG5hbWUgdGhhdCBjb3VsZCBiZSBgbWV0YWBcbiAgICBTdGF0ZVtTdGF0ZVtcIlRhZ05hbWVNZXRhXCJdID0gMTJdID0gXCJUYWdOYW1lTWV0YVwiO1xuICAgIC8vIEEgdGFnIG5hbWUgdGhhdCBpcyBub3QgYG1ldGFgXG4gICAgU3RhdGVbU3RhdGVbXCJUYWdOYW1lT3RoZXJcIl0gPSAxM10gPSBcIlRhZ05hbWVPdGhlclwiO1xuICAgIC8vIFhNTCBkZWNsYXJhdGlvblxuICAgIFN0YXRlW1N0YXRlW1wiWE1MRGVjbGFyYXRpb25cIl0gPSAxNF0gPSBcIlhNTERlY2xhcmF0aW9uXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJYTUxEZWNsYXJhdGlvbkJlZm9yZUVuY29kaW5nXCJdID0gMTVdID0gXCJYTUxEZWNsYXJhdGlvbkJlZm9yZUVuY29kaW5nXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJYTUxEZWNsYXJhdGlvbkFmdGVyRW5jb2RpbmdcIl0gPSAxNl0gPSBcIlhNTERlY2xhcmF0aW9uQWZ0ZXJFbmNvZGluZ1wiO1xuICAgIFN0YXRlW1N0YXRlW1wiWE1MRGVjbGFyYXRpb25CZWZvcmVWYWx1ZVwiXSA9IDE3XSA9IFwiWE1MRGVjbGFyYXRpb25CZWZvcmVWYWx1ZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiWE1MRGVjbGFyYXRpb25WYWx1ZVwiXSA9IDE4XSA9IFwiWE1MRGVjbGFyYXRpb25WYWx1ZVwiO1xuICAgIC8vIEFueXRoaW5nIHRoYXQgbG9va3MgbGlrZSBhIHRhZywgYnV0IGRvZXNuJ3QgZml0IGluIHRoZSBhYm92ZSBjYXRlZ29yaWVzXG4gICAgU3RhdGVbU3RhdGVbXCJXZWlyZFRhZ1wiXSA9IDE5XSA9IFwiV2VpcmRUYWdcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIkJlZm9yZUF0dHJpYnV0ZVwiXSA9IDIwXSA9IFwiQmVmb3JlQXR0cmlidXRlXCI7XG4gICAgLypcbiAgICAgKiBBdHRyaWJ1dGVzIGluIG1ldGEgdGFnIOKAlCB3ZSBjb21wYXJlIHRoZW0gdG8gb3VyIHNldCBoZXJlLCBhbmQgYmFjayBvdXRcbiAgICAgKiBXZSBjYXJlIGFib3V0IGZvdXIgYXR0cmlidXRlczogaHR0cC1lcXVpdiwgY29udGVudC10eXBlLCBjb250ZW50LCBjaGFyc2V0XG4gICAgICovXG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQXR0cmliSHR0cEVxdWl2XCJdID0gMjFdID0gXCJNZXRhQXR0cmliSHR0cEVxdWl2XCI7XG4gICAgLy8gVGhlIHZhbHVlIGhhcyB0byBiZSBgY29udGVudC10eXBlYFxuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUF0dHJpYkh0dHBFcXVpdlZhbHVlXCJdID0gMjJdID0gXCJNZXRhQXR0cmliSHR0cEVxdWl2VmFsdWVcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIk1ldGFBdHRyaWJDXCJdID0gMjNdID0gXCJNZXRhQXR0cmliQ1wiO1xuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUF0dHJpYkNvbnRlbnRcIl0gPSAyNF0gPSBcIk1ldGFBdHRyaWJDb250ZW50XCI7XG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQXR0cmliQ2hhcnNldFwiXSA9IDI1XSA9IFwiTWV0YUF0dHJpYkNoYXJzZXRcIjtcbiAgICAvLyBXYWl0aW5nIGZvciB3aGl0ZXNwYWNlXG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQXR0cmliQWZ0ZXJOYW1lXCJdID0gMjZdID0gXCJNZXRhQXR0cmliQWZ0ZXJOYW1lXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQ29udGVudFZhbHVlUXVvdGVkQmVmb3JlRW5jb2RpbmdcIl0gPSAyN10gPSBcIk1ldGFDb250ZW50VmFsdWVRdW90ZWRCZWZvcmVFbmNvZGluZ1wiO1xuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZEFmdGVyRW5jb2RpbmdcIl0gPSAyOF0gPSBcIk1ldGFDb250ZW50VmFsdWVRdW90ZWRBZnRlckVuY29kaW5nXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQ29udGVudFZhbHVlUXVvdGVkQmVmb3JlVmFsdWVcIl0gPSAyOV0gPSBcIk1ldGFDb250ZW50VmFsdWVRdW90ZWRCZWZvcmVWYWx1ZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZFZhbHVlUXVvdGVkXCJdID0gMzBdID0gXCJNZXRhQ29udGVudFZhbHVlUXVvdGVkVmFsdWVRdW90ZWRcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIk1ldGFDb250ZW50VmFsdWVRdW90ZWRWYWx1ZVVucXVvdGVkXCJdID0gMzFdID0gXCJNZXRhQ29udGVudFZhbHVlUXVvdGVkVmFsdWVVbnF1b3RlZFwiO1xuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkQmVmb3JlRW5jb2RpbmdcIl0gPSAzMl0gPSBcIk1ldGFDb250ZW50VmFsdWVVbnF1b3RlZEJlZm9yZUVuY29kaW5nXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJNZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVWYWx1ZVwiXSA9IDMzXSA9IFwiTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkQmVmb3JlVmFsdWVcIjtcbiAgICBTdGF0ZVtTdGF0ZVtcIk1ldGFDb250ZW50VmFsdWVVbnF1b3RlZFZhbHVlUXVvdGVkXCJdID0gMzRdID0gXCJNZXRhQ29udGVudFZhbHVlVW5xdW90ZWRWYWx1ZVF1b3RlZFwiO1xuICAgIFN0YXRlW1N0YXRlW1wiTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkVmFsdWVVbnF1b3RlZFwiXSA9IDM1XSA9IFwiTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkVmFsdWVVbnF1b3RlZFwiO1xuICAgIFN0YXRlW1N0YXRlW1wiQW55QXR0cmliTmFtZVwiXSA9IDM2XSA9IFwiQW55QXR0cmliTmFtZVwiO1xuICAgIC8vIEFmdGVyIHRoZSBuYW1lIG9mIGFuIGF0dHJpYnV0ZSwgYmVmb3JlIHRoZSBlcXVhbHMgc2lnblxuICAgIFN0YXRlW1N0YXRlW1wiQWZ0ZXJBdHRyaWJ1dGVOYW1lXCJdID0gMzddID0gXCJBZnRlckF0dHJpYnV0ZU5hbWVcIjtcbiAgICAvLyBBZnRlciBgPWBcbiAgICBTdGF0ZVtTdGF0ZVtcIkJlZm9yZUF0dHJpYnV0ZVZhbHVlXCJdID0gMzhdID0gXCJCZWZvcmVBdHRyaWJ1dGVWYWx1ZVwiO1xuICAgIFN0YXRlW1N0YXRlW1wiQXR0cmlidXRlVmFsdWVRdW90ZWRcIl0gPSAzOV0gPSBcIkF0dHJpYnV0ZVZhbHVlUXVvdGVkXCI7XG4gICAgU3RhdGVbU3RhdGVbXCJBdHRyaWJ1dGVWYWx1ZVVucXVvdGVkXCJdID0gNDBdID0gXCJBdHRyaWJ1dGVWYWx1ZVVucXVvdGVkXCI7XG59KShTdGF0ZSB8fCAoU3RhdGUgPSB7fSkpO1xuZXhwb3J0IHZhciBSZXN1bHRUeXBlO1xuKGZ1bmN0aW9uIChSZXN1bHRUeXBlKSB7XG4gICAgLy8gQnl0ZSBvcmRlciBtYXJrXG4gICAgUmVzdWx0VHlwZVtSZXN1bHRUeXBlW1wiQk9NXCJdID0gMF0gPSBcIkJPTVwiO1xuICAgIC8vIFVzZXItIG9yIHRyYW5zcG9ydCBsYXllci1kZWZpbmVkXG4gICAgUmVzdWx0VHlwZVtSZXN1bHRUeXBlW1wiUEFTU0VEXCJdID0gMV0gPSBcIlBBU1NFRFwiO1xuICAgIC8vIFhNTCBwcmVmaXhlc1xuICAgIFJlc3VsdFR5cGVbUmVzdWx0VHlwZVtcIlhNTF9QUkVGSVhcIl0gPSAyXSA9IFwiWE1MX1BSRUZJWFwiO1xuICAgIC8vIE1ldGEgdGFnXG4gICAgUmVzdWx0VHlwZVtSZXN1bHRUeXBlW1wiTUVUQV9UQUdcIl0gPSAzXSA9IFwiTUVUQV9UQUdcIjtcbiAgICAvLyBYTUwgZW5jb2RpbmdcbiAgICBSZXN1bHRUeXBlW1Jlc3VsdFR5cGVbXCJYTUxfRU5DT0RJTkdcIl0gPSA0XSA9IFwiWE1MX0VOQ09ESU5HXCI7XG4gICAgLy8gRGVmYXVsdFxuICAgIFJlc3VsdFR5cGVbUmVzdWx0VHlwZVtcIkRFRkFVTFRcIl0gPSA1XSA9IFwiREVGQVVMVFwiO1xufSkoUmVzdWx0VHlwZSB8fCAoUmVzdWx0VHlwZSA9IHt9KSk7XG52YXIgQXR0cmliVHlwZTtcbihmdW5jdGlvbiAoQXR0cmliVHlwZSkge1xuICAgIEF0dHJpYlR5cGVbQXR0cmliVHlwZVtcIk5vbmVcIl0gPSAwXSA9IFwiTm9uZVwiO1xuICAgIEF0dHJpYlR5cGVbQXR0cmliVHlwZVtcIkh0dHBFcXVpdlwiXSA9IDFdID0gXCJIdHRwRXF1aXZcIjtcbiAgICBBdHRyaWJUeXBlW0F0dHJpYlR5cGVbXCJDb250ZW50XCJdID0gMl0gPSBcIkNvbnRlbnRcIjtcbiAgICBBdHRyaWJUeXBlW0F0dHJpYlR5cGVbXCJDaGFyc2V0XCJdID0gM10gPSBcIkNoYXJzZXRcIjtcbn0pKEF0dHJpYlR5cGUgfHwgKEF0dHJpYlR5cGUgPSB7fSkpO1xudmFyIENoYXJzO1xuKGZ1bmN0aW9uIChDaGFycykge1xuICAgIENoYXJzW0NoYXJzW1wiTklMXCJdID0gMF0gPSBcIk5JTFwiO1xuICAgIENoYXJzW0NoYXJzW1wiVEFCXCJdID0gOV0gPSBcIlRBQlwiO1xuICAgIENoYXJzW0NoYXJzW1wiTEZcIl0gPSAxMF0gPSBcIkxGXCI7XG4gICAgQ2hhcnNbQ2hhcnNbXCJDUlwiXSA9IDEzXSA9IFwiQ1JcIjtcbiAgICBDaGFyc1tDaGFyc1tcIlNQQUNFXCJdID0gMzJdID0gXCJTUEFDRVwiO1xuICAgIENoYXJzW0NoYXJzW1wiRVhDTEFNQVRJT05cIl0gPSAzM10gPSBcIkVYQ0xBTUFUSU9OXCI7XG4gICAgQ2hhcnNbQ2hhcnNbXCJEUVVPVEVcIl0gPSAzNF0gPSBcIkRRVU9URVwiO1xuICAgIENoYXJzW0NoYXJzW1wiU1FVT1RFXCJdID0gMzldID0gXCJTUVVPVEVcIjtcbiAgICBDaGFyc1tDaGFyc1tcIkRBU0hcIl0gPSA0NV0gPSBcIkRBU0hcIjtcbiAgICBDaGFyc1tDaGFyc1tcIlNMQVNIXCJdID0gNDddID0gXCJTTEFTSFwiO1xuICAgIENoYXJzW0NoYXJzW1wiU0VNSUNPTE9OXCJdID0gNTldID0gXCJTRU1JQ09MT05cIjtcbiAgICBDaGFyc1tDaGFyc1tcIkxUXCJdID0gNjBdID0gXCJMVFwiO1xuICAgIENoYXJzW0NoYXJzW1wiRVFVQUxTXCJdID0gNjFdID0gXCJFUVVBTFNcIjtcbiAgICBDaGFyc1tDaGFyc1tcIkdUXCJdID0gNjJdID0gXCJHVFwiO1xuICAgIENoYXJzW0NoYXJzW1wiUVVFU1RJT05cIl0gPSA2M10gPSBcIlFVRVNUSU9OXCI7XG4gICAgQ2hhcnNbQ2hhcnNbXCJVcHBlckFcIl0gPSA2NV0gPSBcIlVwcGVyQVwiO1xuICAgIENoYXJzW0NoYXJzW1wiVXBwZXJaXCJdID0gOTBdID0gXCJVcHBlclpcIjtcbiAgICBDaGFyc1tDaGFyc1tcIkxvd2VyQVwiXSA9IDk3XSA9IFwiTG93ZXJBXCI7XG4gICAgQ2hhcnNbQ2hhcnNbXCJMb3dlclpcIl0gPSAxMjJdID0gXCJMb3dlclpcIjtcbn0pKENoYXJzIHx8IChDaGFycyA9IHt9KSk7XG5jb25zdCBTUEFDRV9DSEFSQUNURVJTID0gbmV3IFNldChbQ2hhcnMuU1BBQ0UsIENoYXJzLkxGLCBDaGFycy5DUiwgQ2hhcnMuVEFCXSk7XG5jb25zdCBFTkRfT0ZfVU5RVU9URURfQVRUUklCVVRFX1ZBTFVFID0gbmV3IFNldChbXG4gICAgQ2hhcnMuU1BBQ0UsXG4gICAgQ2hhcnMuTEYsXG4gICAgQ2hhcnMuQ1IsXG4gICAgQ2hhcnMuVEFCLFxuICAgIENoYXJzLkdULFxuXSk7XG5mdW5jdGlvbiB0b1VpbnQ4QXJyYXkoc3RyKSB7XG4gICAgY29uc3QgYXJyID0gbmV3IFVpbnQ4QXJyYXkoc3RyLmxlbmd0aCk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgYXJyW2ldID0gc3RyLmNoYXJDb2RlQXQoaSk7XG4gICAgfVxuICAgIHJldHVybiBhcnI7XG59XG5leHBvcnQgY29uc3QgU1RSSU5HUyA9IHtcbiAgICBVVEY4X0JPTTogbmV3IFVpbnQ4QXJyYXkoWzB4ZWYsIDB4YmIsIDB4YmZdKSxcbiAgICBVVEYxNkxFX0JPTTogbmV3IFVpbnQ4QXJyYXkoWzB4ZmYsIDB4ZmVdKSxcbiAgICBVVEYxNkJFX0JPTTogbmV3IFVpbnQ4QXJyYXkoWzB4ZmUsIDB4ZmZdKSxcbiAgICBVVEYxNkxFX1hNTF9QUkVGSVg6IG5ldyBVaW50OEFycmF5KFsweDNjLCAweDAsIDB4M2YsIDB4MCwgMHg3OCwgMHgwXSksXG4gICAgVVRGMTZCRV9YTUxfUFJFRklYOiBuZXcgVWludDhBcnJheShbMHgwLCAweDNjLCAweDAsIDB4M2YsIDB4MCwgMHg3OF0pLFxuICAgIFhNTF9ERUNMQVJBVElPTjogdG9VaW50OEFycmF5KFwiPD94bWxcIiksXG4gICAgRU5DT0RJTkc6IHRvVWludDhBcnJheShcImVuY29kaW5nXCIpLFxuICAgIE1FVEE6IHRvVWludDhBcnJheShcIm1ldGFcIiksXG4gICAgSFRUUF9FUVVJVjogdG9VaW50OEFycmF5KFwiaHR0cC1lcXVpdlwiKSxcbiAgICBDT05URU5UOiB0b1VpbnQ4QXJyYXkoXCJjb250ZW50XCIpLFxuICAgIENPTlRFTlRfVFlQRTogdG9VaW50OEFycmF5KFwiY29udGVudC10eXBlXCIpLFxuICAgIENIQVJTRVQ6IHRvVWludDhBcnJheShcImNoYXJzZXRcIiksXG4gICAgQ09NTUVOVF9TVEFSVDogdG9VaW50OEFycmF5KFwiPCEtLVwiKSxcbiAgICBDT01NRU5UX0VORDogdG9VaW50OEFycmF5KFwiLS0+XCIpLFxufTtcbmZ1bmN0aW9uIGlzQXNjaWlBbHBoYShjKSB7XG4gICAgcmV0dXJuICgoYyA+PSBDaGFycy5VcHBlckEgJiYgYyA8PSBDaGFycy5VcHBlclopIHx8XG4gICAgICAgIChjID49IENoYXJzLkxvd2VyQSAmJiBjIDw9IENoYXJzLkxvd2VyWikpO1xufVxuZnVuY3Rpb24gaXNRdW90ZShjKSB7XG4gICAgcmV0dXJuIGMgPT09IENoYXJzLkRRVU9URSB8fCBjID09PSBDaGFycy5TUVVPVEU7XG59XG5leHBvcnQgY2xhc3MgU25pZmZlciB7XG4gICAgc2V0UmVzdWx0KGxhYmVsLCB0eXBlKSB7XG4gICAgICAgIGlmICh0aGlzLnJlc3VsdFR5cGUgPT09IFJlc3VsdFR5cGUuREVGQVVMVCB8fCB0aGlzLnJlc3VsdFR5cGUgPiB0eXBlKSB7XG4gICAgICAgICAgICBjb25zdCBlbmNvZGluZyA9IGxhYmVsVG9OYW1lKGxhYmVsKTtcbiAgICAgICAgICAgIGlmIChlbmNvZGluZykge1xuICAgICAgICAgICAgICAgIHRoaXMuZW5jb2RpbmcgPVxuICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiB3ZSBhcmUgaW4gYSBtZXRhIHRhZyBhbmQgdGhlIGVuY29kaW5nIGlzIGB4LXVzZXItZGVmaW5lZGBcbiAgICAgICAgICAgICAgICAgICAgdHlwZSA9PT0gUmVzdWx0VHlwZS5NRVRBX1RBRyAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgZW5jb2RpbmcgPT09IFwieC11c2VyLWRlZmluZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcIndpbmRvd3MtMTI1MlwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IC8vIENoZWNrIGlmIHdlIGFyZSBpbiBhIG1ldGEgdGFnIG9yIHhtbCBkZWNsYXJhdGlvbiwgYW5kIHRoZSBlbmNvZGluZyBpcyBVVEYtMTZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAodHlwZSA9PT0gUmVzdWx0VHlwZS5NRVRBX1RBRyB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlID09PSBSZXN1bHRUeXBlLlhNTF9FTkNPRElORykgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGVuY29kaW5nID09PSBcIlVURi0xNkxFXCIgfHwgZW5jb2RpbmcgPT09IFwiVVRGLTE2QkVcIilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIlVURi04XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBlbmNvZGluZztcbiAgICAgICAgICAgICAgICB0aGlzLnJlc3VsdFR5cGUgPSB0eXBlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKHsgbWF4Qnl0ZXMgPSAxMDI0LCB1c2VyRW5jb2RpbmcsIHRyYW5zcG9ydExheWVyRW5jb2RpbmdMYWJlbCwgZGVmYXVsdEVuY29kaW5nLCB9ID0ge30pIHtcbiAgICAgICAgLyoqIFRoZSBvZmZzZXQgb2YgdGhlIHByZXZpb3VzIGJ1ZmZlcnMuICovXG4gICAgICAgIHRoaXMub2Zmc2V0ID0gMDtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZ2luO1xuICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDA7XG4gICAgICAgIHRoaXMuYXR0cmliVHlwZSA9IEF0dHJpYlR5cGUuTm9uZTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIEluZGljYXRlcyBpZiB0aGUgYGh0dHAtZXF1aXZgIGlzIGBjb250ZW50LXR5cGVgLlxuICAgICAgICAgKlxuICAgICAgICAgKiBJbml0aWFsbHkgYG51bGxgLCBhIGJvb2xlYW4gd2hlbiBhIHZhbHVlIGlzIGZvdW5kLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5nb3RQcmFnbWEgPSBudWxsO1xuICAgICAgICB0aGlzLm5lZWRzUHJhZ21hID0gbnVsbDtcbiAgICAgICAgdGhpcy5pbk1ldGFUYWcgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5lbmNvZGluZyA9IFwid2luZG93cy0xMjUyXCI7XG4gICAgICAgIHRoaXMucmVzdWx0VHlwZSA9IFJlc3VsdFR5cGUuREVGQVVMVDtcbiAgICAgICAgdGhpcy5xdW90ZUNoYXJhY3RlciA9IDA7XG4gICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUgPSBbXTtcbiAgICAgICAgdGhpcy5tYXhCeXRlcyA9IG1heEJ5dGVzO1xuICAgICAgICBpZiAodXNlckVuY29kaW5nKSB7XG4gICAgICAgICAgICB0aGlzLnNldFJlc3VsdCh1c2VyRW5jb2RpbmcsIFJlc3VsdFR5cGUuUEFTU0VEKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHJhbnNwb3J0TGF5ZXJFbmNvZGluZ0xhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLnNldFJlc3VsdCh0cmFuc3BvcnRMYXllckVuY29kaW5nTGFiZWwsIFJlc3VsdFR5cGUuUEFTU0VEKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGVmYXVsdEVuY29kaW5nKSB7XG4gICAgICAgICAgICB0aGlzLnNldFJlc3VsdChkZWZhdWx0RW5jb2RpbmcsIFJlc3VsdFR5cGUuREVGQVVMVCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWdpbihjKSB7XG4gICAgICAgIHN3aXRjaCAoYykge1xuICAgICAgICAgICAgY2FzZSBTVFJJTkdTLlVURjE2QkVfQk9NWzBdOiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJPTTE2QkU7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlIFNUUklOR1MuVVRGMTZMRV9CT01bMF06IHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQk9NMTZMRTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgU1RSSU5HUy5VVEY4X0JPTVswXToge1xuICAgICAgICAgICAgICAgIHRoaXMuc2VjdGlvbkluZGV4ID0gMTtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQk9NODtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgQ2hhcnMuTklMOiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLlVURjE2QkVfWE1MX1BSRUZJWDtcbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDE7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXNlIENoYXJzLkxUOiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZ2luTFQ7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJlZ2luTFQoYykge1xuICAgICAgICBpZiAoYyA9PT0gQ2hhcnMuTklMKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuVVRGMTZMRV9YTUxfUFJFRklYO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAyO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IENoYXJzLlFVRVNUSU9OKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuWE1MRGVjbGFyYXRpb247XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDI7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVUYWdOYW1lKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlVVRGMTZCRV9YTUxfUFJFRklYKGMpIHtcbiAgICAgICAgLy8gQWR2YW5jZSBwb3NpdGlvbiBpbiB0aGUgc2VjdGlvblxuICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbihTVFJJTkdTLlVURjE2QkVfWE1MX1BSRUZJWCwgYykpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnNlY3Rpb25JbmRleCA9PT0gU1RSSU5HUy5VVEYxNkJFX1hNTF9QUkVGSVgubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgLy8gV2UgaGF2ZSB0aGUgd2hvbGUgcHJlZml4XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRSZXN1bHQoXCJ1dGYtMTZiZVwiLCBSZXN1bHRUeXBlLlhNTF9QUkVGSVgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZztcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVUYWcoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVVVEYxNkxFX1hNTF9QUkVGSVgoYykge1xuICAgICAgICAvLyBBZHZhbmNlIHBvc2l0aW9uIGluIHRoZSBzZWN0aW9uXG4gICAgICAgIGlmICh0aGlzLmFkdmFuY2VTZWN0aW9uKFNUUklOR1MuVVRGMTZMRV9YTUxfUFJFRklYLCBjKSkge1xuICAgICAgICAgICAgaWYgKHRoaXMuc2VjdGlvbkluZGV4ID09PSBTVFJJTkdTLlVURjE2TEVfWE1MX1BSRUZJWC5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAvLyBXZSBoYXZlIHRoZSB3aG9sZSBwcmVmaXhcbiAgICAgICAgICAgICAgICB0aGlzLnNldFJlc3VsdChcInV0Zi0xNmxlXCIsIFJlc3VsdFR5cGUuWE1MX1BSRUZJWCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZyhjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJPTTE2TEUoYykge1xuICAgICAgICBpZiAoYyA9PT0gU1RSSU5HUy5VVEYxNkxFX0JPTVsxXSkge1xuICAgICAgICAgICAgdGhpcy5zZXRSZXN1bHQoXCJ1dGYtMTZsZVwiLCBSZXN1bHRUeXBlLkJPTSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZyhjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJPTTE2QkUoYykge1xuICAgICAgICBpZiAoYyA9PT0gU1RSSU5HUy5VVEYxNkJFX0JPTVsxXSkge1xuICAgICAgICAgICAgdGhpcy5zZXRSZXN1bHQoXCJ1dGYtMTZiZVwiLCBSZXN1bHRUeXBlLkJPTSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZyhjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJPTTgoYykge1xuICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbihTVFJJTkdTLlVURjhfQk9NLCBjKSAmJlxuICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPT09IFNUUklOR1MuVVRGOF9CT00ubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aGlzLnNldFJlc3VsdChcInV0Zi04XCIsIFJlc3VsdFR5cGUuQk9NKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUJlZm9yZVRhZyhjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFycy5MVCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZ05hbWU7XG4gICAgICAgICAgICB0aGlzLmluTWV0YVRhZyA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdlIGhhdmUgc2VlbiBhIGA8YCwgYW5kIG5vdyBoYXZlIHRvIGZpZ3VyZSBvdXQgd2hhdCB0byBkby5cbiAgICAgKlxuICAgICAqIE9wdGlvbnM6XG4gICAgICogIC0gYDxtZXRhYFxuICAgICAqICAtIEFueSBvdGhlciB0YWdcbiAgICAgKiAgLSBBIGNsb3NpbmcgdGFnXG4gICAgICogIC0gYDwhLS1gXG4gICAgICogIC0gQW4gWE1MIGRlY2xhcmF0aW9uXG4gICAgICpcbiAgICAgKi9cbiAgICBzdGF0ZUJlZm9yZVRhZ05hbWUoYykge1xuICAgICAgICBpZiAoaXNBc2NpaUFscGhhKGMpKSB7XG4gICAgICAgICAgICBpZiAoKGMgfCAweDIwKSA9PT0gU1RSSU5HUy5NRVRBWzBdKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAxO1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UYWdOYW1lTWV0YTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5UYWdOYW1lT3RoZXI7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgc3dpdGNoIChjKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBDaGFycy5TTEFTSDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQ2xvc2VUYWdOYW1lO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBDaGFycy5FWENMQU1BVElPTjoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQ29tbWVudFN0YXJ0O1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDI7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIENoYXJzLlFVRVNUSU9OOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5XZWlyZFRhZztcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZztcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZyhjKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlQmVmb3JlQ2xvc2VUYWdOYW1lKGMpIHtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IGlzQXNjaWlBbHBoYShjKVxuICAgICAgICAgICAgPyAvLyBTd2l0Y2ggdG8gYFRhZ05hbWVPdGhlcmA7IHRoZSBIVE1MIHNwZWMgYWxsb3dzIGF0dHJpYnV0ZXMgaGVyZSBhcyB3ZWxsLlxuICAgICAgICAgICAgICAgIFN0YXRlLlRhZ05hbWVPdGhlclxuICAgICAgICAgICAgOiBTdGF0ZS5XZWlyZFRhZztcbiAgICB9XG4gICAgc3RhdGVDb21tZW50U3RhcnQoYykge1xuICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbihTVFJJTkdTLkNPTU1FTlRfU1RBUlQsIGMpKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5zZWN0aW9uSW5kZXggPT09IFNUUklOR1MuQ09NTUVOVF9TVEFSVC5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQ29tbWVudEVuZDtcbiAgICAgICAgICAgICAgICAvLyBUaGUgLS0gb2YgdGhlIGNvbW1lbnQgc3RhcnQgY2FuIGJlIHBhcnQgb2YgdGhlIGVuZC5cbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDI7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuV2VpcmRUYWc7XG4gICAgICAgICAgICB0aGlzLnN0YXRlV2VpcmRUYWcoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVDb21tZW50RW5kKGMpIHtcbiAgICAgICAgaWYgKHRoaXMuYWR2YW5jZVNlY3Rpb24oU1RSSU5HUy5DT01NRU5UX0VORCwgYykpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnNlY3Rpb25JbmRleCA9PT0gU1RSSU5HUy5DT01NRU5UX0VORC5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IENoYXJzLkRBU0gpIHtcbiAgICAgICAgICAgIC8qXG4gICAgICAgICAgICAgKiBJZiB3ZSBhcmUgaGVyZSwgd2Uga25vdyB3ZSBleHBlY3RlZCBhIGA+YCBhYm92ZS5cbiAgICAgICAgICAgICAqIFNldCB0aGlzIHRvIDIsIHRvIHN1cHBvcnQgbWFueSBkYXNoZXMgYmVmb3JlIHRoZSBjbG9zaW5nIGA+YC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAyO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFueSBzZWN0aW9uIHN0YXJ0aW5nIHdpdGggYDwhYCwgYDw/YCwgYDwvYCwgd2l0aG91dCBiZWluZyBhIGNsb3NpbmcgdGFnIG9yIGNvbW1lbnQuXG4gICAgICovXG4gICAgc3RhdGVXZWlyZFRhZyhjKSB7XG4gICAgICAgIGlmIChjID09PSBDaGFycy5HVCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZztcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBBZHZhbmNlcyB0aGUgc2VjdGlvbiwgaWdub3JpbmcgdXBwZXIvbG93ZXIgY2FzZS5cbiAgICAgKlxuICAgICAqIE1ha2Ugc3VyZSB0aGUgc2VjdGlvbiBoYXMgbGVmdC1vdmVyIGNoYXJhY3RlcnMgYmVmb3JlIGNhbGxpbmcuXG4gICAgICpcbiAgICAgKiBAcmV0dXJucyBgZmFsc2VgIGlmIHdlIGRpZCBub3QgbWF0Y2ggdGhlIHNlY3Rpb24uXG4gICAgICovXG4gICAgYWR2YW5jZVNlY3Rpb25JQyhzZWN0aW9uLCBjKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmFkdmFuY2VTZWN0aW9uKHNlY3Rpb24sIGMgfCAweDIwKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQWR2YW5jZXMgdGhlIHNlY3Rpb24uXG4gICAgICpcbiAgICAgKiBNYWtlIHN1cmUgdGhlIHNlY3Rpb24gaGFzIGxlZnQtb3ZlciBjaGFyYWN0ZXJzIGJlZm9yZSBjYWxsaW5nLlxuICAgICAqXG4gICAgICogQHJldHVybnMgYGZhbHNlYCBpZiB3ZSBkaWQgbm90IG1hdGNoIHRoZSBzZWN0aW9uLlxuICAgICAqL1xuICAgIGFkdmFuY2VTZWN0aW9uKHNlY3Rpb24sIGMpIHtcbiAgICAgICAgaWYgKHNlY3Rpb25bdGhpcy5zZWN0aW9uSW5kZXhdID09PSBjKSB7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCsrO1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAwO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHN0YXRlVGFnTmFtZU1ldGEoYykge1xuICAgICAgICBpZiAodGhpcy5zZWN0aW9uSW5kZXggPCBTVFJJTkdTLk1FVEEubGVuZ3RoKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbklDKFNUUklOR1MuTUVUQSwgYykpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoU1BBQ0VfQ0hBUkFDVEVSUy5oYXMoYykpIHtcbiAgICAgICAgICAgIHRoaXMuaW5NZXRhVGFnID0gdHJ1ZTtcbiAgICAgICAgICAgIHRoaXMuZ290UHJhZ21hID0gbnVsbDtcbiAgICAgICAgICAgIHRoaXMubmVlZHNQcmFnbWEgPSBudWxsO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZUF0dHJpYnV0ZTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuVGFnTmFtZU90aGVyO1xuICAgICAgICAvLyBSZWNvbnN1bWUgaW4gY2FzZSB0aGVyZSBpcyBhIGA+YC5cbiAgICAgICAgdGhpcy5zdGF0ZVRhZ05hbWVPdGhlcihjKTtcbiAgICB9XG4gICAgc3RhdGVUYWdOYW1lT3RoZXIoYykge1xuICAgICAgICBpZiAoU1BBQ0VfQ0hBUkFDVEVSUy5oYXMoYykpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhcnMuR1QpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVUYWc7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWZvcmVBdHRyaWJ1dGUoYykge1xuICAgICAgICBpZiAoU1BBQ0VfQ0hBUkFDVEVSUy5oYXMoYykpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGlmICh0aGlzLmluTWV0YVRhZykge1xuICAgICAgICAgICAgY29uc3QgbG93ZXIgPSBjIHwgMHgyMDtcbiAgICAgICAgICAgIGlmIChsb3dlciA9PT0gU1RSSU5HUy5IVFRQX0VRVUlWWzBdKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAxO1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQXR0cmliSHR0cEVxdWl2O1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGxvd2VyID09PSBTVFJJTkdTLkNIQVJTRVRbMF0pIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDE7XG4gICAgICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLk1ldGFBdHRyaWJDO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXRlID1cbiAgICAgICAgICAgIGMgPT09IENoYXJzLlNMQVNIIHx8IGMgPT09IENoYXJzLkdUXG4gICAgICAgICAgICAgICAgPyBTdGF0ZS5CZWZvcmVUYWdcbiAgICAgICAgICAgICAgICA6IFN0YXRlLkFueUF0dHJpYk5hbWU7XG4gICAgfVxuICAgIGhhbmRsZU1ldGFBdHRyaWIoYywgc2VjdGlvbiwgdHlwZSkge1xuICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbklDKHNlY3Rpb24sIGMpKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5zZWN0aW9uSW5kZXggPT09IHNlY3Rpb24ubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5hdHRyaWJUeXBlID0gdHlwZTtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuTWV0YUF0dHJpYkFmdGVyTmFtZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5BbnlBdHRyaWJOYW1lO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUFueUF0dHJpYk5hbWUoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVNZXRhQXR0cmliSHR0cEVxdWl2KGMpIHtcbiAgICAgICAgdGhpcy5oYW5kbGVNZXRhQXR0cmliKGMsIFNUUklOR1MuSFRUUF9FUVVJViwgQXR0cmliVHlwZS5IdHRwRXF1aXYpO1xuICAgIH1cbiAgICBzdGF0ZU1ldGFBdHRyaWJDKGMpIHtcbiAgICAgICAgY29uc3QgbG93ZXIgPSBjIHwgMHgyMDtcbiAgICAgICAgaWYgKGxvd2VyID09PSBTVFJJTkdTLkNIQVJTRVRbMV0pIHtcbiAgICAgICAgICAgIHRoaXMuc2VjdGlvbkluZGV4ID0gMjtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQXR0cmliQ2hhcnNldDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChsb3dlciA9PT0gU1RSSU5HUy5DT05URU5UWzFdKSB7XG4gICAgICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDI7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuTWV0YUF0dHJpYkNvbnRlbnQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQW55QXR0cmliTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVBbnlBdHRyaWJOYW1lKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlTWV0YUF0dHJpYkNoYXJzZXQoYykge1xuICAgICAgICB0aGlzLmhhbmRsZU1ldGFBdHRyaWIoYywgU1RSSU5HUy5DSEFSU0VULCBBdHRyaWJUeXBlLkNoYXJzZXQpO1xuICAgIH1cbiAgICBzdGF0ZU1ldGFBdHRyaWJDb250ZW50KGMpIHtcbiAgICAgICAgdGhpcy5oYW5kbGVNZXRhQXR0cmliKGMsIFNUUklOR1MuQ09OVEVOVCwgQXR0cmliVHlwZS5Db250ZW50KTtcbiAgICB9XG4gICAgc3RhdGVNZXRhQXR0cmliQWZ0ZXJOYW1lKGMpIHtcbiAgICAgICAgaWYgKFNQQUNFX0NIQVJBQ1RFUlMuaGFzKGMpIHx8IGMgPT09IENoYXJzLkVRVUFMUykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkFmdGVyQXR0cmlidXRlTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVBZnRlckF0dHJpYnV0ZU5hbWUoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQW55QXR0cmliTmFtZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVBbnlBdHRyaWJOYW1lKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlQW55QXR0cmliTmFtZShjKSB7XG4gICAgICAgIGlmIChTUEFDRV9DSEFSQUNURVJTLmhhcyhjKSkge1xuICAgICAgICAgICAgdGhpcy5hdHRyaWJUeXBlID0gQXR0cmliVHlwZS5Ob25lO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkFmdGVyQXR0cmlidXRlTmFtZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBDaGFycy5TTEFTSCB8fCBjID09PSBDaGFycy5HVCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZVRhZztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChjID09PSBDaGFycy5FUVVBTFMpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGVWYWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUFmdGVyQXR0cmlidXRlTmFtZShjKSB7XG4gICAgICAgIGlmIChTUEFDRV9DSEFSQUNURVJTLmhhcyhjKSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgaWYgKGMgPT09IENoYXJzLkVRVUFMUykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZUF0dHJpYnV0ZVZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkJlZm9yZUF0dHJpYnV0ZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVBdHRyaWJ1dGUoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVCZWZvcmVBdHRyaWJ1dGVWYWx1ZShjKSB7XG4gICAgICAgIGlmIChTUEFDRV9DSEFSQUNURVJTLmhhcyhjKSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgdGhpcy5hdHRyaWJ1dGVWYWx1ZS5sZW5ndGggPSAwO1xuICAgICAgICB0aGlzLnNlY3Rpb25JbmRleCA9IDA7XG4gICAgICAgIGlmIChpc1F1b3RlKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnF1b3RlQ2hhcmFjdGVyID0gYztcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPVxuICAgICAgICAgICAgICAgIHRoaXMuYXR0cmliVHlwZSA9PT0gQXR0cmliVHlwZS5Db250ZW50XG4gICAgICAgICAgICAgICAgICAgID8gU3RhdGUuTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZEJlZm9yZUVuY29kaW5nXG4gICAgICAgICAgICAgICAgICAgIDogdGhpcy5hdHRyaWJUeXBlID09PSBBdHRyaWJUeXBlLkh0dHBFcXVpdlxuICAgICAgICAgICAgICAgICAgICAgICAgPyBTdGF0ZS5NZXRhQXR0cmliSHR0cEVxdWl2VmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogU3RhdGUuQXR0cmlidXRlVmFsdWVRdW90ZWQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5hdHRyaWJUeXBlID09PSBBdHRyaWJUeXBlLkNvbnRlbnQpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVFbmNvZGluZztcbiAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVFbmNvZGluZyhjKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLmF0dHJpYlR5cGUgPT09IEF0dHJpYlR5cGUuSHR0cEVxdWl2KSB7XG4gICAgICAgICAgICAvLyBXZSB1c2UgYHF1b3RlQ2hhcmFjdGVyID0gMGAgdG8gc2lnbmlmeSB0aGF0IHRoZSB2YWx1ZSBpcyB1bnF1b3RlZC5cbiAgICAgICAgICAgIHRoaXMucXVvdGVDaGFyYWN0ZXIgPSAwO1xuICAgICAgICAgICAgdGhpcy5zZWN0aW9uSW5kZXggPSAwO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLk1ldGFBdHRyaWJIdHRwRXF1aXZWYWx1ZTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQXR0cmliSHR0cEVxdWl2VmFsdWUoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQXR0cmlidXRlVmFsdWVVbnF1b3RlZDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVBdHRyaWJ1dGVWYWx1ZVVucXVvdGVkKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIFRoZSB2YWx1ZSBoYXMgdG8gYmUgYGNvbnRlbnQtdHlwZWBcbiAgICBzdGF0ZU1ldGFBdHRyaWJIdHRwRXF1aXZWYWx1ZShjKSB7XG4gICAgICAgIGlmICh0aGlzLnNlY3Rpb25JbmRleCA9PT0gU1RSSU5HUy5DT05URU5UX1RZUEUubGVuZ3RoKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5xdW90ZUNoYXJhY3RlciA9PT0gMFxuICAgICAgICAgICAgICAgID8gRU5EX09GX1VOUVVPVEVEX0FUVFJJQlVURV9WQUxVRS5oYXMoYylcbiAgICAgICAgICAgICAgICA6IGMgPT09IHRoaXMucXVvdGVDaGFyYWN0ZXIpIHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5uZWVkc1ByYWdtYSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNldFJlc3VsdCh0aGlzLm5lZWRzUHJhZ21hLCBSZXN1bHRUeXBlLk1FVEFfVEFHKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAodGhpcy5nb3RQcmFnbWEgPT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5nb3RQcmFnbWEgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLmFkdmFuY2VTZWN0aW9uSUMoU1RSSU5HUy5DT05URU5UX1RZUEUsIGMpKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5nb3RQcmFnbWEgPSBmYWxzZTtcbiAgICAgICAgaWYgKHRoaXMucXVvdGVDaGFyYWN0ZXIgPT09IDApIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5BdHRyaWJ1dGVWYWx1ZVVucXVvdGVkO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUF0dHJpYnV0ZVZhbHVlVW5xdW90ZWQoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQXR0cmlidXRlVmFsdWVRdW90ZWQ7XG4gICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVRdW90ZWQoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaGFuZGxlTWV0YUNvbnRlbnRWYWx1ZSgpIHtcbiAgICAgICAgaWYgKHRoaXMuYXR0cmlidXRlVmFsdWUubGVuZ3RoID09PSAwKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBlbmNvZGluZyA9IFN0cmluZy5mcm9tQ2hhckNvZGUoLi4udGhpcy5hdHRyaWJ1dGVWYWx1ZSk7XG4gICAgICAgIGlmICh0aGlzLmdvdFByYWdtYSkge1xuICAgICAgICAgICAgdGhpcy5zZXRSZXN1bHQoZW5jb2RpbmcsIFJlc3VsdFR5cGUuTUVUQV9UQUcpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMubmVlZHNQcmFnbWEgPT09IG51bGwpIHtcbiAgICAgICAgICAgIC8vIERvbid0IG92ZXJyaWRlIGEgcHJldmlvdXMgcmVzdWx0LlxuICAgICAgICAgICAgdGhpcy5uZWVkc1ByYWdtYSA9IGVuY29kaW5nO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUubGVuZ3RoID0gMDtcbiAgICB9XG4gICAgaGFuZGxlQXR0cmlidXRlVmFsdWUoKSB7XG4gICAgICAgIGlmICh0aGlzLmF0dHJpYlR5cGUgPT09IEF0dHJpYlR5cGUuQ2hhcnNldCkge1xuICAgICAgICAgICAgdGhpcy5zZXRSZXN1bHQoU3RyaW5nLmZyb21DaGFyQ29kZSguLi50aGlzLmF0dHJpYnV0ZVZhbHVlKSwgUmVzdWx0VHlwZS5NRVRBX1RBRyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVBdHRyaWJ1dGVWYWx1ZVVucXVvdGVkKGMpIHtcbiAgICAgICAgaWYgKFNQQUNFX0NIQVJBQ1RFUlMuaGFzKGMpKSB7XG4gICAgICAgICAgICB0aGlzLmhhbmRsZUF0dHJpYnV0ZVZhbHVlKCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlQXR0cmlidXRlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPT09IENoYXJzLlNMQVNIIHx8IGMgPT09IENoYXJzLkdUKSB7XG4gICAgICAgICAgICB0aGlzLmhhbmRsZUF0dHJpYnV0ZVZhbHVlKCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQmVmb3JlVGFnO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMuYXR0cmliVHlwZSA9PT0gQXR0cmliVHlwZS5DaGFyc2V0KSB7XG4gICAgICAgICAgICB0aGlzLmF0dHJpYnV0ZVZhbHVlLnB1c2goYyB8IChjID49IDB4NDEgJiYgYyA8PSAweDVhID8gMHgyMCA6IDApKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmaW5kTWV0YUNvbnRlbnRFbmNvZGluZyhjKSB7XG4gICAgICAgIGlmICh0aGlzLmFkdmFuY2VTZWN0aW9uSUMoU1RSSU5HUy5DSEFSU0VULCBjKSkge1xuICAgICAgICAgICAgaWYgKHRoaXMuc2VjdGlvbkluZGV4ID09PSBTVFJJTkdTLkNIQVJTRVQubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBJZiB3ZSBlbmNvdW50ZXJlZCBhbm90aGVyIGBjYCwgYXNzdW1lIHdlIHN0YXJ0ZWQgb3Zlci5cbiAgICAgICAgICAgIHRoaXMuc2VjdGlvbkluZGV4ID0gTnVtYmVyKGMgPT09IFNUUklOR1MuQ0hBUlNFVFswXSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBzdGF0ZU1ldGFDb250ZW50VmFsdWVVbnF1b3RlZEJlZm9yZUVuY29kaW5nKGMpIHtcbiAgICAgICAgaWYgKEVORF9PRl9VTlFVT1RFRF9BVFRSSUJVVEVfVkFMVUUuaGFzKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVVbnF1b3RlZChjKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0aGlzLnNlY3Rpb25JbmRleCA9PT0gU1RSSU5HUy5DSEFSU0VULmxlbmd0aCkge1xuICAgICAgICAgICAgaWYgKGMgPT09IENoYXJzLkVRVUFMUykge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVWYWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuZmluZE1ldGFDb250ZW50RW5jb2RpbmcoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVNZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVWYWx1ZShjKSB7XG4gICAgICAgIGlmIChpc1F1b3RlKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnF1b3RlQ2hhcmFjdGVyID0gYztcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlVW5xdW90ZWRWYWx1ZVF1b3RlZDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChFTkRfT0ZfVU5RVU9URURfQVRUUklCVVRFX1ZBTFVFLmhhcyhjKSkge1xuICAgICAgICAgICAgLy8gQ2FuJ3QgaGF2ZSBzcGFjZXMgaGVyZSwgYXMgaXQgd291bGQgbm8gbG9uZ2VyIGJlIHBhcnQgb2YgdGhlIGF0dHJpYnV0ZSB2YWx1ZS5cbiAgICAgICAgICAgIHRoaXMuc3RhdGVBdHRyaWJ1dGVWYWx1ZVVucXVvdGVkKGMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLk1ldGFDb250ZW50VmFsdWVVbnF1b3RlZFZhbHVlVW5xdW90ZWQ7XG4gICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkVmFsdWVVbnF1b3RlZChjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZU1ldGFDb250ZW50VmFsdWVVbnF1b3RlZFZhbHVlUXVvdGVkKGMpIHtcbiAgICAgICAgaWYgKEVORF9PRl9VTlFVT1RFRF9BVFRSSUJVVEVfVkFMVUUuaGFzKGMpKSB7XG4gICAgICAgICAgICAvLyBRdW90ZXMgd2VyZW4ndCBtYXRjaGVkLCBzbyB3ZSdyZSBkb25lLlxuICAgICAgICAgICAgdGhpcy5zdGF0ZUF0dHJpYnV0ZVZhbHVlVW5xdW90ZWQoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gdGhpcy5xdW90ZUNoYXJhY3Rlcikge1xuICAgICAgICAgICAgdGhpcy5oYW5kbGVNZXRhQ29udGVudFZhbHVlKCk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQXR0cmlidXRlVmFsdWVVbnF1b3RlZDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUucHVzaChjIHwgKGMgPj0gMHg0MSAmJiBjIDw9IDB4NWEgPyAweDIwIDogMCkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkVmFsdWVVbnF1b3RlZChjKSB7XG4gICAgICAgIGlmIChFTkRfT0ZfVU5RVU9URURfQVRUUklCVVRFX1ZBTFVFLmhhcyhjKSB8fCBjID09PSBDaGFycy5TRU1JQ09MT04pIHtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlTWV0YUNvbnRlbnRWYWx1ZSgpO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkF0dHJpYnV0ZVZhbHVlVW5xdW90ZWQ7XG4gICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVVbnF1b3RlZChjKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUucHVzaChjIHwgKGMgPj0gMHg0MSAmJiBjIDw9IDB4NWEgPyAweDIwIDogMCkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZFZhbHVlVW5xdW90ZWQoYykge1xuICAgICAgICBpZiAoaXNRdW90ZShjKSB8fCBTUEFDRV9DSEFSQUNURVJTLmhhcyhjKSB8fCBjID09PSBDaGFycy5TRU1JQ09MT04pIHtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlTWV0YUNvbnRlbnRWYWx1ZSgpO1xuICAgICAgICAgICAgLy8gV2UgYXJlIGRvbmUgd2l0aCB0aGUgdmFsdWUsIGJ1dCBtaWdodCBub3QgYmUgYXQgdGhlIGVuZCBvZiB0aGUgYXR0cmlidXRlXG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuQXR0cmlidXRlVmFsdWVRdW90ZWQ7XG4gICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVRdW90ZWQoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmF0dHJpYnV0ZVZhbHVlLnB1c2goYyB8IChjID49IDB4NDEgJiYgYyA8PSAweDVhID8gMHgyMCA6IDApKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZU1ldGFDb250ZW50VmFsdWVRdW90ZWRWYWx1ZVF1b3RlZChjKSB7XG4gICAgICAgIGlmIChpc1F1b3RlKGMpKSB7XG4gICAgICAgICAgICAvLyBXZSBoYXZlIHJlYWNoZWQgdGhlIGVuZCBvZiBvdXIgdmFsdWUuXG4gICAgICAgICAgICBpZiAoYyAhPT0gdGhpcy5xdW90ZUNoYXJhY3Rlcikge1xuICAgICAgICAgICAgICAgIC8vIE9ubHkgaGFuZGxlIHRoZSB2YWx1ZSBpZiBpbm5lciBxdW90ZXMgd2VyZSBtYXRjaGVkLlxuICAgICAgICAgICAgICAgIHRoaXMuaGFuZGxlTWV0YUNvbnRlbnRWYWx1ZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLkF0dHJpYnV0ZVZhbHVlUXVvdGVkO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUF0dHJpYnV0ZVZhbHVlUXVvdGVkKGMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5hdHRyaWJ1dGVWYWx1ZS5wdXNoKGMgfCAoYyA+PSAweDQxICYmIGMgPD0gMHg1YSA/IDB4MjAgOiAwKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVNZXRhQ29udGVudFZhbHVlUXVvdGVkQmVmb3JlRW5jb2RpbmcoYykge1xuICAgICAgICBpZiAoYyA9PT0gdGhpcy5xdW90ZUNoYXJhY3Rlcikge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZUF0dHJpYnV0ZVZhbHVlUXVvdGVkKGMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHRoaXMuZmluZE1ldGFDb250ZW50RW5jb2RpbmcoYykpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlUXVvdGVkQWZ0ZXJFbmNvZGluZztcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZU1ldGFDb250ZW50VmFsdWVRdW90ZWRBZnRlckVuY29kaW5nKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJzLkVRVUFMUykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLk1ldGFDb250ZW50VmFsdWVRdW90ZWRCZWZvcmVWYWx1ZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghU1BBQ0VfQ0hBUkFDVEVSUy5oYXMoYykpIHtcbiAgICAgICAgICAgIC8vIExvb2sgZm9yIHRoZSBuZXh0IGVuY29kaW5nXG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZEJlZm9yZUVuY29kaW5nO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZU1ldGFDb250ZW50VmFsdWVRdW90ZWRCZWZvcmVFbmNvZGluZyhjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZU1ldGFDb250ZW50VmFsdWVRdW90ZWRCZWZvcmVWYWx1ZShjKSB7XG4gICAgICAgIGlmIChjID09PSB0aGlzLnF1b3RlQ2hhcmFjdGVyKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVRdW90ZWQoYyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNRdW90ZShjKSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLk1ldGFDb250ZW50VmFsdWVRdW90ZWRWYWx1ZVF1b3RlZDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICghU1BBQ0VfQ0hBUkFDVEVSUy5oYXMoYykpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlUXVvdGVkVmFsdWVVbnF1b3RlZDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlUXVvdGVkVmFsdWVVbnF1b3RlZChjKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZUF0dHJpYnV0ZVZhbHVlUXVvdGVkKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IHRoaXMucXVvdGVDaGFyYWN0ZXIpIHtcbiAgICAgICAgICAgIHRoaXMuaGFuZGxlQXR0cmlidXRlVmFsdWUoKTtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5hdHRyaWJUeXBlID09PSBBdHRyaWJUeXBlLkNoYXJzZXQpIHtcbiAgICAgICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUucHVzaChjIHwgKGMgPj0gMHg0MSAmJiBjIDw9IDB4NWEgPyAweDIwIDogMCkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIFJlYWQgU1RSSU5HUy5YTUxfREVDTEFSQVRJT05cbiAgICBzdGF0ZVhNTERlY2xhcmF0aW9uKGMpIHtcbiAgICAgICAgaWYgKHRoaXMuYWR2YW5jZVNlY3Rpb24oU1RSSU5HUy5YTUxfREVDTEFSQVRJT04sIGMpKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5zZWN0aW9uSW5kZXggPT09IFNUUklOR1MuWE1MX0RFQ0xBUkFUSU9OLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2VjdGlvbkluZGV4ID0gMDtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuWE1MRGVjbGFyYXRpb25CZWZvcmVFbmNvZGluZztcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5XZWlyZFRhZztcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0ZVhNTERlY2xhcmF0aW9uQmVmb3JlRW5jb2RpbmcoYykge1xuICAgICAgICBpZiAodGhpcy5hZHZhbmNlU2VjdGlvbihTVFJJTkdTLkVOQ09ESU5HLCBjKSkge1xuICAgICAgICAgICAgaWYgKHRoaXMuc2VjdGlvbkluZGV4ID09PSBTVFJJTkdTLkVOQ09ESU5HLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5YTUxEZWNsYXJhdGlvbkFmdGVyRW5jb2Rpbmc7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhcnMuR1QpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVUYWc7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBJZiB3ZSBlbmNvdW50ZXJlZCBhbm90aGVyIGBjYCwgYXNzdW1lIHdlIHN0YXJ0ZWQgb3Zlci5cbiAgICAgICAgICAgIHRoaXMuc2VjdGlvbkluZGV4ID0gTnVtYmVyKGMgPT09IFNUUklOR1MuRU5DT0RJTkdbMF0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlWE1MRGVjbGFyYXRpb25BZnRlckVuY29kaW5nKGMpIHtcbiAgICAgICAgaWYgKGMgPT09IENoYXJzLkVRVUFMUykge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLlhNTERlY2xhcmF0aW9uQmVmb3JlVmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA+IENoYXJzLlNQQUNFKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuV2VpcmRUYWc7XG4gICAgICAgICAgICB0aGlzLnN0YXRlV2VpcmRUYWcoYyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGVYTUxEZWNsYXJhdGlvbkJlZm9yZVZhbHVlKGMpIHtcbiAgICAgICAgaWYgKGlzUXVvdGUoYykpIHtcbiAgICAgICAgICAgIHRoaXMuYXR0cmlidXRlVmFsdWUubGVuZ3RoID0gMDtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5YTUxEZWNsYXJhdGlvblZhbHVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGMgPiBDaGFycy5TUEFDRSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLldlaXJkVGFnO1xuICAgICAgICAgICAgdGhpcy5zdGF0ZVdlaXJkVGFnKGMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRlWE1MRGVjbGFyYXRpb25WYWx1ZShjKSB7XG4gICAgICAgIGlmIChpc1F1b3RlKGMpKSB7XG4gICAgICAgICAgICB0aGlzLnNldFJlc3VsdChTdHJpbmcuZnJvbUNoYXJDb2RlKC4uLnRoaXMuYXR0cmlidXRlVmFsdWUpLCBSZXN1bHRUeXBlLlhNTF9FTkNPRElORyk7XG4gICAgICAgICAgICB0aGlzLnN0YXRlID0gU3RhdGUuV2VpcmRUYWc7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA9PT0gQ2hhcnMuR1QpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBTdGF0ZS5CZWZvcmVUYWc7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYyA8PSBDaGFycy5TUEFDRSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IFN0YXRlLldlaXJkVGFnO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5hdHRyaWJ1dGVWYWx1ZS5wdXNoKGMgfCAoYyA+PSAweDQxICYmIGMgPD0gMHg1YSA/IDB4MjAgOiAwKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgd3JpdGUoYnVmZmVyKSB7XG4gICAgICAgIGxldCBpbmRleCA9IDA7XG4gICAgICAgIGZvciAoOyBpbmRleCA8IGJ1ZmZlci5sZW5ndGggJiYgdGhpcy5vZmZzZXQgKyBpbmRleCA8IHRoaXMubWF4Qnl0ZXM7IGluZGV4KyspIHtcbiAgICAgICAgICAgIGNvbnN0IGMgPSBidWZmZXJbaW5kZXhdO1xuICAgICAgICAgICAgc3dpdGNoICh0aGlzLnN0YXRlKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5CZWdpbjoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVnaW4oYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJPTTE2QkU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJPTTE2QkUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJPTTE2TEU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJPTTE2TEUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJPTTg6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJPTTgoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLlVURjE2TEVfWE1MX1BSRUZJWDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlVVRGMTZMRV9YTUxfUFJFRklYKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5CZWdpbkxUOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVCZWdpbkxUKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5VVEYxNkJFX1hNTF9QUkVGSVg6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZVVURjE2QkVfWE1MX1BSRUZJWChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlVGFnOiB7XG4gICAgICAgICAgICAgICAgICAgIC8vIE9wdGltaXphdGlvbjogU2tpcCBhbGwgY2hhcmFjdGVycyB1bnRpbCB3ZSBmaW5kIGEgYDxgXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlkeCA9IGJ1ZmZlci5pbmRleE9mKENoYXJzLkxULCBpbmRleCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpZHggPT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBXZSBhcmUgZG9uZSB3aXRoIHRoaXMgYnVmZmVyLiBTdGF5IGluIHRoZSBzdGF0ZSBhbmQgdHJ5IG9uIHRoZSBuZXh0IG9uZS5cbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID0gYnVmZmVyLmxlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID0gaWR4O1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUJlZm9yZVRhZyhDaGFycy5MVCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlVGFnTmFtZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlVGFnTmFtZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQmVmb3JlQ2xvc2VUYWdOYW1lOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVCZWZvcmVDbG9zZVRhZ05hbWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkNvbW1lbnRTdGFydDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQ29tbWVudFN0YXJ0KGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5Db21tZW50RW5kOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVDb21tZW50RW5kKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5UYWdOYW1lTWV0YToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlVGFnTmFtZU1ldGEoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLlRhZ05hbWVPdGhlcjoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlVGFnTmFtZU90aGVyKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5YTUxEZWNsYXJhdGlvbjoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlWE1MRGVjbGFyYXRpb24oYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLlhNTERlY2xhcmF0aW9uQmVmb3JlRW5jb2Rpbmc6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZVhNTERlY2xhcmF0aW9uQmVmb3JlRW5jb2RpbmcoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLlhNTERlY2xhcmF0aW9uQWZ0ZXJFbmNvZGluZzoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlWE1MRGVjbGFyYXRpb25BZnRlckVuY29kaW5nKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5YTUxEZWNsYXJhdGlvbkJlZm9yZVZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVYTUxEZWNsYXJhdGlvbkJlZm9yZVZhbHVlKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5YTUxEZWNsYXJhdGlvblZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVYTUxEZWNsYXJhdGlvblZhbHVlKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5XZWlyZFRhZzoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlV2VpcmRUYWcoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkJlZm9yZUF0dHJpYnV0ZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlQXR0cmlidXRlKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5NZXRhQXR0cmliSHR0cEVxdWl2OiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQXR0cmliSHR0cEVxdWl2KGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5NZXRhQXR0cmliSHR0cEVxdWl2VmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZU1ldGFBdHRyaWJIdHRwRXF1aXZWYWx1ZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuTWV0YUF0dHJpYkM6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZU1ldGFBdHRyaWJDKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5NZXRhQXR0cmliQ29udGVudDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUF0dHJpYkNvbnRlbnQoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLk1ldGFBdHRyaWJDaGFyc2V0OiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQXR0cmliQ2hhcnNldChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuTWV0YUF0dHJpYkFmdGVyTmFtZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUF0dHJpYkFmdGVyTmFtZShjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZEJlZm9yZUVuY29kaW5nOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlUXVvdGVkQmVmb3JlRW5jb2RpbmcoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLk1ldGFDb250ZW50VmFsdWVRdW90ZWRBZnRlckVuY29kaW5nOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlUXVvdGVkQWZ0ZXJFbmNvZGluZyhjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZEJlZm9yZVZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlUXVvdGVkQmVmb3JlVmFsdWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLk1ldGFDb250ZW50VmFsdWVRdW90ZWRWYWx1ZVF1b3RlZDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZFZhbHVlUXVvdGVkKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlUXVvdGVkVmFsdWVVbnF1b3RlZDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUNvbnRlbnRWYWx1ZVF1b3RlZFZhbHVlVW5xdW90ZWQoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLk1ldGFDb250ZW50VmFsdWVVbnF1b3RlZEJlZm9yZUVuY29kaW5nOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVNZXRhQ29udGVudFZhbHVlVW5xdW90ZWRCZWZvcmVFbmNvZGluZyhjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkQmVmb3JlVmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZU1ldGFDb250ZW50VmFsdWVVbnF1b3RlZEJlZm9yZVZhbHVlKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5NZXRhQ29udGVudFZhbHVlVW5xdW90ZWRWYWx1ZVF1b3RlZDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlTWV0YUNvbnRlbnRWYWx1ZVVucXVvdGVkVmFsdWVRdW90ZWQoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLk1ldGFDb250ZW50VmFsdWVVbnF1b3RlZFZhbHVlVW5xdW90ZWQ6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZU1ldGFDb250ZW50VmFsdWVVbnF1b3RlZFZhbHVlVW5xdW90ZWQoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkFueUF0dHJpYk5hbWU6IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGF0ZUFueUF0dHJpYk5hbWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkFmdGVyQXR0cmlidXRlTmFtZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQWZ0ZXJBdHRyaWJ1dGVOYW1lKGMpO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBTdGF0ZS5CZWZvcmVBdHRyaWJ1dGVWYWx1ZToge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQmVmb3JlQXR0cmlidXRlVmFsdWUoYyk7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlIFN0YXRlLkF0dHJpYnV0ZVZhbHVlUXVvdGVkOiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhdGVBdHRyaWJ1dGVWYWx1ZVF1b3RlZChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgU3RhdGUuQXR0cmlidXRlVmFsdWVVbnF1b3RlZDoge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRlQXR0cmlidXRlVmFsdWVVbnF1b3RlZChjKTtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IGluZGV4O1xuICAgIH1cbn1cbi8qKiBHZXQgdGhlIGVuY29kaW5nIGZvciB0aGUgcGFzc2VkIGJ1ZmZlci4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbmNvZGluZyhidWZmZXIsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBzbmlmZmVyID0gbmV3IFNuaWZmZXIob3B0aW9ucyk7XG4gICAgc25pZmZlci53cml0ZShidWZmZXIpO1xuICAgIHJldHVybiBzbmlmZmVyLmVuY29kaW5nO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c25pZmZlci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\n");

/***/ })

};
;