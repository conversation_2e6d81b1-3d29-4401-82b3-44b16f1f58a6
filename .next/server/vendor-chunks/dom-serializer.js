"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-serializer";
exports.ids = ["vendor-chunks/dom-serializer"];
exports.modules = {

/***/ "(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js":
/*!*************************************************************!*\
  !*** ./node_modules/dom-serializer/lib/esm/foreignNames.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attributeNames: () => (/* binding */ attributeNames),\n/* harmony export */   elementNames: () => (/* binding */ elementNames)\n/* harmony export */ });\nconst elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nconst attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/dom-serializer/lib/esm/index.js":
/*!******************************************************!*\
  !*** ./node_modules/dom-serializer/lib/esm/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   render: () => (/* binding */ render)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var entities__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities */ \"(rsc)/./node_modules/entities/lib/esm/index.js\");\n/* harmony import */ var _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./foreignNames.js */ \"(rsc)/./node_modules/dom-serializer/lib/esm/foreignNames.js\");\n/*\n * Module dependencies\n */\n\n\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\n\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML\n            : entities__WEBPACK_IMPORTED_MODULE_1__.escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nfunction render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (render);\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Doctype:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Directive:\n            return renderDirective(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Comment:\n            return renderComment(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.CDATA:\n            return renderCdata(node);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Script:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Style:\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Tag:\n            return renderTag(node, options);\n        case domelementtype__WEBPACK_IMPORTED_MODULE_0__.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = _foreignNames_js__WEBPACK_IMPORTED_MODULE_2__.elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? (0,entities__WEBPACK_IMPORTED_MODULE_1__.encodeXML)(data)\n                : (0,entities__WEBPACK_IMPORTED_MODULE_1__.escapeText)(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/dom-serializer/lib/esm/index.js\n");

/***/ })

};
;