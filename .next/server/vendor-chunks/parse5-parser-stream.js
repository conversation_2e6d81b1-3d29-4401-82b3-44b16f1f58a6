"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse5-parser-stream";
exports.ids = ["vendor-chunks/parse5-parser-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse5-parser-stream/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/parse5-parser-stream/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParserStream: () => (/* binding */ ParserStream)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n\n\n/* eslint-disable unicorn/consistent-function-scoping -- The rule seems to be broken here. */\n/**\n * Streaming HTML parser with scripting support.\n * A [writable stream](https://nodejs.org/api/stream.html#stream_class_stream_writable).\n *\n * @example\n *\n * ```js\n * const ParserStream = require('parse5-parser-stream');\n * const http = require('http');\n * const { finished } = require('node:stream');\n *\n * // Fetch the page content and obtain it's <head> node\n * http.get('http://inikulin.github.io/parse5/', res => {\n *     const parser = new ParserStream();\n *\n *     finished(parser, () => {\n *         console.log(parser.document.childNodes[1].childNodes[0].tagName); //> 'head'\n *     });\n *\n *     res.pipe(parser);\n * });\n * ```\n *\n */\nclass ParserStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Writable {\n    static getFragmentStream(fragmentContext, options) {\n        const parser = parse5__WEBPACK_IMPORTED_MODULE_1__.Parser.getFragmentParser(fragmentContext, options);\n        const stream = new ParserStream(options, parser);\n        return stream;\n    }\n    /** The resulting document node. */\n    get document() {\n        return this.parser.document;\n    }\n    getFragment() {\n        return this.parser.getFragment();\n    }\n    /**\n     * @param options Parsing options.\n     */\n    constructor(options, parser = new parse5__WEBPACK_IMPORTED_MODULE_1__.Parser(options)) {\n        super({ decodeStrings: false });\n        this.parser = parser;\n        this.lastChunkWritten = false;\n        this.writeCallback = undefined;\n        this.pendingHtmlInsertions = [];\n        const resume = () => {\n            for (let i = this.pendingHtmlInsertions.length - 1; i >= 0; i--) {\n                this.parser.tokenizer.insertHtmlAtCurrentPos(this.pendingHtmlInsertions[i]);\n            }\n            this.pendingHtmlInsertions.length = 0;\n            //NOTE: keep parsing if we don't wait for the next input chunk\n            this.parser.tokenizer.resume(this.writeCallback);\n        };\n        const documentWrite = (html) => {\n            if (!this.parser.stopped) {\n                this.pendingHtmlInsertions.push(html);\n            }\n        };\n        const scriptHandler = (scriptElement) => {\n            if (this.listenerCount('script') > 0) {\n                this.parser.tokenizer.pause();\n                this.emit('script', scriptElement, documentWrite, resume);\n            }\n        };\n        this.parser.scriptHandler = scriptHandler;\n    }\n    //WritableStream implementation\n    _write(chunk, _encoding, callback) {\n        if (typeof chunk !== 'string') {\n            throw new TypeError('Parser can work only with string streams.');\n        }\n        this.writeCallback = callback;\n        this.parser.tokenizer.write(chunk, this.lastChunkWritten, this.writeCallback);\n    }\n    // TODO [engine:node@>=16]: Due to issues with Node < 16, we are overriding `end` instead of `_final`.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    end(chunk, encoding, callback) {\n        this.lastChunkWritten = true;\n        super.end(chunk || '', encoding, callback);\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse5-parser-stream/dist/index.js\n");

/***/ })

};
;