"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-what";
exports.ids = ["vendor-chunks/css-what"];
exports.modules = {

/***/ "(rsc)/./node_modules/css-what/lib/es/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/parse.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isTraversal: () => (/* binding */ isTraversal),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/css-what/lib/es/types.js\");\n\nconst reName = /^[^\\\\#]?(?:\\\\(?:[\\da-f]{1,6}\\s?|.)|[\\w\\-\\u00b0-\\uFFFF])+/;\nconst reEscape = /\\\\([\\da-f]{1,6}\\s?|(\\s)|.)/gi;\nconst actionTypes = new Map([\n    [126 /* Tilde */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element],\n    [94 /* Circumflex */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Start],\n    [36 /* Dollar */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.End],\n    [42 /* Asterisk */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Any],\n    [33 /* ExclamationMark */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Not],\n    [124 /* Pipe */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Hyphen],\n]);\n// Pseudos, whose data property is parsed as well.\nconst unpackPseudos = new Set([\n    \"has\",\n    \"not\",\n    \"matches\",\n    \"is\",\n    \"where\",\n    \"host\",\n    \"host-context\",\n]);\n/**\n * Checks whether a specific selector is a traversal.\n * This is useful eg. in swapping the order of elements that\n * are not traversals.\n *\n * @param selector Selector to check.\n */\nfunction isTraversal(selector) {\n    switch (selector.type) {\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator:\n            return true;\n        default:\n            return false;\n    }\n}\nconst stripQuotesFromPseudos = new Set([\"contains\", \"icontains\"]);\n// Unescape function taken from https://github.com/jquery/sizzle/blob/master/src/sizzle.js#L152\nfunction funescape(_, escaped, escapedWhitespace) {\n    const high = parseInt(escaped, 16) - 0x10000;\n    // NaN means non-codepoint\n    return high !== high || escapedWhitespace\n        ? escaped\n        : high < 0\n            ? // BMP codepoint\n                String.fromCharCode(high + 0x10000)\n            : // Supplemental Plane codepoint (surrogate pair)\n                String.fromCharCode((high >> 10) | 0xd800, (high & 0x3ff) | 0xdc00);\n}\nfunction unescapeCSS(str) {\n    return str.replace(reEscape, funescape);\n}\nfunction isQuote(c) {\n    return c === 39 /* SingleQuote */ || c === 34 /* DoubleQuote */;\n}\nfunction isWhitespace(c) {\n    return (c === 32 /* Space */ ||\n        c === 9 /* Tab */ ||\n        c === 10 /* NewLine */ ||\n        c === 12 /* FormFeed */ ||\n        c === 13 /* CarriageReturn */);\n}\n/**\n * Parses `selector`, optionally with the passed `options`.\n *\n * @param selector Selector to parse.\n * @param options Options for parsing.\n * @returns Returns a two-dimensional array.\n * The first dimension represents selectors separated by commas (eg. `sub1, sub2`),\n * the second contains the relevant tokens for that selector.\n */\nfunction parse(selector) {\n    const subselects = [];\n    const endIndex = parseSelector(subselects, `${selector}`, 0);\n    if (endIndex < selector.length) {\n        throw new Error(`Unmatched selector: ${selector.slice(endIndex)}`);\n    }\n    return subselects;\n}\nfunction parseSelector(subselects, selector, selectorIndex) {\n    let tokens = [];\n    function getName(offset) {\n        const match = selector.slice(selectorIndex + offset).match(reName);\n        if (!match) {\n            throw new Error(`Expected name, found ${selector.slice(selectorIndex)}`);\n        }\n        const [name] = match;\n        selectorIndex += offset + name.length;\n        return unescapeCSS(name);\n    }\n    function stripWhitespace(offset) {\n        selectorIndex += offset;\n        while (selectorIndex < selector.length &&\n            isWhitespace(selector.charCodeAt(selectorIndex))) {\n            selectorIndex++;\n        }\n    }\n    function readValueWithParenthesis() {\n        selectorIndex += 1;\n        const start = selectorIndex;\n        let counter = 1;\n        for (; counter > 0 && selectorIndex < selector.length; selectorIndex++) {\n            if (selector.charCodeAt(selectorIndex) ===\n                40 /* LeftParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter++;\n            }\n            else if (selector.charCodeAt(selectorIndex) ===\n                41 /* RightParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter--;\n            }\n        }\n        if (counter) {\n            throw new Error(\"Parenthesis not matched\");\n        }\n        return unescapeCSS(selector.slice(start, selectorIndex - 1));\n    }\n    function isEscaped(pos) {\n        let slashCount = 0;\n        while (selector.charCodeAt(--pos) === 92 /* BackSlash */)\n            slashCount++;\n        return (slashCount & 1) === 1;\n    }\n    function ensureNotTraversal() {\n        if (tokens.length > 0 && isTraversal(tokens[tokens.length - 1])) {\n            throw new Error(\"Did not expect successive traversals.\");\n        }\n    }\n    function addTraversal(type) {\n        if (tokens.length > 0 &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens[tokens.length - 1].type = type;\n            return;\n        }\n        ensureNotTraversal();\n        tokens.push({ type });\n    }\n    function addSpecialAttribute(name, action) {\n        tokens.push({\n            type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n            name,\n            action,\n            value: getName(1),\n            namespace: null,\n            ignoreCase: \"quirks\",\n        });\n    }\n    /**\n     * We have finished parsing the current part of the selector.\n     *\n     * Remove descendant tokens at the end if they exist,\n     * and return the last index, so that parsing can be\n     * picked up from here.\n     */\n    function finalizeSubselector() {\n        if (tokens.length &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens.pop();\n        }\n        if (tokens.length === 0) {\n            throw new Error(\"Empty sub-selector\");\n        }\n        subselects.push(tokens);\n    }\n    stripWhitespace(0);\n    if (selector.length === selectorIndex) {\n        return selectorIndex;\n    }\n    loop: while (selectorIndex < selector.length) {\n        const firstChar = selector.charCodeAt(selectorIndex);\n        switch (firstChar) {\n            // Whitespace\n            case 32 /* Space */:\n            case 9 /* Tab */:\n            case 10 /* NewLine */:\n            case 12 /* FormFeed */:\n            case 13 /* CarriageReturn */: {\n                if (tokens.length === 0 ||\n                    tokens[0].type !== _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n                    ensureNotTraversal();\n                    tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant });\n                }\n                stripWhitespace(1);\n                break;\n            }\n            // Traversals\n            case 62 /* GreaterThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child);\n                stripWhitespace(1);\n                break;\n            }\n            case 60 /* LessThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent);\n                stripWhitespace(1);\n                break;\n            }\n            case 126 /* Tilde */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling);\n                stripWhitespace(1);\n                break;\n            }\n            case 43 /* Plus */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent);\n                stripWhitespace(1);\n                break;\n            }\n            // Special attribute selectors: .class, #id\n            case 46 /* Period */: {\n                addSpecialAttribute(\"class\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element);\n                break;\n            }\n            case 35 /* Hash */: {\n                addSpecialAttribute(\"id\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals);\n                break;\n            }\n            case 91 /* LeftSquareBracket */: {\n                stripWhitespace(1);\n                // Determine attribute name and namespace\n                let name;\n                let namespace = null;\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */) {\n                    // Equivalent to no namespace\n                    name = getName(1);\n                }\n                else if (selector.startsWith(\"*|\", selectorIndex)) {\n                    namespace = \"*\";\n                    name = getName(2);\n                }\n                else {\n                    name = getName(0);\n                    if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                        selector.charCodeAt(selectorIndex + 1) !==\n                            61 /* Equal */) {\n                        namespace = name;\n                        name = getName(1);\n                    }\n                }\n                stripWhitespace(0);\n                // Determine comparison operation\n                let action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Exists;\n                const possibleAction = actionTypes.get(selector.charCodeAt(selectorIndex));\n                if (possibleAction) {\n                    action = possibleAction;\n                    if (selector.charCodeAt(selectorIndex + 1) !==\n                        61 /* Equal */) {\n                        throw new Error(\"Expected `=`\");\n                    }\n                    stripWhitespace(2);\n                }\n                else if (selector.charCodeAt(selectorIndex) === 61 /* Equal */) {\n                    action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals;\n                    stripWhitespace(1);\n                }\n                // Determine value\n                let value = \"\";\n                let ignoreCase = null;\n                if (action !== \"exists\") {\n                    if (isQuote(selector.charCodeAt(selectorIndex))) {\n                        const quote = selector.charCodeAt(selectorIndex);\n                        let sectionEnd = selectorIndex + 1;\n                        while (sectionEnd < selector.length &&\n                            (selector.charCodeAt(sectionEnd) !== quote ||\n                                isEscaped(sectionEnd))) {\n                            sectionEnd += 1;\n                        }\n                        if (selector.charCodeAt(sectionEnd) !== quote) {\n                            throw new Error(\"Attribute value didn't end\");\n                        }\n                        value = unescapeCSS(selector.slice(selectorIndex + 1, sectionEnd));\n                        selectorIndex = sectionEnd + 1;\n                    }\n                    else {\n                        const valueStart = selectorIndex;\n                        while (selectorIndex < selector.length &&\n                            ((!isWhitespace(selector.charCodeAt(selectorIndex)) &&\n                                selector.charCodeAt(selectorIndex) !==\n                                    93 /* RightSquareBracket */) ||\n                                isEscaped(selectorIndex))) {\n                            selectorIndex += 1;\n                        }\n                        value = unescapeCSS(selector.slice(valueStart, selectorIndex));\n                    }\n                    stripWhitespace(0);\n                    // See if we have a force ignore flag\n                    const forceIgnore = selector.charCodeAt(selectorIndex) | 0x20;\n                    // If the forceIgnore flag is set (either `i` or `s`), use that value\n                    if (forceIgnore === 115 /* LowerS */) {\n                        ignoreCase = false;\n                        stripWhitespace(1);\n                    }\n                    else if (forceIgnore === 105 /* LowerI */) {\n                        ignoreCase = true;\n                        stripWhitespace(1);\n                    }\n                }\n                if (selector.charCodeAt(selectorIndex) !==\n                    93 /* RightSquareBracket */) {\n                    throw new Error(\"Attribute selector didn't terminate\");\n                }\n                selectorIndex += 1;\n                const attributeSelector = {\n                    type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n                    name,\n                    action,\n                    value,\n                    namespace,\n                    ignoreCase,\n                };\n                tokens.push(attributeSelector);\n                break;\n            }\n            case 58 /* Colon */: {\n                if (selector.charCodeAt(selectorIndex + 1) === 58 /* Colon */) {\n                    tokens.push({\n                        type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.PseudoElement,\n                        name: getName(2).toLowerCase(),\n                        data: selector.charCodeAt(selectorIndex) ===\n                            40 /* LeftParenthesis */\n                            ? readValueWithParenthesis()\n                            : null,\n                    });\n                    continue;\n                }\n                const name = getName(1).toLowerCase();\n                let data = null;\n                if (selector.charCodeAt(selectorIndex) ===\n                    40 /* LeftParenthesis */) {\n                    if (unpackPseudos.has(name)) {\n                        if (isQuote(selector.charCodeAt(selectorIndex + 1))) {\n                            throw new Error(`Pseudo-selector ${name} cannot be quoted`);\n                        }\n                        data = [];\n                        selectorIndex = parseSelector(data, selector, selectorIndex + 1);\n                        if (selector.charCodeAt(selectorIndex) !==\n                            41 /* RightParenthesis */) {\n                            throw new Error(`Missing closing parenthesis in :${name} (${selector})`);\n                        }\n                        selectorIndex += 1;\n                    }\n                    else {\n                        data = readValueWithParenthesis();\n                        if (stripQuotesFromPseudos.has(name)) {\n                            const quot = data.charCodeAt(0);\n                            if (quot === data.charCodeAt(data.length - 1) &&\n                                isQuote(quot)) {\n                                data = data.slice(1, -1);\n                            }\n                        }\n                        data = unescapeCSS(data);\n                    }\n                }\n                tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Pseudo, name, data });\n                break;\n            }\n            case 44 /* Comma */: {\n                finalizeSubselector();\n                tokens = [];\n                stripWhitespace(1);\n                break;\n            }\n            default: {\n                if (selector.startsWith(\"/*\", selectorIndex)) {\n                    const endIndex = selector.indexOf(\"*/\", selectorIndex + 2);\n                    if (endIndex < 0) {\n                        throw new Error(\"Comment was not terminated\");\n                    }\n                    selectorIndex = endIndex + 2;\n                    // Remove leading whitespace\n                    if (tokens.length === 0) {\n                        stripWhitespace(0);\n                    }\n                    break;\n                }\n                let namespace = null;\n                let name;\n                if (firstChar === 42 /* Asterisk */) {\n                    selectorIndex += 1;\n                    name = \"*\";\n                }\n                else if (firstChar === 124 /* Pipe */) {\n                    name = \"\";\n                    if (selector.charCodeAt(selectorIndex + 1) === 124 /* Pipe */) {\n                        addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator);\n                        stripWhitespace(2);\n                        break;\n                    }\n                }\n                else if (reName.test(selector.slice(selectorIndex))) {\n                    name = getName(0);\n                }\n                else {\n                    break loop;\n                }\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                    selector.charCodeAt(selectorIndex + 1) !== 124 /* Pipe */) {\n                    namespace = name;\n                    if (selector.charCodeAt(selectorIndex + 1) ===\n                        42 /* Asterisk */) {\n                        name = \"*\";\n                        selectorIndex += 2;\n                    }\n                    else {\n                        name = getName(1);\n                    }\n                }\n                tokens.push(name === \"*\"\n                    ? { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Universal, namespace }\n                    : { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Tag, name, namespace });\n            }\n        }\n    }\n    finalizeSubselector();\n    return selectorIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/css-what/lib/es/types.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributeAction: () => (/* binding */ AttributeAction),\n/* harmony export */   IgnoreCaseMode: () => (/* binding */ IgnoreCaseMode),\n/* harmony export */   SelectorType: () => (/* binding */ SelectorType)\n/* harmony export */ });\nvar SelectorType;\n(function (SelectorType) {\n    SelectorType[\"Attribute\"] = \"attribute\";\n    SelectorType[\"Pseudo\"] = \"pseudo\";\n    SelectorType[\"PseudoElement\"] = \"pseudo-element\";\n    SelectorType[\"Tag\"] = \"tag\";\n    SelectorType[\"Universal\"] = \"universal\";\n    // Traversals\n    SelectorType[\"Adjacent\"] = \"adjacent\";\n    SelectorType[\"Child\"] = \"child\";\n    SelectorType[\"Descendant\"] = \"descendant\";\n    SelectorType[\"Parent\"] = \"parent\";\n    SelectorType[\"Sibling\"] = \"sibling\";\n    SelectorType[\"ColumnCombinator\"] = \"column-combinator\";\n})(SelectorType || (SelectorType = {}));\n/**\n * Modes for ignore case.\n *\n * This could be updated to an enum, and the object is\n * the current stand-in that will allow code to be updated\n * without big changes.\n */\nconst IgnoreCaseMode = {\n    Unknown: null,\n    QuirksMode: \"quirks\",\n    IgnoreCase: true,\n    CaseSensitive: false,\n};\nvar AttributeAction;\n(function (AttributeAction) {\n    AttributeAction[\"Any\"] = \"any\";\n    AttributeAction[\"Element\"] = \"element\";\n    AttributeAction[\"End\"] = \"end\";\n    AttributeAction[\"Equals\"] = \"equals\";\n    AttributeAction[\"Exists\"] = \"exists\";\n    AttributeAction[\"Hyphen\"] = \"hyphen\";\n    AttributeAction[\"Not\"] = \"not\";\n    AttributeAction[\"Start\"] = \"start\";\n})(AttributeAction || (AttributeAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/types.js\n");

/***/ })

};
;