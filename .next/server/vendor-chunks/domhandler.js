"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domhandler";
exports.ids = ["vendor-chunks/domhandler"];
exports.modules = {

/***/ "(rsc)/./node_modules/domhandler/lib/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA),\n/* harmony export */   Comment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment),\n/* harmony export */   DataNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.DataNode),\n/* harmony export */   Document: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Document),\n/* harmony export */   DomHandler: () => (/* binding */ DomHandler),\n/* harmony export */   Element: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Element),\n/* harmony export */   Node: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Node),\n/* harmony export */   NodeWithChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction),\n/* harmony export */   Text: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Text),\n/* harmony export */   cloneNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.cloneNode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.hasChildren),\n/* harmony export */   isCDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isComment),\n/* harmony export */   isDirective: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDirective),\n/* harmony export */   isDocument: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/esm/node.js\");\n\n\n\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nclass DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag : undefined;\n        const element = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(\"\");\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/esm/node.js":
/*!*************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* binding */ CDATA),\n/* harmony export */   Comment: () => (/* binding */ Comment),\n/* harmony export */   DataNode: () => (/* binding */ DataNode),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeWithChildren: () => (/* binding */ NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* binding */ ProcessingInstruction),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode),\n/* harmony export */   hasChildren: () => (/* binding */ hasChildren),\n/* harmony export */   isCDATA: () => (/* binding */ isCDATA),\n/* harmony export */   isComment: () => (/* binding */ isComment),\n/* harmony export */   isDirective: () => (/* binding */ isDirective),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isTag: () => (/* binding */ isTag),\n/* harmony export */   isText: () => (/* binding */ isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nclass Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nclass DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nclass Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nclass Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nclass ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nclass NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nclass CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nclass Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nclass Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Script\n        : name === \"style\"\n            ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Style\n            : domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0,domelementtype__WEBPACK_IMPORTED_MODULE_0__.isTag)(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9taGFuZGxlci9saWIvZXNtL25vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0JBQW9CLHVEQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0IsdURBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHVEQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZUFBZTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0IsdURBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLG9CQUFvQix1REFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHVEQUFXO0FBQ3JCO0FBQ0EsY0FBYyx1REFBVztBQUN6QixjQUFjLHVEQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyxxREFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx5QkFBeUIsdURBQVc7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AseUJBQXlCLHVEQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLHlCQUF5Qix1REFBVztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx5QkFBeUIsdURBQVc7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AseUJBQXlCLHVEQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxpQkFBaUI7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELFVBQVU7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IscUJBQXFCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pY29uLWdlbmVyYXRvci8uL25vZGVfbW9kdWxlcy9kb21oYW5kbGVyL2xpYi9lc20vbm9kZS5qcz84ZDI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEVsZW1lbnRUeXBlLCBpc1RhZyBhcyBpc1RhZ1JhdyB9IGZyb20gXCJkb21lbGVtZW50dHlwZVwiO1xuLyoqXG4gKiBUaGlzIG9iamVjdCB3aWxsIGJlIHVzZWQgYXMgdGhlIHByb3RvdHlwZSBmb3IgTm9kZXMgd2hlbiBjcmVhdGluZyBhXG4gKiBET00tTGV2ZWwtMS1jb21wbGlhbnQgc3RydWN0dXJlLlxuICovXG5leHBvcnQgY2xhc3MgTm9kZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIC8qKiBQYXJlbnQgb2YgdGhlIG5vZGUgKi9cbiAgICAgICAgdGhpcy5wYXJlbnQgPSBudWxsO1xuICAgICAgICAvKiogUHJldmlvdXMgc2libGluZyAqL1xuICAgICAgICB0aGlzLnByZXYgPSBudWxsO1xuICAgICAgICAvKiogTmV4dCBzaWJsaW5nICovXG4gICAgICAgIHRoaXMubmV4dCA9IG51bGw7XG4gICAgICAgIC8qKiBUaGUgc3RhcnQgaW5kZXggb2YgdGhlIG5vZGUuIFJlcXVpcmVzIGB3aXRoU3RhcnRJbmRpY2VzYCBvbiB0aGUgaGFuZGxlciB0byBiZSBgdHJ1ZS4gKi9cbiAgICAgICAgdGhpcy5zdGFydEluZGV4ID0gbnVsbDtcbiAgICAgICAgLyoqIFRoZSBlbmQgaW5kZXggb2YgdGhlIG5vZGUuIFJlcXVpcmVzIGB3aXRoRW5kSW5kaWNlc2Agb24gdGhlIGhhbmRsZXIgdG8gYmUgYHRydWUuICovXG4gICAgICAgIHRoaXMuZW5kSW5kZXggPSBudWxsO1xuICAgIH1cbiAgICAvLyBSZWFkLXdyaXRlIGFsaWFzZXMgZm9yIHByb3BlcnRpZXNcbiAgICAvKipcbiAgICAgKiBTYW1lIGFzIHtAbGluayBwYXJlbnR9LlxuICAgICAqIFtET00gc3BlY10oaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnKS1jb21wYXRpYmxlIGFsaWFzLlxuICAgICAqL1xuICAgIGdldCBwYXJlbnROb2RlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wYXJlbnQ7XG4gICAgfVxuICAgIHNldCBwYXJlbnROb2RlKHBhcmVudCkge1xuICAgICAgICB0aGlzLnBhcmVudCA9IHBhcmVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2FtZSBhcyB7QGxpbmsgcHJldn0uXG4gICAgICogW0RPTSBzcGVjXShodHRwczovL2RvbS5zcGVjLndoYXR3Zy5vcmcpLWNvbXBhdGlibGUgYWxpYXMuXG4gICAgICovXG4gICAgZ2V0IHByZXZpb3VzU2libGluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucHJldjtcbiAgICB9XG4gICAgc2V0IHByZXZpb3VzU2libGluZyhwcmV2KSB7XG4gICAgICAgIHRoaXMucHJldiA9IHByZXY7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNhbWUgYXMge0BsaW5rIG5leHR9LlxuICAgICAqIFtET00gc3BlY10oaHR0cHM6Ly9kb20uc3BlYy53aGF0d2cub3JnKS1jb21wYXRpYmxlIGFsaWFzLlxuICAgICAqL1xuICAgIGdldCBuZXh0U2libGluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubmV4dDtcbiAgICB9XG4gICAgc2V0IG5leHRTaWJsaW5nKG5leHQpIHtcbiAgICAgICAgdGhpcy5uZXh0ID0gbmV4dDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2xvbmUgdGhpcyBub2RlLCBhbmQgb3B0aW9uYWxseSBpdHMgY2hpbGRyZW4uXG4gICAgICpcbiAgICAgKiBAcGFyYW0gcmVjdXJzaXZlIENsb25lIGNoaWxkIG5vZGVzIGFzIHdlbGwuXG4gICAgICogQHJldHVybnMgQSBjbG9uZSBvZiB0aGUgbm9kZS5cbiAgICAgKi9cbiAgICBjbG9uZU5vZGUocmVjdXJzaXZlID0gZmFsc2UpIHtcbiAgICAgICAgcmV0dXJuIGNsb25lTm9kZSh0aGlzLCByZWN1cnNpdmUpO1xuICAgIH1cbn1cbi8qKlxuICogQSBub2RlIHRoYXQgY29udGFpbnMgc29tZSBkYXRhLlxuICovXG5leHBvcnQgY2xhc3MgRGF0YU5vZGUgZXh0ZW5kcyBOb2RlIHtcbiAgICAvKipcbiAgICAgKiBAcGFyYW0gZGF0YSBUaGUgY29udGVudCBvZiB0aGUgZGF0YSBub2RlXG4gICAgICovXG4gICAgY29uc3RydWN0b3IoZGF0YSkge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmRhdGEgPSBkYXRhO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTYW1lIGFzIHtAbGluayBkYXRhfS5cbiAgICAgKiBbRE9NIHNwZWNdKGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZyktY29tcGF0aWJsZSBhbGlhcy5cbiAgICAgKi9cbiAgICBnZXQgbm9kZVZhbHVlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kYXRhO1xuICAgIH1cbiAgICBzZXQgbm9kZVZhbHVlKGRhdGEpIHtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YTtcbiAgICB9XG59XG4vKipcbiAqIFRleHQgd2l0aGluIHRoZSBkb2N1bWVudC5cbiAqL1xuZXhwb3J0IGNsYXNzIFRleHQgZXh0ZW5kcyBEYXRhTm9kZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgICAgIHRoaXMudHlwZSA9IEVsZW1lbnRUeXBlLlRleHQ7XG4gICAgfVxuICAgIGdldCBub2RlVHlwZSgpIHtcbiAgICAgICAgcmV0dXJuIDM7XG4gICAgfVxufVxuLyoqXG4gKiBDb21tZW50cyB3aXRoaW4gdGhlIGRvY3VtZW50LlxuICovXG5leHBvcnQgY2xhc3MgQ29tbWVudCBleHRlbmRzIERhdGFOb2RlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy50eXBlID0gRWxlbWVudFR5cGUuQ29tbWVudDtcbiAgICB9XG4gICAgZ2V0IG5vZGVUeXBlKCkge1xuICAgICAgICByZXR1cm4gODtcbiAgICB9XG59XG4vKipcbiAqIFByb2Nlc3NpbmcgaW5zdHJ1Y3Rpb25zLCBpbmNsdWRpbmcgZG9jIHR5cGVzLlxuICovXG5leHBvcnQgY2xhc3MgUHJvY2Vzc2luZ0luc3RydWN0aW9uIGV4dGVuZHMgRGF0YU5vZGUge1xuICAgIGNvbnN0cnVjdG9yKG5hbWUsIGRhdGEpIHtcbiAgICAgICAgc3VwZXIoZGF0YSk7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMudHlwZSA9IEVsZW1lbnRUeXBlLkRpcmVjdGl2ZTtcbiAgICB9XG4gICAgZ2V0IG5vZGVUeXBlKCkge1xuICAgICAgICByZXR1cm4gMTtcbiAgICB9XG59XG4vKipcbiAqIEEgYE5vZGVgIHRoYXQgY2FuIGhhdmUgY2hpbGRyZW4uXG4gKi9cbmV4cG9ydCBjbGFzcyBOb2RlV2l0aENoaWxkcmVuIGV4dGVuZHMgTm9kZSB7XG4gICAgLyoqXG4gICAgICogQHBhcmFtIGNoaWxkcmVuIENoaWxkcmVuIG9mIHRoZSBub2RlLiBPbmx5IGNlcnRhaW4gbm9kZSB0eXBlcyBjYW4gaGF2ZSBjaGlsZHJlbi5cbiAgICAgKi9cbiAgICBjb25zdHJ1Y3RvcihjaGlsZHJlbikge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmNoaWxkcmVuID0gY2hpbGRyZW47XG4gICAgfVxuICAgIC8vIEFsaWFzZXNcbiAgICAvKiogRmlyc3QgY2hpbGQgb2YgdGhlIG5vZGUuICovXG4gICAgZ2V0IGZpcnN0Q2hpbGQoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgcmV0dXJuIChfYSA9IHRoaXMuY2hpbGRyZW5bMF0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG51bGw7XG4gICAgfVxuICAgIC8qKiBMYXN0IGNoaWxkIG9mIHRoZSBub2RlLiAqL1xuICAgIGdldCBsYXN0Q2hpbGQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNoaWxkcmVuLmxlbmd0aCA+IDBcbiAgICAgICAgICAgID8gdGhpcy5jaGlsZHJlblt0aGlzLmNoaWxkcmVuLmxlbmd0aCAtIDFdXG4gICAgICAgICAgICA6IG51bGw7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNhbWUgYXMge0BsaW5rIGNoaWxkcmVufS5cbiAgICAgKiBbRE9NIHNwZWNdKGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZyktY29tcGF0aWJsZSBhbGlhcy5cbiAgICAgKi9cbiAgICBnZXQgY2hpbGROb2RlcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2hpbGRyZW47XG4gICAgfVxuICAgIHNldCBjaGlsZE5vZGVzKGNoaWxkcmVuKSB7XG4gICAgICAgIHRoaXMuY2hpbGRyZW4gPSBjaGlsZHJlbjtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgQ0RBVEEgZXh0ZW5kcyBOb2RlV2l0aENoaWxkcmVuIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy50eXBlID0gRWxlbWVudFR5cGUuQ0RBVEE7XG4gICAgfVxuICAgIGdldCBub2RlVHlwZSgpIHtcbiAgICAgICAgcmV0dXJuIDQ7XG4gICAgfVxufVxuLyoqXG4gKiBUaGUgcm9vdCBub2RlIG9mIHRoZSBkb2N1bWVudC5cbiAqL1xuZXhwb3J0IGNsYXNzIERvY3VtZW50IGV4dGVuZHMgTm9kZVdpdGhDaGlsZHJlbiB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgICAgIHRoaXMudHlwZSA9IEVsZW1lbnRUeXBlLlJvb3Q7XG4gICAgfVxuICAgIGdldCBub2RlVHlwZSgpIHtcbiAgICAgICAgcmV0dXJuIDk7XG4gICAgfVxufVxuLyoqXG4gKiBBbiBlbGVtZW50IHdpdGhpbiB0aGUgRE9NLlxuICovXG5leHBvcnQgY2xhc3MgRWxlbWVudCBleHRlbmRzIE5vZGVXaXRoQ2hpbGRyZW4ge1xuICAgIC8qKlxuICAgICAqIEBwYXJhbSBuYW1lIE5hbWUgb2YgdGhlIHRhZywgZWcuIGBkaXZgLCBgc3BhbmAuXG4gICAgICogQHBhcmFtIGF0dHJpYnMgT2JqZWN0IG1hcHBpbmcgYXR0cmlidXRlIG5hbWVzIHRvIGF0dHJpYnV0ZSB2YWx1ZXMuXG4gICAgICogQHBhcmFtIGNoaWxkcmVuIENoaWxkcmVuIG9mIHRoZSBub2RlLlxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKG5hbWUsIGF0dHJpYnMsIGNoaWxkcmVuID0gW10sIHR5cGUgPSBuYW1lID09PSBcInNjcmlwdFwiXG4gICAgICAgID8gRWxlbWVudFR5cGUuU2NyaXB0XG4gICAgICAgIDogbmFtZSA9PT0gXCJzdHlsZVwiXG4gICAgICAgICAgICA/IEVsZW1lbnRUeXBlLlN0eWxlXG4gICAgICAgICAgICA6IEVsZW1lbnRUeXBlLlRhZykge1xuICAgICAgICBzdXBlcihjaGlsZHJlbik7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMuYXR0cmlicyA9IGF0dHJpYnM7XG4gICAgICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgfVxuICAgIGdldCBub2RlVHlwZSgpIHtcbiAgICAgICAgcmV0dXJuIDE7XG4gICAgfVxuICAgIC8vIERPTSBMZXZlbCAxIGFsaWFzZXNcbiAgICAvKipcbiAgICAgKiBTYW1lIGFzIHtAbGluayBuYW1lfS5cbiAgICAgKiBbRE9NIHNwZWNdKGh0dHBzOi8vZG9tLnNwZWMud2hhdHdnLm9yZyktY29tcGF0aWJsZSBhbGlhcy5cbiAgICAgKi9cbiAgICBnZXQgdGFnTmFtZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubmFtZTtcbiAgICB9XG4gICAgc2V0IHRhZ05hbWUobmFtZSkge1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgIH1cbiAgICBnZXQgYXR0cmlidXRlcygpIHtcbiAgICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHRoaXMuYXR0cmlicykubWFwKChuYW1lKSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgcmV0dXJuICh7XG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICB2YWx1ZTogdGhpcy5hdHRyaWJzW25hbWVdLFxuICAgICAgICAgICAgICAgIG5hbWVzcGFjZTogKF9hID0gdGhpc1tcIngtYXR0cmlic05hbWVzcGFjZVwiXSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hW25hbWVdLFxuICAgICAgICAgICAgICAgIHByZWZpeDogKF9iID0gdGhpc1tcIngtYXR0cmlic1ByZWZpeFwiXSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iW25hbWVdLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8qKlxuICogQHBhcmFtIG5vZGUgTm9kZSB0byBjaGVjay5cbiAqIEByZXR1cm5zIGB0cnVlYCBpZiB0aGUgbm9kZSBpcyBhIGBFbGVtZW50YCwgYGZhbHNlYCBvdGhlcndpc2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1RhZyhub2RlKSB7XG4gICAgcmV0dXJuIGlzVGFnUmF3KG5vZGUpO1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyB0aGUgdHlwZSBgQ0RBVEFgLCBgZmFsc2VgIG90aGVyd2lzZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzQ0RBVEEobm9kZSkge1xuICAgIHJldHVybiBub2RlLnR5cGUgPT09IEVsZW1lbnRUeXBlLkNEQVRBO1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyB0aGUgdHlwZSBgVGV4dGAsIGBmYWxzZWAgb3RoZXJ3aXNlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNUZXh0KG5vZGUpIHtcbiAgICByZXR1cm4gbm9kZS50eXBlID09PSBFbGVtZW50VHlwZS5UZXh0O1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyB0aGUgdHlwZSBgQ29tbWVudGAsIGBmYWxzZWAgb3RoZXJ3aXNlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNDb21tZW50KG5vZGUpIHtcbiAgICByZXR1cm4gbm9kZS50eXBlID09PSBFbGVtZW50VHlwZS5Db21tZW50O1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyB0aGUgdHlwZSBgUHJvY2Vzc2luZ0luc3RydWN0aW9uYCwgYGZhbHNlYCBvdGhlcndpc2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0RpcmVjdGl2ZShub2RlKSB7XG4gICAgcmV0dXJuIG5vZGUudHlwZSA9PT0gRWxlbWVudFR5cGUuRGlyZWN0aXZlO1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyB0aGUgdHlwZSBgUHJvY2Vzc2luZ0luc3RydWN0aW9uYCwgYGZhbHNlYCBvdGhlcndpc2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0RvY3VtZW50KG5vZGUpIHtcbiAgICByZXR1cm4gbm9kZS50eXBlID09PSBFbGVtZW50VHlwZS5Sb290O1xufVxuLyoqXG4gKiBAcGFyYW0gbm9kZSBOb2RlIHRvIGNoZWNrLlxuICogQHJldHVybnMgYHRydWVgIGlmIHRoZSBub2RlIGhhcyBjaGlsZHJlbiwgYGZhbHNlYCBvdGhlcndpc2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXNDaGlsZHJlbihub2RlKSB7XG4gICAgcmV0dXJuIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChub2RlLCBcImNoaWxkcmVuXCIpO1xufVxuLyoqXG4gKiBDbG9uZSBhIG5vZGUsIGFuZCBvcHRpb25hbGx5IGl0cyBjaGlsZHJlbi5cbiAqXG4gKiBAcGFyYW0gcmVjdXJzaXZlIENsb25lIGNoaWxkIG5vZGVzIGFzIHdlbGwuXG4gKiBAcmV0dXJucyBBIGNsb25lIG9mIHRoZSBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gY2xvbmVOb2RlKG5vZGUsIHJlY3Vyc2l2ZSA9IGZhbHNlKSB7XG4gICAgbGV0IHJlc3VsdDtcbiAgICBpZiAoaXNUZXh0KG5vZGUpKSB7XG4gICAgICAgIHJlc3VsdCA9IG5ldyBUZXh0KG5vZGUuZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzQ29tbWVudChub2RlKSkge1xuICAgICAgICByZXN1bHQgPSBuZXcgQ29tbWVudChub2RlLmRhdGEpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1RhZyhub2RlKSkge1xuICAgICAgICBjb25zdCBjaGlsZHJlbiA9IHJlY3Vyc2l2ZSA/IGNsb25lQ2hpbGRyZW4obm9kZS5jaGlsZHJlbikgOiBbXTtcbiAgICAgICAgY29uc3QgY2xvbmUgPSBuZXcgRWxlbWVudChub2RlLm5hbWUsIHsgLi4ubm9kZS5hdHRyaWJzIH0sIGNoaWxkcmVuKTtcbiAgICAgICAgY2hpbGRyZW4uZm9yRWFjaCgoY2hpbGQpID0+IChjaGlsZC5wYXJlbnQgPSBjbG9uZSkpO1xuICAgICAgICBpZiAobm9kZS5uYW1lc3BhY2UgIT0gbnVsbCkge1xuICAgICAgICAgICAgY2xvbmUubmFtZXNwYWNlID0gbm9kZS5uYW1lc3BhY2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5vZGVbXCJ4LWF0dHJpYnNOYW1lc3BhY2VcIl0pIHtcbiAgICAgICAgICAgIGNsb25lW1wieC1hdHRyaWJzTmFtZXNwYWNlXCJdID0geyAuLi5ub2RlW1wieC1hdHRyaWJzTmFtZXNwYWNlXCJdIH07XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG5vZGVbXCJ4LWF0dHJpYnNQcmVmaXhcIl0pIHtcbiAgICAgICAgICAgIGNsb25lW1wieC1hdHRyaWJzUHJlZml4XCJdID0geyAuLi5ub2RlW1wieC1hdHRyaWJzUHJlZml4XCJdIH07XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0ID0gY2xvbmU7XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzQ0RBVEEobm9kZSkpIHtcbiAgICAgICAgY29uc3QgY2hpbGRyZW4gPSByZWN1cnNpdmUgPyBjbG9uZUNoaWxkcmVuKG5vZGUuY2hpbGRyZW4pIDogW107XG4gICAgICAgIGNvbnN0IGNsb25lID0gbmV3IENEQVRBKGNoaWxkcmVuKTtcbiAgICAgICAgY2hpbGRyZW4uZm9yRWFjaCgoY2hpbGQpID0+IChjaGlsZC5wYXJlbnQgPSBjbG9uZSkpO1xuICAgICAgICByZXN1bHQgPSBjbG9uZTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNEb2N1bWVudChub2RlKSkge1xuICAgICAgICBjb25zdCBjaGlsZHJlbiA9IHJlY3Vyc2l2ZSA/IGNsb25lQ2hpbGRyZW4obm9kZS5jaGlsZHJlbikgOiBbXTtcbiAgICAgICAgY29uc3QgY2xvbmUgPSBuZXcgRG9jdW1lbnQoY2hpbGRyZW4pO1xuICAgICAgICBjaGlsZHJlbi5mb3JFYWNoKChjaGlsZCkgPT4gKGNoaWxkLnBhcmVudCA9IGNsb25lKSk7XG4gICAgICAgIGlmIChub2RlW1wieC1tb2RlXCJdKSB7XG4gICAgICAgICAgICBjbG9uZVtcIngtbW9kZVwiXSA9IG5vZGVbXCJ4LW1vZGVcIl07XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0ID0gY2xvbmU7XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzRGlyZWN0aXZlKG5vZGUpKSB7XG4gICAgICAgIGNvbnN0IGluc3RydWN0aW9uID0gbmV3IFByb2Nlc3NpbmdJbnN0cnVjdGlvbihub2RlLm5hbWUsIG5vZGUuZGF0YSk7XG4gICAgICAgIGlmIChub2RlW1wieC1uYW1lXCJdICE9IG51bGwpIHtcbiAgICAgICAgICAgIGluc3RydWN0aW9uW1wieC1uYW1lXCJdID0gbm9kZVtcIngtbmFtZVwiXTtcbiAgICAgICAgICAgIGluc3RydWN0aW9uW1wieC1wdWJsaWNJZFwiXSA9IG5vZGVbXCJ4LXB1YmxpY0lkXCJdO1xuICAgICAgICAgICAgaW5zdHJ1Y3Rpb25bXCJ4LXN5c3RlbUlkXCJdID0gbm9kZVtcIngtc3lzdGVtSWRcIl07XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0ID0gaW5zdHJ1Y3Rpb247XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYE5vdCBpbXBsZW1lbnRlZCB5ZXQ6ICR7bm9kZS50eXBlfWApO1xuICAgIH1cbiAgICByZXN1bHQuc3RhcnRJbmRleCA9IG5vZGUuc3RhcnRJbmRleDtcbiAgICByZXN1bHQuZW5kSW5kZXggPSBub2RlLmVuZEluZGV4O1xuICAgIGlmIChub2RlLnNvdXJjZUNvZGVMb2NhdGlvbiAhPSBudWxsKSB7XG4gICAgICAgIHJlc3VsdC5zb3VyY2VDb2RlTG9jYXRpb24gPSBub2RlLnNvdXJjZUNvZGVMb2NhdGlvbjtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIGNsb25lQ2hpbGRyZW4oY2hpbGRzKSB7XG4gICAgY29uc3QgY2hpbGRyZW4gPSBjaGlsZHMubWFwKChjaGlsZCkgPT4gY2xvbmVOb2RlKGNoaWxkLCB0cnVlKSk7XG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPCBjaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICBjaGlsZHJlbltpXS5wcmV2ID0gY2hpbGRyZW5baSAtIDFdO1xuICAgICAgICBjaGlsZHJlbltpIC0gMV0ubmV4dCA9IGNoaWxkcmVuW2ldO1xuICAgIH1cbiAgICByZXR1cm4gY2hpbGRyZW47XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/node.js\n");

/***/ })

};
;