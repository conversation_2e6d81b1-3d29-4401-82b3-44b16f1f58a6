"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-url-for-icon/route";
exports.ids = ["app/api/analyze-url-for-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-url-for-icon/route.ts */ \"(rsc)/./src/app/api/analyze-url-for-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-url-for-icon/route\",\n        pathname: \"/api/analyze-url-for-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-url-for-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-url-for-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-url-for-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-url-for-icon/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/analyze-url-for-icon/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Normalize URL\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch the website content\n        let websiteContent = \"\";\n        let extractedColors = [];\n        let websiteInfo = {};\n        try {\n            const response = await fetch(normalizedUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const html = await response.text();\n                // Extract basic info for display\n                const titleMatch = html.match(/<title[^>]*>([^<]+)<\\/title>/i);\n                const descMatch = html.match(/<meta[^>]*name=\"description\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogTitleMatch = html.match(/<meta[^>]*property=\"og:title\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogSiteMatch = html.match(/<meta[^>]*property=\"og:site_name\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                // Extract colors from the HTML\n                extractedColors = (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.extractColors)(html);\n                console.log(\"\\uD83C\\uDFA8 Color extraction results for\", normalizedUrl);\n                console.log(\"\\uD83D\\uDCCA Number of colors found:\", extractedColors.length);\n                console.log(\"\\uD83C\\uDF08 Extracted colors:\", extractedColors);\n                websiteInfo = {\n                    title: titleMatch?.[1] || ogTitleMatch?.[1] || \"\",\n                    description: descMatch?.[1] || \"\",\n                    siteName: ogSiteMatch?.[1] || \"\",\n                    extractedColors\n                };\n                // Clean HTML for AI analysis\n                websiteContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 10000); // Limit content for API efficiency\n            }\n        } catch (fetchError) {\n            console.warn(\"Could not fetch website content:\", fetchError);\n        // Continue with URL-only analysis\n        }\n        // Create AI prompt for icon generation\n        console.log(\"\\uD83E\\uDD16 Preparing AI prompt with colors:\", extractedColors);\n        const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.\n\nWebsite URL: ${normalizedUrl}\n${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : \"\"}\n${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : \"\"}\n${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : \"\"}\n${extractedColors.length > 0 ? `Website Brand Colors: ${extractedColors.join(\", \")}` : \"No brand colors detected\"}\n\n${websiteContent ? `Website Content (first 10,000 characters):\\n${websiteContent}` : \"Website content could not be accessed - please analyze based on URL and any available metadata.\"}\n\nBased on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt should include:\n\n1. The website's purpose and industry\n2. Appropriate visual elements and symbols\n3. Color scheme - IMPORTANT: If Website Brand Colors are provided above, use those EXACT hex color values in your prompt. Do not guess or interpret colors - use the extracted hex values exactly as provided.\n4. Design style (modern, minimalist, corporate, etc.)\n5. Technical requirements for app store submission\n\nCRITICAL COLOR INSTRUCTION: If brand colors were extracted from the website (listed above), you MUST include those exact hex color values in your icon generation prompt. For example, if the extracted colors are \"#3b82f6, #10b981\", your prompt should specify \"using primary color #3b82f6, secondary color #10b981\" rather than describing colors in general terms.\n\nCreate a detailed, specific prompt that would generate an icon that perfectly represents this website. Focus on accuracy and brand appropriateness, especially regarding the exact color values.\n\nRespond with ONLY the icon generation prompt, no additional text or explanation.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"AI analysis failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const generatedPrompt = result.candidates[0].content.parts[0].text.trim();\n            console.log(\"✅ AI generated prompt:\");\n            console.log(generatedPrompt);\n            console.log(\"\\uD83C\\uDFA8 Colors in final response:\", extractedColors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: generatedPrompt,\n                websiteInfo\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate icon prompt from website analysis.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error analyzing URL for icon:\", error);\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-url-for-icon/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Performs AI-powered analysis of website content using Gemini directly\n */ async function performAIAnalysis(html, metadata) {\n    try {\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            console.warn(\"Google AI API key not configured, skipping AI analysis\");\n            return undefined;\n        }\n        // Extract text content from HTML (simplified)\n        const textContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 8000); // Limit text for API efficiency\n        const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.\n\nWebsite Information:\n- URL: ${metadata.url || \"Not provided\"}\n- Title: ${metadata.title || \"Not provided\"}\n- Description: ${metadata.description || \"Not provided\"}\n- Site Name: ${metadata.siteName || \"Not provided\"}\n- Keywords: ${metadata.keywords?.join(\", \") || \"Not provided\"}\n\nWebsite Content (first 8000 characters):\n${textContent}\n\nPlease analyze this website and respond with a JSON object containing the following fields:\n\n{\n  \"category\": \"one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other\",\n  \"visualStyle\": \"one of: modern, minimalist, corporate, playful, elegant, bold, classic\",\n  \"brandPersonality\": \"brief description of the brand's personality and tone\",\n  \"targetAudience\": \"description of the primary target audience\",\n  \"primaryPurpose\": \"main purpose or function of the website\",\n  \"keyFeatures\": [\"array\", \"of\", \"key\", \"features\", \"or\", \"services\"],\n  \"industryContext\": \"industry or sector this website operates in\",\n  \"designCharacteristics\": [\"array\", \"of\", \"visual\", \"design\", \"characteristics\"]\n}\n\nBase your analysis on:\n1. The actual content and purpose of the website\n2. The language, tone, and messaging used\n3. The apparent target audience and use cases\n4. The industry context and competitive landscape\n5. Visual and functional characteristics that would inform icon design\n\nRespond ONLY with the JSON object, no additional text.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            console.warn(\"Gemini API request failed:\", response.status, response.statusText);\n            return undefined;\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let analysisText = result.candidates[0].content.parts[0].text.trim();\n            // Clean up the response to extract JSON\n            analysisText = analysisText.replace(/```json\\s*/, \"\").replace(/```\\s*$/, \"\").trim();\n            try {\n                const analysis = JSON.parse(analysisText);\n                // Validate the response structure\n                if (!analysis.category || !analysis.visualStyle) {\n                    console.warn(\"Invalid AI analysis structure received\");\n                    return undefined;\n                }\n                return analysis;\n            } catch (parseError) {\n                console.warn(\"Failed to parse AI analysis JSON:\", parseError);\n                return undefined;\n            }\n        } else {\n            console.warn(\"No valid response from Gemini API\");\n            return undefined;\n        }\n    } catch (error) {\n        console.warn(\"AI analysis error:\", error);\n        return undefined;\n    }\n}\n/**\n * Basic fallback analysis when AI analysis fails\n */ function basicFallbackAnalysis(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Basic category determination\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\")) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\")) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\")) {\n        category = \"corporate\";\n    }\n    // Basic style determination\n    let visualStyle = \"modern\";\n    if (allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (allText.includes(\"corporate\") || allText.includes(\"professional\")) {\n        visualStyle = \"corporate\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    console.log(\"\\uD83D\\uDD0D Starting color extraction from HTML (length:\", html.length, \"chars)\");\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    }\n                });\n            }\n        }\n        // Check for brand-related elements (headers, logos, buttons)\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    $('style, link[rel=\"stylesheet\"]').each((_, element)=>{\n        const css = $(element).html() || \"\";\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                }\n            });\n        }\n    });\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    console.log(\"\\uD83D\\uDCC8 Color extraction summary:\");\n    console.log(\"- Total unique colors found:\", Object.keys(colorCounts).length);\n    console.log(\"- Color frequency map:\", colorCounts);\n    console.log(\"- Top 5 colors returned:\", sortedColors);\n    return sortedColors;\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Perform AI-powered analysis\n        const aiAnalysis = await performAIAnalysis(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        // Use AI analysis if available, otherwise fall back to basic analysis\n        let category = \"other\";\n        let visualStyle = \"modern\";\n        if (aiAnalysis) {\n            category = aiAnalysis.category;\n            visualStyle = aiAnalysis.visualStyle;\n        } else {\n            // Fallback to basic analysis\n            const fallback = basicFallbackAnalysis(html, metadata);\n            category = fallback.category;\n            visualStyle = fallback.visualStyle;\n        }\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage,\n            aiAnalysis\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();