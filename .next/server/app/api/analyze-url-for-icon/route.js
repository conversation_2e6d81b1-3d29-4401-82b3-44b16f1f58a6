"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-url-for-icon/route";
exports.ids = ["app/api/analyze-url-for-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-url-for-icon/route.ts */ \"(rsc)/./src/app/api/analyze-url-for-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-url-for-icon/route\",\n        pathname: \"/api/analyze-url-for-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-url-for-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-url-for-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-url-for-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-url-for-icon/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/analyze-url-for-icon/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Normalize URL\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch the website content\n        let websiteContent = \"\";\n        let extractedColors = [];\n        let websiteInfo = {};\n        try {\n            const response = await fetch(normalizedUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const html = await response.text();\n                // Extract basic info for display\n                const titleMatch = html.match(/<title[^>]*>([^<]+)<\\/title>/i);\n                const descMatch = html.match(/<meta[^>]*name=\"description\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogTitleMatch = html.match(/<meta[^>]*property=\"og:title\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogSiteMatch = html.match(/<meta[^>]*property=\"og:site_name\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                // Extract colors from the HTML\n                extractedColors = (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.extractColors)(html);\n                console.log(\"\\uD83C\\uDFA8 Color extraction results for\", normalizedUrl);\n                console.log(\"\\uD83D\\uDCCA Number of colors found:\", extractedColors.length);\n                console.log(\"\\uD83C\\uDF08 Extracted colors:\", extractedColors);\n                websiteInfo = {\n                    title: titleMatch?.[1] || ogTitleMatch?.[1] || \"\",\n                    description: descMatch?.[1] || \"\",\n                    siteName: ogSiteMatch?.[1] || \"\",\n                    extractedColors\n                };\n                // Clean HTML for AI analysis\n                websiteContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 10000); // Limit content for API efficiency\n            }\n        } catch (fetchError) {\n            console.warn(\"Could not fetch website content:\", fetchError);\n        // Continue with URL-only analysis\n        }\n        // Create AI prompt for icon generation\n        console.log(\"\\uD83E\\uDD16 Preparing AI prompt with colors:\", extractedColors);\n        const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.\n\nWebsite URL: ${normalizedUrl}\n${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : \"\"}\n${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : \"\"}\n${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : \"\"}\n${extractedColors.length > 0 ? `Website Brand Colors: ${extractedColors.join(\", \")}` : \"No brand colors detected\"}\n\n${websiteContent ? `Website Content (first 10,000 characters):\\n${websiteContent}` : \"Website content could not be accessed - please analyze based on URL and any available metadata.\"}\n\nBased on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt should include:\n\n1. The website's purpose and industry\n2. Appropriate visual elements and symbols\n3. Color scheme - IMPORTANT: If Website Brand Colors are provided above, use those EXACT hex color values in your prompt. Do not guess or interpret colors - use the extracted hex values exactly as provided.\n4. Design style (modern, minimalist, corporate, etc.)\n5. Technical requirements for app store submission\n\nCRITICAL COLOR INSTRUCTION: If brand colors were extracted from the website (listed above), you MUST include those exact hex color values in your icon generation prompt. For example, if the extracted colors are \"#3b82f6, #10b981\", your prompt should specify \"using primary color #3b82f6, secondary color #10b981\" rather than describing colors in general terms.\n\nCreate a detailed, specific prompt that would generate an icon that perfectly represents this website. Focus on accuracy and brand appropriateness, especially regarding the exact color values.\n\nRespond with ONLY the icon generation prompt, no additional text or explanation.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"AI analysis failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const generatedPrompt = result.candidates[0].content.parts[0].text.trim();\n            console.log(\"✅ AI generated prompt:\");\n            console.log(generatedPrompt);\n            console.log(\"\\uD83C\\uDFA8 Colors in final response:\", extractedColors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: generatedPrompt,\n                websiteInfo\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate icon prompt from website analysis.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error analyzing URL for icon:\", error);\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-url-for-icon/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Performs AI-powered analysis of website content using Gemini directly\n */ async function performAIAnalysis(html, metadata) {\n    try {\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            console.warn(\"Google AI API key not configured, skipping AI analysis\");\n            return undefined;\n        }\n        // Extract text content from HTML (simplified)\n        const textContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 8000); // Limit text for API efficiency\n        const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.\n\nWebsite Information:\n- URL: ${metadata.url || \"Not provided\"}\n- Title: ${metadata.title || \"Not provided\"}\n- Description: ${metadata.description || \"Not provided\"}\n- Site Name: ${metadata.siteName || \"Not provided\"}\n- Keywords: ${metadata.keywords?.join(\", \") || \"Not provided\"}\n\nWebsite Content (first 8000 characters):\n${textContent}\n\nPlease analyze this website and respond with a JSON object containing the following fields:\n\n{\n  \"category\": \"one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other\",\n  \"visualStyle\": \"one of: modern, minimalist, corporate, playful, elegant, bold, classic\",\n  \"brandPersonality\": \"brief description of the brand's personality and tone\",\n  \"targetAudience\": \"description of the primary target audience\",\n  \"primaryPurpose\": \"main purpose or function of the website\",\n  \"keyFeatures\": [\"array\", \"of\", \"key\", \"features\", \"or\", \"services\"],\n  \"industryContext\": \"industry or sector this website operates in\",\n  \"designCharacteristics\": [\"array\", \"of\", \"visual\", \"design\", \"characteristics\"]\n}\n\nBase your analysis on:\n1. The actual content and purpose of the website\n2. The language, tone, and messaging used\n3. The apparent target audience and use cases\n4. The industry context and competitive landscape\n5. Visual and functional characteristics that would inform icon design\n\nRespond ONLY with the JSON object, no additional text.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            console.warn(\"Gemini API request failed:\", response.status, response.statusText);\n            return undefined;\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let analysisText = result.candidates[0].content.parts[0].text.trim();\n            // Clean up the response to extract JSON\n            analysisText = analysisText.replace(/```json\\s*/, \"\").replace(/```\\s*$/, \"\").trim();\n            try {\n                const analysis = JSON.parse(analysisText);\n                // Validate the response structure\n                if (!analysis.category || !analysis.visualStyle) {\n                    console.warn(\"Invalid AI analysis structure received\");\n                    return undefined;\n                }\n                return analysis;\n            } catch (parseError) {\n                console.warn(\"Failed to parse AI analysis JSON:\", parseError);\n                return undefined;\n            }\n        } else {\n            console.warn(\"No valid response from Gemini API\");\n            return undefined;\n        }\n    } catch (error) {\n        console.warn(\"AI analysis error:\", error);\n        return undefined;\n    }\n}\n/**\n * Basic fallback analysis when AI analysis fails\n */ function basicFallbackAnalysis(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Basic category determination\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\")) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\")) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\")) {\n        category = \"corporate\";\n    }\n    // Basic style determination\n    let visualStyle = \"modern\";\n    if (allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (allText.includes(\"corporate\") || allText.includes(\"professional\")) {\n        visualStyle = \"corporate\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    console.log(\"\\uD83D\\uDD0D Starting color extraction from HTML (length:\", html.length, \"chars)\");\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    let inlineStyleCount = 0;\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            inlineStyleCount++;\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                console.log(\"\\uD83C\\uDFA8 Found colors in inline style:\", colorMatches, \"from element:\", $(element).prop(\"tagName\"));\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                        console.log(\"✅ Added color:\", normalized, \"with weight:\", weight);\n                    } else {\n                        console.log(\"❌ Skipped color:\", color, \"(normalized:\", normalized, \", common:\", isCommonColor(normalized || \"\"), \")\");\n                    }\n                });\n            }\n        }\n    });\n    console.log(\"\\uD83D\\uDCCA Processed\", inlineStyleCount, \"elements with inline styles\");\n    // Check for brand-related elements (headers, logos, buttons) in a separate loop\n    $(\"*\").each((_, element)=>{\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    console.log(\"\\uD83C\\uDFF7️ Found brand colors in\", tagName, \"element:\", brandColors);\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                            console.log(\"⭐ Added brand color:\", normalized);\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    let styleTagCount = 0;\n    $(\"style\").each((_, element)=>{\n        styleTagCount++;\n        const css = $(element).html() || \"\";\n        console.log(\"\\uD83C\\uDFA8 Processing style tag\", styleTagCount, \"(length:\", css.length, \"chars)\");\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            console.log(\"\\uD83C\\uDFA8 Found colors in CSS:\", colorMatches);\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    console.log(\"✅ Added CSS color:\", normalized, \"with weight:\", weight);\n                }\n            });\n        } else {\n            console.log(\"❌ No colors found in this style tag\");\n        }\n    });\n    console.log(\"\\uD83D\\uDCCA Processed\", styleTagCount, \"style tags\");\n    // Also try to extract from any CSS custom properties in the HTML\n    const htmlContent = $.html();\n    const cssVarMatches = htmlContent.match(/--[\\w-]+:\\s*(#[0-9a-fA-F]{3,6}|rgb\\([^)]+\\)|rgba\\([^)]+\\))/gi);\n    if (cssVarMatches) {\n        console.log(\"\\uD83C\\uDFA8 Found CSS custom properties:\", cssVarMatches);\n        cssVarMatches.forEach((match)=>{\n            const colorMatch = match.match(colorRegex);\n            if (colorMatch) {\n                colorMatch.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + 4; // High priority for CSS vars\n                        console.log(\"⭐ Added CSS variable color:\", normalized);\n                    }\n                });\n            }\n        });\n    }\n    // If we haven't found many colors, try some fallback methods\n    if (Object.keys(colorCounts).length < 3) {\n        console.log(\"\\uD83D\\uDD0D Low color count, trying fallback methods...\");\n        // Look for data attributes that might contain colors\n        $(\"*\").each((_, element)=>{\n            const $el = $(element);\n            // Check all attributes for color values\n            const attributes = $el.get(0)?.attributes;\n            if (attributes) {\n                for(let i = 0; i < attributes.length; i++){\n                    const attr = attributes[i];\n                    if (attr.name.includes(\"color\") || attr.name.includes(\"theme\") || attr.name.startsWith(\"data-\")) {\n                        const colorMatch = attr.value.match(colorRegex);\n                        if (colorMatch) {\n                            console.log(\"\\uD83C\\uDFA8 Found color in attribute\", attr.name, \":\", colorMatch);\n                            colorMatch.forEach((color)=>{\n                                const normalized = normalizeColor(color);\n                                if (normalized && !isCommonColor(normalized)) {\n                                    colorCounts[normalized] = (colorCounts[normalized] || 0) + 2;\n                                    console.log(\"✅ Added fallback color:\", normalized);\n                                }\n                            });\n                        }\n                    }\n                }\n            }\n        });\n        // Look for common color class patterns\n        const colorClassPatterns = [\n            \"bg-blue\",\n            \"bg-red\",\n            \"bg-green\",\n            \"bg-purple\",\n            \"bg-yellow\",\n            \"bg-pink\",\n            \"bg-indigo\",\n            \"text-blue\",\n            \"text-red\",\n            \"text-green\",\n            \"text-purple\",\n            \"text-yellow\",\n            \"text-pink\",\n            \"primary\",\n            \"secondary\",\n            \"accent\",\n            \"brand\"\n        ];\n        colorClassPatterns.forEach((pattern)=>{\n            const elements = $(`.${pattern}, [class*=\"${pattern}\"]`);\n            if (elements.length > 0) {\n                console.log(\"\\uD83C\\uDFA8 Found elements with color class pattern:\", pattern, \"(\", elements.length, \"elements)\");\n                // Add some default colors for common patterns (this is a fallback)\n                if (pattern.includes(\"blue\")) colorCounts[\"#3b82f6\"] = (colorCounts[\"#3b82f6\"] || 0) + 1;\n                if (pattern.includes(\"red\")) colorCounts[\"#ef4444\"] = (colorCounts[\"#ef4444\"] || 0) + 1;\n                if (pattern.includes(\"green\")) colorCounts[\"#10b981\"] = (colorCounts[\"#10b981\"] || 0) + 1;\n                if (pattern.includes(\"purple\")) colorCounts[\"#8b5cf6\"] = (colorCounts[\"#8b5cf6\"] || 0) + 1;\n            }\n        });\n    }\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    console.log(\"\\uD83D\\uDCC8 Color extraction summary:\");\n    console.log(\"- Total unique colors found:\", Object.keys(colorCounts).length);\n    console.log(\"- Color frequency map:\", colorCounts);\n    console.log(\"- Top 5 colors returned:\", sortedColors);\n    return sortedColors;\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Perform AI-powered analysis\n        const aiAnalysis = await performAIAnalysis(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        // Use AI analysis if available, otherwise fall back to basic analysis\n        let category = \"other\";\n        let visualStyle = \"modern\";\n        if (aiAnalysis) {\n            category = aiAnalysis.category;\n            visualStyle = aiAnalysis.visualStyle;\n        } else {\n            // Fallback to basic analysis\n            const fallback = basicFallbackAnalysis(html, metadata);\n            category = fallback.category;\n            visualStyle = fallback.visualStyle;\n        }\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage,\n            aiAnalysis\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();