"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-url-for-icon/route";
exports.ids = ["app/api/analyze-url-for-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-url-for-icon/route.ts */ \"(rsc)/./src/app/api/analyze-url-for-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-url-for-icon/route\",\n        pathname: \"/api/analyze-url-for-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-url-for-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-url-for-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-url-for-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-url-for-icon/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/analyze-url-for-icon/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Normalize URL\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch the website content\n        let websiteContent = \"\";\n        let websiteInfo = {};\n        try {\n            const response = await fetch(normalizedUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const html = await response.text();\n                // Extract basic info for display\n                const titleMatch = html.match(/<title[^>]*>([^<]+)<\\/title>/i);\n                const descMatch = html.match(/<meta[^>]*name=\"description\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogTitleMatch = html.match(/<meta[^>]*property=\"og:title\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogSiteMatch = html.match(/<meta[^>]*property=\"og:site_name\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                websiteInfo = {\n                    title: titleMatch?.[1] || ogTitleMatch?.[1] || \"\",\n                    description: descMatch?.[1] || \"\",\n                    siteName: ogSiteMatch?.[1] || \"\"\n                };\n                // Clean HTML for AI analysis\n                websiteContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 10000); // Limit content for API efficiency\n            }\n        } catch (fetchError) {\n            console.warn(\"Could not fetch website content:\", fetchError);\n        // Continue with URL-only analysis\n        }\n        // Create AI prompt for icon generation\n        const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.\n\nWebsite URL: ${normalizedUrl}\n${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : \"\"}\n${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : \"\"}\n${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : \"\"}\n\n${websiteContent ? `Website Content (first 10,000 characters):\\n${websiteContent}` : \"Website content could not be accessed - please analyze based on URL and any available metadata.\"}\n\nBased on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt should include:\n\n1. The website's purpose and industry\n2. Appropriate visual elements and symbols\n3. Color scheme (use specific hex colors if you can determine the brand colors)\n4. Design style (modern, minimalist, corporate, etc.)\n5. Technical requirements for app store submission\n\nCreate a detailed, specific prompt that would generate an icon that perfectly represents this website. Focus on accuracy and brand appropriateness.\n\nRespond with ONLY the icon generation prompt, no additional text or explanation.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"AI analysis failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const generatedPrompt = result.candidates[0].content.parts[0].text.trim();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: generatedPrompt,\n                websiteInfo\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate icon prompt from website analysis.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error analyzing URL for icon:\", error);\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-url-for-icon/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();