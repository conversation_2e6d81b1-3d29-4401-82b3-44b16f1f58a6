"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-url-for-icon/route";
exports.ids = ["app/api/analyze-url-for-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-url-for-icon/route.ts */ \"(rsc)/./src/app/api/analyze-url-for-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-url-for-icon/route\",\n        pathname: \"/api/analyze-url-for-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-url-for-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-url-for-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-url-for-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-url-for-icon/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/analyze-url-for-icon/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Normalize URL\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch the website content\n        let websiteContent = \"\";\n        let extractedColors = [];\n        let websiteInfo = {};\n        try {\n            const response = await fetch(normalizedUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const html = await response.text();\n                // Extract basic info for display\n                const titleMatch = html.match(/<title[^>]*>([^<]+)<\\/title>/i);\n                const descMatch = html.match(/<meta[^>]*name=\"description\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogTitleMatch = html.match(/<meta[^>]*property=\"og:title\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogSiteMatch = html.match(/<meta[^>]*property=\"og:site_name\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                // Extract colors from the HTML\n                extractedColors = (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.extractColors)(html);\n                websiteInfo = {\n                    title: titleMatch?.[1] || ogTitleMatch?.[1] || \"\",\n                    description: descMatch?.[1] || \"\",\n                    siteName: ogSiteMatch?.[1] || \"\",\n                    extractedColors\n                };\n                // Clean HTML for AI analysis\n                websiteContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 10000); // Limit content for API efficiency\n            }\n        } catch (fetchError) {\n            console.warn(\"Could not fetch website content:\", fetchError);\n        // Continue with URL-only analysis\n        }\n        // Create AI prompt for icon generation\n        const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.\n\nWebsite URL: ${normalizedUrl}\n${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : \"\"}\n${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : \"\"}\n${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : \"\"}\n${extractedColors.length > 0 ? `Website Brand Colors: ${extractedColors.join(\", \")}` : \"No brand colors detected\"}\n\n${websiteContent ? `Website Content (first 10,000 characters):\\n${websiteContent}` : \"Website content could not be accessed - please analyze based on URL and any available metadata.\"}\n\nBased on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt should include:\n\n1. The website's purpose and industry\n2. Appropriate visual elements and symbols\n3. Color scheme - IMPORTANT: If Website Brand Colors are provided above, use those EXACT hex color values in your prompt. Do not guess or interpret colors - use the extracted hex values exactly as provided.\n4. Design style (modern, minimalist, corporate, etc.)\n5. Technical requirements for app store submission\n\nCRITICAL COLOR INSTRUCTION: If brand colors were extracted from the website (listed above), you MUST include those exact hex color values in your icon generation prompt. For example, if the extracted colors are \"#3b82f6, #10b981\", your prompt should specify \"using primary color #3b82f6, secondary color #10b981\" rather than describing colors in general terms.\n\nCreate a detailed, specific prompt that would generate an icon that perfectly represents this website. Focus on accuracy and brand appropriateness, especially regarding the exact color values.\n\nRespond with ONLY the icon generation prompt, no additional text or explanation.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"AI analysis failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const generatedPrompt = result.candidates[0].content.parts[0].text.trim();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: generatedPrompt,\n                websiteInfo\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate icon prompt from website analysis.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error analyzing URL for icon:\", error);\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-url-for-icon/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Performs AI-powered analysis of website content using Gemini directly\n */ async function performAIAnalysis(html, metadata) {\n    try {\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            console.warn(\"Google AI API key not configured, skipping AI analysis\");\n            return undefined;\n        }\n        // Extract text content from HTML (simplified)\n        const textContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 8000); // Limit text for API efficiency\n        const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.\n\nWebsite Information:\n- URL: ${metadata.url || \"Not provided\"}\n- Title: ${metadata.title || \"Not provided\"}\n- Description: ${metadata.description || \"Not provided\"}\n- Site Name: ${metadata.siteName || \"Not provided\"}\n- Keywords: ${metadata.keywords?.join(\", \") || \"Not provided\"}\n\nWebsite Content (first 8000 characters):\n${textContent}\n\nPlease analyze this website and respond with a JSON object containing the following fields:\n\n{\n  \"category\": \"one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other\",\n  \"visualStyle\": \"one of: modern, minimalist, corporate, playful, elegant, bold, classic\",\n  \"brandPersonality\": \"brief description of the brand's personality and tone\",\n  \"targetAudience\": \"description of the primary target audience\",\n  \"primaryPurpose\": \"main purpose or function of the website\",\n  \"keyFeatures\": [\"array\", \"of\", \"key\", \"features\", \"or\", \"services\"],\n  \"industryContext\": \"industry or sector this website operates in\",\n  \"designCharacteristics\": [\"array\", \"of\", \"visual\", \"design\", \"characteristics\"]\n}\n\nBase your analysis on:\n1. The actual content and purpose of the website\n2. The language, tone, and messaging used\n3. The apparent target audience and use cases\n4. The industry context and competitive landscape\n5. Visual and functional characteristics that would inform icon design\n\nRespond ONLY with the JSON object, no additional text.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            console.warn(\"Gemini API request failed:\", response.status, response.statusText);\n            return undefined;\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let analysisText = result.candidates[0].content.parts[0].text.trim();\n            // Clean up the response to extract JSON\n            analysisText = analysisText.replace(/```json\\s*/, \"\").replace(/```\\s*$/, \"\").trim();\n            try {\n                const analysis = JSON.parse(analysisText);\n                // Validate the response structure\n                if (!analysis.category || !analysis.visualStyle) {\n                    console.warn(\"Invalid AI analysis structure received\");\n                    return undefined;\n                }\n                return analysis;\n            } catch (parseError) {\n                console.warn(\"Failed to parse AI analysis JSON:\", parseError);\n                return undefined;\n            }\n        } else {\n            console.warn(\"No valid response from Gemini API\");\n            return undefined;\n        }\n    } catch (error) {\n        console.warn(\"AI analysis error:\", error);\n        return undefined;\n    }\n}\n/**\n * Basic fallback analysis when AI analysis fails\n */ function basicFallbackAnalysis(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Basic category determination\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\")) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\")) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\")) {\n        category = \"corporate\";\n    }\n    // Basic style determination\n    let visualStyle = \"modern\";\n    if (allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (allText.includes(\"corporate\") || allText.includes(\"professional\")) {\n        visualStyle = \"corporate\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    }\n                });\n            }\n        }\n        // Check for brand-related elements (headers, logos, buttons)\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    $('style, link[rel=\"stylesheet\"]').each((_, element)=>{\n        const css = $(element).html() || \"\";\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                }\n            });\n        }\n    });\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    return sortedColors;\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Perform AI-powered analysis\n        const aiAnalysis = await performAIAnalysis(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        // Use AI analysis if available, otherwise fall back to basic analysis\n        let category = \"other\";\n        let visualStyle = \"modern\";\n        if (aiAnalysis) {\n            category = aiAnalysis.category;\n            visualStyle = aiAnalysis.visualStyle;\n        } else {\n            // Fallback to basic analysis\n            const fallback = basicFallbackAnalysis(html, metadata);\n            category = fallback.category;\n            visualStyle = fallback.visualStyle;\n        }\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage,\n            aiAnalysis\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();