"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-prompt-from-url/route";
exports.ids = ["app/api/generate-prompt-from-url/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_prompt_from_url_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-prompt-from-url/route.ts */ \"(rsc)/./src/app/api/generate-prompt-from-url/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-prompt-from-url/route\",\n        pathname: \"/api/generate-prompt-from-url\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-prompt-from-url/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/generate-prompt-from-url/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_prompt_from_url_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-prompt-from-url/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-prompt-from-url/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/generate-prompt-from-url/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/promptGenerator */ \"(rsc)/./src/utils/promptGenerator.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { websiteData } = body;\n        if (!websiteData || !websiteData.url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Website data is required.\"\n            }, {\n                status: 400\n            });\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Create the enhancement request\n        const enhancementRequest = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__.createPromptEnhancementRequest)(websiteData);\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: enhancementRequest\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"Prompt generation failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let generatedPrompt = result.candidates[0].content.parts[0].text;\n            // Clean up the prompt (remove \"Enhanced Prompt:\" prefix if present)\n            generatedPrompt = generatedPrompt.replace(/^Enhanced Prompt:\\s*/i, \"\").trim();\n            // Validate the generated prompt\n            const validation = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__.validatePrompt)(generatedPrompt);\n            if (!validation.isValid && validation.issues) {\n                console.warn(\"Generated prompt has issues:\", validation.issues);\n            }\n            const finalPrompt = validation.cleanedPrompt || generatedPrompt;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: finalPrompt\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate prompt from website data. Please try again.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error generating prompt from URL:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `Failed to generate prompt: ${errorMessage}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-prompt-from-url/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/promptGenerator.ts":
/*!**************************************!*\
  !*** ./src/utils/promptGenerator.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPromptEnhancementRequest: () => (/* binding */ createPromptEnhancementRequest),\n/* harmony export */   generateIconPrompt: () => (/* binding */ generateIconPrompt),\n/* harmony export */   validatePrompt: () => (/* binding */ validatePrompt)\n/* harmony export */ });\n/**\n * Maps content categories to icon style descriptions\n */ const CATEGORY_STYLES = {\n    ecommerce: {\n        elements: [\n            \"shopping bag\",\n            \"cart\",\n            \"storefront\",\n            \"package\",\n            \"credit card\"\n        ],\n        style: \"commercial and trustworthy\",\n        mood: \"professional yet approachable\"\n    },\n    blog: {\n        elements: [\n            \"pen\",\n            \"notebook\",\n            \"speech bubble\",\n            \"document\",\n            \"quill\"\n        ],\n        style: \"editorial and readable\",\n        mood: \"informative and engaging\"\n    },\n    portfolio: {\n        elements: [\n            \"easel\",\n            \"brush\",\n            \"camera\",\n            \"frame\",\n            \"gallery\"\n        ],\n        style: \"creative and artistic\",\n        mood: \"inspiring and professional\"\n    },\n    saas: {\n        elements: [\n            \"cloud\",\n            \"dashboard\",\n            \"graph\",\n            \"network\",\n            \"gear\"\n        ],\n        style: \"tech-forward and modern\",\n        mood: \"innovative and reliable\"\n    },\n    corporate: {\n        elements: [\n            \"building\",\n            \"handshake\",\n            \"briefcase\",\n            \"chart\",\n            \"globe\"\n        ],\n        style: \"professional and established\",\n        mood: \"trustworthy and authoritative\"\n    },\n    creative: {\n        elements: [\n            \"palette\",\n            \"lightbulb\",\n            \"star\",\n            \"magic wand\",\n            \"rainbow\"\n        ],\n        style: \"artistic and expressive\",\n        mood: \"imaginative and vibrant\"\n    },\n    educational: {\n        elements: [\n            \"book\",\n            \"graduation cap\",\n            \"apple\",\n            \"chalkboard\",\n            \"lightbulb\"\n        ],\n        style: \"educational and clear\",\n        mood: \"knowledgeable and approachable\"\n    },\n    other: {\n        elements: [\n            \"circle\",\n            \"square\",\n            \"diamond\",\n            \"star\",\n            \"hexagon\"\n        ],\n        style: \"versatile and clean\",\n        mood: \"neutral and professional\"\n    }\n};\n/**\n * Maps visual styles to design descriptions\n */ const VISUAL_STYLE_DESCRIPTIONS = {\n    modern: \"sleek, contemporary design with clean lines and subtle gradients\",\n    minimalist: \"ultra-clean, simple design with plenty of white space and minimal elements\",\n    corporate: \"professional, business-like appearance with structured layout and conservative colors\",\n    playful: \"fun, energetic design with rounded corners and vibrant, cheerful elements\",\n    elegant: \"sophisticated, refined design with premium feel and subtle luxury touches\",\n    bold: \"strong, impactful design with high contrast and dynamic visual elements\",\n    classic: \"timeless, traditional design with balanced proportions and established conventions\"\n};\n/**\n * Converts hex/rgb colors to descriptive color names\n */ function colorToDescription(color) {\n    const colorMap = {\n        \"#ff0000\": \"bright red\",\n        \"#00ff00\": \"bright green\",\n        \"#0000ff\": \"bright blue\",\n        \"#ffff00\": \"bright yellow\",\n        \"#ff00ff\": \"bright magenta\",\n        \"#00ffff\": \"bright cyan\",\n        \"#ffa500\": \"orange\",\n        \"#800080\": \"purple\",\n        \"#ffc0cb\": \"pink\",\n        \"#a52a2a\": \"brown\",\n        \"#808080\": \"gray\",\n        \"#000000\": \"black\",\n        \"#ffffff\": \"white\"\n    };\n    // Simple color matching (could be enhanced with more sophisticated color analysis)\n    const lowerColor = color.toLowerCase();\n    if (colorMap[lowerColor]) {\n        return colorMap[lowerColor];\n    }\n    // Basic RGB analysis for common colors\n    if (lowerColor.includes(\"red\") || lowerColor.startsWith(\"#f\") || lowerColor.startsWith(\"#e\")) {\n        return \"red tones\";\n    } else if (lowerColor.includes(\"blue\") || lowerColor.includes(\"#0\") || lowerColor.includes(\"#1\")) {\n        return \"blue tones\";\n    } else if (lowerColor.includes(\"green\") || lowerColor.includes(\"#0f\") || lowerColor.includes(\"#2\")) {\n        return \"green tones\";\n    } else if (lowerColor.includes(\"yellow\") || lowerColor.includes(\"#ff\")) {\n        return \"yellow tones\";\n    }\n    return \"custom color\";\n}\n/**\n * Generates a detailed prompt for icon generation based on website data\n */ function generateIconPrompt(websiteData) {\n    const category = websiteData.category || \"other\";\n    const visualStyle = websiteData.visualStyle || \"modern\";\n    const categoryInfo = CATEGORY_STYLES[category];\n    const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle];\n    // Build the prompt components\n    const components = [];\n    // 1. Basic description\n    const siteName = websiteData.siteName || websiteData.title || \"website\";\n    components.push(`Create a professional app icon for \"${siteName}\"`);\n    // 2. Category-specific elements\n    const suggestedElement = categoryInfo.elements[Math.floor(Math.random() * categoryInfo.elements.length)];\n    components.push(`incorporating a ${suggestedElement} as the main element`);\n    // 3. Visual style\n    components.push(`designed in a ${visualStyle} style with ${styleDescription}`);\n    // 4. Color scheme\n    if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {\n        const colorDescriptions = websiteData.primaryColors.slice(0, 3) // Use up to 3 colors\n        .map(colorToDescription).join(\", \");\n        components.push(`using a color palette featuring ${colorDescriptions}`);\n    } else {\n        // Default color suggestions based on category\n        const defaultColors = {\n            ecommerce: \"blue and green tones for trust and growth\",\n            blog: \"warm orange and blue tones for readability\",\n            portfolio: \"creative purple and teal tones\",\n            saas: \"modern blue and gray tones\",\n            corporate: \"professional navy and silver tones\",\n            creative: \"vibrant rainbow or artistic color combinations\",\n            educational: \"friendly blue and yellow tones\",\n            other: \"balanced blue and gray tones\"\n        };\n        components.push(`using ${defaultColors[category]}`);\n    }\n    // 5. Technical specifications\n    components.push(\"The icon should be suitable for app stores with a square format\");\n    components.push(\"featuring clear, recognizable imagery that works well at small sizes\");\n    components.push(`conveying a ${categoryInfo.mood} feeling`);\n    // 6. Additional context from website content\n    if (websiteData.description) {\n        const shortDesc = websiteData.description.slice(0, 100);\n        components.push(`The website is described as: \"${shortDesc}\"`);\n    }\n    // 7. Style refinements\n    components.push(\"Ensure the design is scalable, memorable, and stands out among other app icons\");\n    components.push(`with a ${categoryInfo.style} aesthetic`);\n    return components.join(\", \") + \".\";\n}\n/**\n * Creates a structured prompt for the AI to generate an even better icon prompt\n */ function createPromptEnhancementRequest(websiteData) {\n    const basicPrompt = generateIconPrompt(websiteData);\n    return `Based on the following website analysis, create an enhanced, detailed prompt for generating a perfect app icon:\n\nWebsite Information:\n- Name: ${websiteData.siteName || websiteData.title || \"Unknown\"}\n- URL: ${websiteData.url}\n- Category: ${websiteData.category}\n- Visual Style: ${websiteData.visualStyle}\n- Description: ${websiteData.description || \"No description available\"}\n- Primary Colors: ${websiteData.primaryColors?.join(\", \") || \"Not detected\"}\n\nInitial Prompt: ${basicPrompt}\n\nPlease enhance this prompt to create a more detailed, creative, and specific description for an AI image generator. Focus on:\n1. Specific visual elements that represent the website's purpose\n2. Appropriate color schemes and styling\n3. Professional app icon design principles\n4. Unique characteristics that make it memorable\n5. Technical requirements for app store submission\n\nEnhanced Prompt:`;\n}\n/**\n * Validates and cleans generated prompts\n */ function validatePrompt(prompt) {\n    const issues = [];\n    let cleanedPrompt = prompt.trim();\n    // Check minimum length\n    if (cleanedPrompt.length < 20) {\n        issues.push(\"Prompt is too short\");\n    }\n    // Check maximum length (most AI models have token limits)\n    if (cleanedPrompt.length > 20000) {\n        issues.push(\"Prompt is too long\");\n        cleanedPrompt = cleanedPrompt.slice(0, 2000) + \"...\";\n    }\n    // Remove potentially problematic content\n    const problematicTerms = [\n        \"nsfw\",\n        \"explicit\",\n        \"violent\",\n        \"inappropriate\"\n    ];\n    const lowerPrompt = cleanedPrompt.toLowerCase();\n    for (const term of problematicTerms){\n        if (lowerPrompt.includes(term)) {\n            issues.push(`Contains potentially inappropriate content: ${term}`);\n        }\n    }\n    // Ensure it mentions \"icon\" or \"app icon\"\n    if (!lowerPrompt.includes(\"icon\") && !lowerPrompt.includes(\"logo\")) {\n        cleanedPrompt = \"App icon: \" + cleanedPrompt;\n    }\n    return {\n        isValid: issues.length === 0,\n        cleanedPrompt,\n        issues: issues.length > 0 ? issues : undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/promptGenerator.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();