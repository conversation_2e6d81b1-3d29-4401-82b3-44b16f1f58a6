"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-prompt-from-url/route";
exports.ids = ["app/api/generate-prompt-from-url/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_prompt_from_url_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-prompt-from-url/route.ts */ \"(rsc)/./src/app/api/generate-prompt-from-url/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-prompt-from-url/route\",\n        pathname: \"/api/generate-prompt-from-url\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-prompt-from-url/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/generate-prompt-from-url/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_prompt_from_url_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-prompt-from-url/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-prompt-from-url/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/generate-prompt-from-url/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/promptGenerator */ \"(rsc)/./src/utils/promptGenerator.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { websiteData } = body;\n        if (!websiteData || !websiteData.url) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Website data is required.\"\n            }, {\n                status: 400\n            });\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Create the enhancement request\n        const enhancementRequest = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__.createPromptEnhancementRequest)(websiteData);\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: enhancementRequest\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"Prompt generation failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let generatedPrompt = result.candidates[0].content.parts[0].text;\n            // Clean up the prompt (remove \"Enhanced Prompt:\" prefix if present)\n            generatedPrompt = generatedPrompt.replace(/^Enhanced Prompt:\\s*/i, \"\").trim();\n            // Validate the generated prompt\n            const validation = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_1__.validatePrompt)(generatedPrompt);\n            if (!validation.isValid && validation.issues) {\n                console.warn(\"Generated prompt has issues:\", validation.issues);\n            }\n            const finalPrompt = validation.cleanedPrompt || generatedPrompt;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: finalPrompt\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate prompt from website data. Please try again.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error generating prompt from URL:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `Failed to generate prompt: ${errorMessage}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-prompt-from-url/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/promptGenerator.ts":
/*!**************************************!*\
  !*** ./src/utils/promptGenerator.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPromptEnhancementRequest: () => (/* binding */ createPromptEnhancementRequest),\n/* harmony export */   generateIconPrompt: () => (/* binding */ generateIconPrompt),\n/* harmony export */   validatePrompt: () => (/* binding */ validatePrompt)\n/* harmony export */ });\n/**\n * Maps content categories to icon style descriptions\n */ const CATEGORY_STYLES = {\n    ecommerce: {\n        elements: [\n            \"shopping bag\",\n            \"cart\",\n            \"storefront\",\n            \"package\",\n            \"credit card\"\n        ],\n        style: \"commercial and trustworthy\",\n        mood: \"professional yet approachable\"\n    },\n    blog: {\n        elements: [\n            \"pen\",\n            \"notebook\",\n            \"speech bubble\",\n            \"document\",\n            \"quill\"\n        ],\n        style: \"editorial and readable\",\n        mood: \"informative and engaging\"\n    },\n    portfolio: {\n        elements: [\n            \"easel\",\n            \"brush\",\n            \"camera\",\n            \"frame\",\n            \"gallery\"\n        ],\n        style: \"creative and artistic\",\n        mood: \"inspiring and professional\"\n    },\n    saas: {\n        elements: [\n            \"cloud\",\n            \"dashboard\",\n            \"graph\",\n            \"network\",\n            \"gear\"\n        ],\n        style: \"tech-forward and modern\",\n        mood: \"innovative and reliable\"\n    },\n    corporate: {\n        elements: [\n            \"building\",\n            \"handshake\",\n            \"briefcase\",\n            \"chart\",\n            \"globe\"\n        ],\n        style: \"professional and established\",\n        mood: \"trustworthy and authoritative\"\n    },\n    creative: {\n        elements: [\n            \"palette\",\n            \"lightbulb\",\n            \"star\",\n            \"magic wand\",\n            \"rainbow\"\n        ],\n        style: \"artistic and expressive\",\n        mood: \"imaginative and vibrant\"\n    },\n    educational: {\n        elements: [\n            \"book\",\n            \"graduation cap\",\n            \"apple\",\n            \"chalkboard\",\n            \"lightbulb\"\n        ],\n        style: \"educational and clear\",\n        mood: \"knowledgeable and approachable\"\n    },\n    other: {\n        elements: [\n            \"circle\",\n            \"square\",\n            \"diamond\",\n            \"star\",\n            \"hexagon\"\n        ],\n        style: \"versatile and clean\",\n        mood: \"neutral and professional\"\n    }\n};\n/**\n * Maps visual styles to design descriptions\n */ const VISUAL_STYLE_DESCRIPTIONS = {\n    modern: \"sleek, contemporary design with clean lines and subtle gradients\",\n    minimalist: \"ultra-clean, simple design with plenty of white space and minimal elements\",\n    corporate: \"professional, business-like appearance with structured layout and conservative colors\",\n    playful: \"fun, energetic design with rounded corners and vibrant, cheerful elements\",\n    elegant: \"sophisticated, refined design with premium feel and subtle luxury touches\",\n    bold: \"strong, impactful design with high contrast and dynamic visual elements\",\n    classic: \"timeless, traditional design with balanced proportions and established conventions\"\n};\n/**\n * Generates a detailed prompt for icon generation based on website data\n */ function generateIconPrompt(websiteData) {\n    const category = websiteData.category || \"other\";\n    const visualStyle = websiteData.visualStyle || \"modern\";\n    const categoryInfo = CATEGORY_STYLES[category];\n    const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle];\n    // Build the prompt components\n    const components = [];\n    // 1. Basic description\n    const siteName = websiteData.siteName || websiteData.title || \"website\";\n    components.push(`Create a professional app icon for \"${siteName}\"`);\n    // 2. Category-specific elements\n    const suggestedElement = categoryInfo.elements[Math.floor(Math.random() * categoryInfo.elements.length)];\n    components.push(`incorporating a ${suggestedElement} as the main element`);\n    // 3. Visual style\n    components.push(`designed in a ${visualStyle} style with ${styleDescription}`);\n    // 4. Color scheme - Use exact hex values\n    if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {\n        const colors = websiteData.primaryColors.slice(0, 3);\n        if (colors.length >= 1) {\n            components.push(`using primary color ${colors[0]}`);\n        }\n        if (colors.length >= 2) {\n            components.push(`secondary color ${colors[1]}`);\n        }\n        if (colors.length >= 3) {\n            components.push(`accent color ${colors[2]}`);\n        }\n        // Add emphasis for exact color usage\n        components.push(`Use these exact hex color values: ${colors.join(\", \")}`);\n    } else {\n        // Default color suggestions based on category\n        const defaultColors = {\n            ecommerce: \"blue and green tones for trust and growth\",\n            blog: \"warm orange and blue tones for readability\",\n            portfolio: \"creative purple and teal tones\",\n            saas: \"modern blue and gray tones\",\n            corporate: \"professional navy and silver tones\",\n            creative: \"vibrant rainbow or artistic color combinations\",\n            educational: \"friendly blue and yellow tones\",\n            other: \"balanced blue and gray tones\"\n        };\n        components.push(`using ${defaultColors[category]}`);\n    }\n    // 5. Technical specifications\n    components.push(\"The icon should be suitable for app stores with a square format\");\n    components.push(\"featuring clear, recognizable imagery that works well at small sizes\");\n    components.push(`conveying a ${categoryInfo.mood} feeling`);\n    // 6. Additional context from website content\n    if (websiteData.description) {\n        const shortDesc = websiteData.description.slice(0, 100);\n        components.push(`The website is described as: \"${shortDesc}\"`);\n    }\n    // 7. Style refinements\n    components.push(\"Ensure the design is scalable, memorable, and stands out among other app icons\");\n    components.push(`with a ${categoryInfo.style} aesthetic`);\n    return components.join(\", \") + \".\";\n}\n/**\n * Creates a structured prompt for the AI to generate an even better icon prompt\n */ function createPromptEnhancementRequest(websiteData) {\n    const basicPrompt = generateIconPrompt(websiteData);\n    return `Based on the following website analysis, create an enhanced, detailed prompt for generating a perfect app icon:\n\nWebsite Information:\n- Name: ${websiteData.siteName || websiteData.title || \"Unknown\"}\n- URL: ${websiteData.url}\n- Category: ${websiteData.category}\n- Visual Style: ${websiteData.visualStyle}\n- Description: ${websiteData.description || \"No description available\"}\n- Primary Colors: ${websiteData.primaryColors?.join(\", \") || \"Not detected\"}\n\nInitial Prompt: ${basicPrompt}\n\nPlease enhance this prompt to create a more detailed, creative, and specific description for an AI image generator. Focus on:\n1. Specific visual elements that represent the website's purpose\n2. Using the EXACT hex color values provided (do not interpret or change them)\n3. Professional app icon design principles\n4. Unique characteristics that make it memorable\n5. Technical requirements for app store submission\n\nIMPORTANT: If hex color values are provided, use them exactly as specified without any color interpretation or description.\n\nEnhanced Prompt:`;\n}\n/**\n * Validates and cleans generated prompts\n */ function validatePrompt(prompt) {\n    const issues = [];\n    let cleanedPrompt = prompt.trim();\n    // Check minimum length\n    if (cleanedPrompt.length < 20) {\n        issues.push(\"Prompt is too short\");\n    }\n    // Check maximum length (most AI models have token limits)\n    if (cleanedPrompt.length > 20000) {\n        issues.push(\"Prompt is too long\");\n        cleanedPrompt = cleanedPrompt.slice(0, 2000) + \"...\";\n    }\n    // Remove potentially problematic content\n    const problematicTerms = [\n        \"nsfw\",\n        \"explicit\",\n        \"violent\",\n        \"inappropriate\"\n    ];\n    const lowerPrompt = cleanedPrompt.toLowerCase();\n    for (const term of problematicTerms){\n        if (lowerPrompt.includes(term)) {\n            issues.push(`Contains potentially inappropriate content: ${term}`);\n        }\n    }\n    // Ensure it mentions \"icon\" or \"app icon\"\n    if (!lowerPrompt.includes(\"icon\") && !lowerPrompt.includes(\"logo\")) {\n        cleanedPrompt = \"App icon: \" + cleanedPrompt;\n    }\n    return {\n        isValid: issues.length === 0,\n        cleanedPrompt,\n        issues: issues.length > 0 ? issues : undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/promptGenerator.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-prompt-from-url%2Froute&page=%2Fapi%2Fgenerate-prompt-from-url%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-prompt-from-url%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();