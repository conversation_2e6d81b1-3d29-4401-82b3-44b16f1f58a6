"use strict";(()=>{var e={};e.id=495,e.ids=[495],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7598:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>u});var a={};r.r(a),r.d(a,{POST:()=>p});var o=r(9303),n=r(8716),s=r(670),i=r(7070),l=r(7971);async function p(e){try{let{websiteData:t}=await e.json();if(!t||!t.url)return i.NextResponse.json({success:!1,error:"Website data is required."},{status:400});let r=process.env.GOOGLE_AI_API_KEY,a=process.env.GEMINI_MODEL||"gemini-2.5-flash-preview-05-20";if(!r)return i.NextResponse.json({success:!1,error:"API key not configured. Please set GOOGLE_AI_API_KEY environment variable."},{status:500});let o=(0,l.oh)(t),n=`https://generativelanguage.googleapis.com/v1beta/models/${a}:generateContent?key=${r}`,s=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{role:"user",parts:[{text:o}]}]})});if(!s.ok){let e=await s.json();throw Error(e.error?.message||"Prompt generation failed.")}let p=await s.json();if(!p.candidates||!(p.candidates.length>0)||!p.candidates[0].content||!p.candidates[0].content.parts||!(p.candidates[0].content.parts.length>0))return i.NextResponse.json({success:!1,error:"Could not generate prompt from website data. Please try again."},{status:500});{let e=p.candidates[0].content.parts[0].text;e=e.replace(/^Enhanced Prompt:\s*/i,"").trim();let t=(0,l.WT)(e);!t.isValid&&t.issues&&console.warn("Generated prompt has issues:",t.issues);let r=t.cleanedPrompt||e;return i.NextResponse.json({success:!0,prompt:r})}}catch(t){console.error("Error generating prompt from URL:",t);let e=t instanceof Error?t.message:"Unknown error occurred";return i.NextResponse.json({success:!1,error:`Failed to generate prompt: ${e}`},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/generate-prompt-from-url/route",pathname:"/api/generate-prompt-from-url",filename:"route",bundlePath:"app/api/generate-prompt-from-url/route"},resolvedPagePath:"/Users/<USER>/src/personal/icon-generator/src/app/api/generate-prompt-from-url/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:u,serverHooks:m}=c,h="/api/generate-prompt-from-url/route";function g(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:u})}},7971:(e,t,r)=>{r.d(t,{WT:()=>i,oh:()=>s,vm:()=>n});let a={ecommerce:{elements:["shopping bag","cart","storefront","package","credit card"],style:"commercial and trustworthy",mood:"professional yet approachable"},blog:{elements:["pen","notebook","speech bubble","document","quill"],style:"editorial and readable",mood:"informative and engaging"},portfolio:{elements:["easel","brush","camera","frame","gallery"],style:"creative and artistic",mood:"inspiring and professional"},saas:{elements:["cloud","dashboard","graph","network","gear"],style:"tech-forward and modern",mood:"innovative and reliable"},corporate:{elements:["building","handshake","briefcase","chart","globe"],style:"professional and established",mood:"trustworthy and authoritative"},creative:{elements:["palette","lightbulb","star","magic wand","rainbow"],style:"artistic and expressive",mood:"imaginative and vibrant"},educational:{elements:["book","graduation cap","apple","chalkboard","lightbulb"],style:"educational and clear",mood:"knowledgeable and approachable"},other:{elements:["circle","square","diamond","star","hexagon"],style:"versatile and clean",mood:"neutral and professional"}},o={modern:"sleek, contemporary design with clean lines and subtle gradients",minimalist:"ultra-clean, simple design with plenty of white space and minimal elements",corporate:"professional, business-like appearance with structured layout and conservative colors",playful:"fun, energetic design with rounded corners and vibrant, cheerful elements",elegant:"sophisticated, refined design with premium feel and subtle luxury touches",bold:"strong, impactful design with high contrast and dynamic visual elements",classic:"timeless, traditional design with balanced proportions and established conventions"};function n(e){let t=e.category||"other",r=e.visualStyle||"modern",n=a[t],s=o[r],i=[],l=e.siteName||e.title||"website";i.push(`Create a professional app icon for "${l}"`);let p=n.elements[Math.floor(Math.random()*n.elements.length)];if(i.push(`incorporating a ${p} as the main element`),i.push(`designed in a ${r} style with ${s}`),e.primaryColors&&e.primaryColors.length>0){let t=e.primaryColors.slice(0,3);t.length>=1&&i.push(`using primary color ${t[0]}`),t.length>=2&&i.push(`secondary color ${t[1]}`),t.length>=3&&i.push(`accent color ${t[2]}`),i.push(`Use these exact hex color values: ${t.join(", ")}`)}else i.push(`using ${{ecommerce:"blue and green tones for trust and growth",blog:"warm orange and blue tones for readability",portfolio:"creative purple and teal tones",saas:"modern blue and gray tones",corporate:"professional navy and silver tones",creative:"vibrant rainbow or artistic color combinations",educational:"friendly blue and yellow tones",other:"balanced blue and gray tones"}[t]}`);if(i.push("The icon should be suitable for app stores with a square format"),i.push("featuring clear, recognizable imagery that works well at small sizes"),i.push(`conveying a ${n.mood} feeling`),e.description){let t=e.description.slice(0,100);i.push(`The website is described as: "${t}"`)}return i.push("Ensure the design is scalable, memorable, and stands out among other app icons"),i.push(`with a ${n.style} aesthetic`),i.join(", ")+"."}function s(e){let t=n(e);return`Based on the following website analysis, create an enhanced, detailed prompt for generating a perfect app icon:

Website Information:
- Name: ${e.siteName||e.title||"Unknown"}
- URL: ${e.url}
- Category: ${e.category}
- Visual Style: ${e.visualStyle}
- Description: ${e.description||"No description available"}
- Primary Colors: ${e.primaryColors?.join(", ")||"Not detected"}

Initial Prompt: ${t}

Please enhance this prompt to create a more detailed, creative, and specific description for an AI image generator. Focus on:
1. Specific visual elements that represent the website's purpose
2. Using the EXACT hex color values provided (do not interpret or change them)
3. Professional app icon design principles
4. Unique characteristics that make it memorable
5. Technical requirements for app store submission

IMPORTANT: If hex color values are provided, use them exactly as specified without any color interpretation or description.

Enhanced Prompt:`}function i(e){let t=[],r=e.trim();r.length<20&&t.push("Prompt is too short"),r.length>2e4&&(t.push("Prompt is too long"),r=r.slice(0,2e3)+"...");let a=r.toLowerCase();for(let e of["nsfw","explicit","violent","inappropriate"])a.includes(e)&&t.push(`Contains potentially inappropriate content: ${e}`);return a.includes("icon")||a.includes("logo")||(r="App icon: "+r),{isValid:0===t.length,cleanedPrompt:r,issues:t.length>0?t:void 0}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,972],()=>r(7598));module.exports=a})();