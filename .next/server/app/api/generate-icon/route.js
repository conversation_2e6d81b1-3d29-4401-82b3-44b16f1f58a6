"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-icon/route";
exports.ids = ["app/api/generate-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-icon%2Froute&page=%2Fapi%2Fgenerate-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-icon%2Froute&page=%2Fapi%2Fgenerate-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-icon/route.ts */ \"(rsc)/./src/app/api/generate-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-icon/route\",\n        pathname: \"/api/generate-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/generate-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_generate_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-icon%2Froute&page=%2Fapi%2Fgenerate-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-icon/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/generate-icon/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { prompt } = body;\n        if (!prompt || prompt.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please enter a description for the app icon.\"\n            }, {\n                status: 400\n            });\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const imagenModel = process.env.IMAGEN_MODEL || \"imagen-4.0-generate-preview-06-06\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Construct the payload for the API call\n        const payload = {\n            instances: {\n                prompt: prompt.trim()\n            },\n            parameters: {\n                sampleCount: 1\n            }\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${imagenModel}:predict?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"Image generation failed.\");\n        }\n        const result = await response.json();\n        if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {\n            const imageUrl = `data:image/png;base64,${result.predictions[0].bytesBase64Encoded}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                imageUrl\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"No image data received. Please try again with a different prompt.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error generating image:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `Failed to generate icon: ${errorMessage}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-icon/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-icon%2Froute&page=%2Fapi%2Fgenerate-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();