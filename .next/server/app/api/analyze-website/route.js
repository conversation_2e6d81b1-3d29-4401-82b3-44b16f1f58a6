"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-website/route";
exports.ids = ["app/api/analyze-website/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-website/route.ts */ \"(rsc)/./src/app/api/analyze-website/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-website/route\",\n        pathname: \"/api/analyze-website\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-website/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-website/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-website/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-website/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/analyze-website/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Scrape and analyze the website\n        const websiteData = await (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.scrapeWebsite)(url.trim());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: websiteData\n        });\n    } catch (error) {\n        console.error(\"Error analyzing website:\", error);\n        // Handle specific error types\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else if (error.message.includes(\"Invalid URL\")) {\n                errorMessage = \"Please provide a valid URL (e.g., https://example.com).\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-website/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeWebsiteContent: () => (/* binding */ analyzeWebsiteContent),\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Analyzes website content to determine category and style\n */ function analyzeWebsiteContent(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Determine content category\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\") || allText.includes(\"product\") || allText.includes(\"store\") || allText.includes(\"ecommerce\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\") || allText.includes(\"post\") || $(\"article\").length > 0 || $(\".blog\").length > 0) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\") || allText.includes(\"project\") || $(\".portfolio\").length > 0 || $(\".gallery\").length > 0) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\") || allText.includes(\"app\") || allText.includes(\"platform\") || allText.includes(\"dashboard\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\") || allText.includes(\"corporate\") || allText.includes(\"enterprise\") || allText.includes(\"about us\")) {\n        category = \"corporate\";\n    } else if (allText.includes(\"design\") || allText.includes(\"creative\") || allText.includes(\"art\") || allText.includes(\"studio\") || allText.includes(\"agency\")) {\n        category = \"creative\";\n    } else if (allText.includes(\"learn\") || allText.includes(\"course\") || allText.includes(\"education\") || allText.includes(\"tutorial\") || allText.includes(\"school\")) {\n        category = \"educational\";\n    }\n    // Determine visual style based on CSS classes and content\n    let visualStyle = \"modern\";\n    const hasMinimalClasses = $(\".minimal, .clean, .simple\").length > 0;\n    const hasBoldClasses = $(\".bold, .strong, .vibrant\").length > 0;\n    const hasElegantClasses = $(\".elegant, .luxury, .premium\").length > 0;\n    const hasPlayfulClasses = $(\".fun, .playful, .colorful\").length > 0;\n    const hasCorporateClasses = $(\".corporate, .professional, .business\").length > 0;\n    if (hasMinimalClasses || allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (hasBoldClasses || allText.includes(\"bold\") || allText.includes(\"vibrant\")) {\n        visualStyle = \"bold\";\n    } else if (hasElegantClasses || allText.includes(\"elegant\") || allText.includes(\"luxury\")) {\n        visualStyle = \"elegant\";\n    } else if (hasPlayfulClasses || allText.includes(\"fun\") || allText.includes(\"playful\")) {\n        visualStyle = \"playful\";\n    } else if (hasCorporateClasses || category === \"corporate\") {\n        visualStyle = \"corporate\";\n    } else if (allText.includes(\"classic\") || allText.includes(\"traditional\")) {\n        visualStyle = \"classic\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS (simplified approach)\n */ function extractColors(html) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colors = [];\n    // Extract colors from inline styles and CSS\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            const colorMatches = style.match(/(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\([^)]+\\)|rgba\\([^)]+\\))/g);\n            if (colorMatches) {\n                colors.push(...colorMatches);\n            }\n        }\n    });\n    // Extract from style tags\n    $(\"style\").each((_, element)=>{\n        const css = $(element).html() || \"\";\n        const colorMatches = css.match(/(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\([^)]+\\)|rgba\\([^)]+\\))/g);\n        if (colorMatches) {\n            colors.push(...colorMatches);\n        }\n    });\n    // Remove duplicates and filter out common colors\n    const uniqueColors = [\n        ...new Set(colors)\n    ].filter((color)=>![\n            \"#000000\",\n            \"#ffffff\",\n            \"#000\",\n            \"#fff\",\n            \"rgb(0,0,0)\",\n            \"rgb(255,255,255)\"\n        ].includes(color.toLowerCase())).slice(0, 5); // Limit to 5 colors\n    return uniqueColors;\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Analyze content\n        const { category, visualStyle } = analyzeWebsiteContent(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();