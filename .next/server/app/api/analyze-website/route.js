"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-website/route";
exports.ids = ["app/api/analyze-website/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-website/route.ts */ \"(rsc)/./src/app/api/analyze-website/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-website/route\",\n        pathname: \"/api/analyze-website\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-website/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-website/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-website/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhbmFseXplLXdlYnNpdGUlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFuYWx5emUtd2Vic2l0ZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFuYWx5emUtd2Vic2l0ZSUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRnBpZW5hYXJhbmtlciUyRnNyYyUyRnBlcnNvbmFsJTJGaWNvbi1nZW5lcmF0b3IlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGcGllbmFhcmFua2VyJTJGc3JjJTJGcGVyc29uYWwlMkZpY29uLWdlbmVyYXRvciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDb0M7QUFDakg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pY29uLWdlbmVyYXRvci8/YjU2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVXNlcnMvcGllbmFhcmFua2VyL3NyYy9wZXJzb25hbC9pY29uLWdlbmVyYXRvci9zcmMvYXBwL2FwaS9hbmFseXplLXdlYnNpdGUvcm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FuYWx5emUtd2Vic2l0ZS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FuYWx5emUtd2Vic2l0ZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYW5hbHl6ZS13ZWJzaXRlL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiL1VzZXJzL3BpZW5hYXJhbmtlci9zcmMvcGVyc29uYWwvaWNvbi1nZW5lcmF0b3Ivc3JjL2FwcC9hcGkvYW5hbHl6ZS13ZWJzaXRlL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9hbmFseXplLXdlYnNpdGUvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-website/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/analyze-website/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n/* harmony import */ var _utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/promptGenerator */ \"(rsc)/./src/utils/promptGenerator.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Scrape and analyze the website\n        const websiteData = await (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.scrapeWebsite)(url.trim());\n        // Generate the initial prompt\n        const generatedPrompt = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__.generateIconPrompt)(websiteData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: websiteData,\n            generatedPrompt\n        });\n    } catch (error) {\n        console.error(\"Error analyzing website:\", error);\n        // Handle specific error types\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else if (error.message.includes(\"Invalid URL\")) {\n                errorMessage = \"Please provide a valid URL (e.g., https://example.com).\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-website/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/promptGenerator.ts":
/*!**************************************!*\
  !*** ./src/utils/promptGenerator.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPromptEnhancementRequest: () => (/* binding */ createPromptEnhancementRequest),\n/* harmony export */   generateIconPrompt: () => (/* binding */ generateIconPrompt),\n/* harmony export */   validatePrompt: () => (/* binding */ validatePrompt)\n/* harmony export */ });\n/**\n * Maps content categories to icon style descriptions\n */ const CATEGORY_STYLES = {\n    ecommerce: {\n        elements: [\n            \"shopping bag\",\n            \"cart\",\n            \"storefront\",\n            \"package\",\n            \"credit card\"\n        ],\n        style: \"commercial and trustworthy\",\n        mood: \"professional yet approachable\"\n    },\n    blog: {\n        elements: [\n            \"pen\",\n            \"notebook\",\n            \"speech bubble\",\n            \"document\",\n            \"quill\"\n        ],\n        style: \"editorial and readable\",\n        mood: \"informative and engaging\"\n    },\n    portfolio: {\n        elements: [\n            \"easel\",\n            \"brush\",\n            \"camera\",\n            \"frame\",\n            \"gallery\"\n        ],\n        style: \"creative and artistic\",\n        mood: \"inspiring and professional\"\n    },\n    saas: {\n        elements: [\n            \"cloud\",\n            \"dashboard\",\n            \"graph\",\n            \"network\",\n            \"gear\"\n        ],\n        style: \"tech-forward and modern\",\n        mood: \"innovative and reliable\"\n    },\n    corporate: {\n        elements: [\n            \"building\",\n            \"handshake\",\n            \"briefcase\",\n            \"chart\",\n            \"globe\"\n        ],\n        style: \"professional and established\",\n        mood: \"trustworthy and authoritative\"\n    },\n    creative: {\n        elements: [\n            \"palette\",\n            \"lightbulb\",\n            \"star\",\n            \"magic wand\",\n            \"rainbow\"\n        ],\n        style: \"artistic and expressive\",\n        mood: \"imaginative and vibrant\"\n    },\n    educational: {\n        elements: [\n            \"book\",\n            \"graduation cap\",\n            \"apple\",\n            \"chalkboard\",\n            \"lightbulb\"\n        ],\n        style: \"educational and clear\",\n        mood: \"knowledgeable and approachable\"\n    },\n    other: {\n        elements: [\n            \"circle\",\n            \"square\",\n            \"diamond\",\n            \"star\",\n            \"hexagon\"\n        ],\n        style: \"versatile and clean\",\n        mood: \"neutral and professional\"\n    }\n};\n/**\n * Maps visual styles to design descriptions\n */ const VISUAL_STYLE_DESCRIPTIONS = {\n    modern: \"sleek, contemporary design with clean lines and subtle gradients\",\n    minimalist: \"ultra-clean, simple design with plenty of white space and minimal elements\",\n    corporate: \"professional, business-like appearance with structured layout and conservative colors\",\n    playful: \"fun, energetic design with rounded corners and vibrant, cheerful elements\",\n    elegant: \"sophisticated, refined design with premium feel and subtle luxury touches\",\n    bold: \"strong, impactful design with high contrast and dynamic visual elements\",\n    classic: \"timeless, traditional design with balanced proportions and established conventions\"\n};\n/**\n * Generates a detailed prompt for icon generation based on website data\n */ function generateIconPrompt(websiteData) {\n    const category = websiteData.category || \"other\";\n    const visualStyle = websiteData.visualStyle || \"modern\";\n    const categoryInfo = CATEGORY_STYLES[category];\n    const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle];\n    // Build the prompt components\n    const components = [];\n    // 1. Basic description\n    const siteName = websiteData.siteName || websiteData.title || \"website\";\n    components.push(`Create a professional app icon for \"${siteName}\"`);\n    // 2. Category-specific elements\n    const suggestedElement = categoryInfo.elements[Math.floor(Math.random() * categoryInfo.elements.length)];\n    components.push(`incorporating a ${suggestedElement} as the main element`);\n    // 3. Visual style\n    components.push(`designed in a ${visualStyle} style with ${styleDescription}`);\n    // 4. Color scheme - Use exact hex values\n    if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {\n        const colors = websiteData.primaryColors.slice(0, 3);\n        if (colors.length >= 1) {\n            components.push(`using primary color ${colors[0]}`);\n        }\n        if (colors.length >= 2) {\n            components.push(`secondary color ${colors[1]}`);\n        }\n        if (colors.length >= 3) {\n            components.push(`accent color ${colors[2]}`);\n        }\n        // Add emphasis for exact color usage\n        components.push(`Use these exact hex color values: ${colors.join(\", \")}`);\n    } else {\n        // Default color suggestions based on category\n        const defaultColors = {\n            ecommerce: \"blue and green tones for trust and growth\",\n            blog: \"warm orange and blue tones for readability\",\n            portfolio: \"creative purple and teal tones\",\n            saas: \"modern blue and gray tones\",\n            corporate: \"professional navy and silver tones\",\n            creative: \"vibrant rainbow or artistic color combinations\",\n            educational: \"friendly blue and yellow tones\",\n            other: \"balanced blue and gray tones\"\n        };\n        components.push(`using ${defaultColors[category]}`);\n    }\n    // 5. Technical specifications\n    components.push(\"The icon should be suitable for app stores with a square format\");\n    components.push(\"featuring clear, recognizable imagery that works well at small sizes\");\n    components.push(`conveying a ${categoryInfo.mood} feeling`);\n    // 6. Additional context from website content\n    if (websiteData.description) {\n        const shortDesc = websiteData.description.slice(0, 100);\n        components.push(`The website is described as: \"${shortDesc}\"`);\n    }\n    // 7. Style refinements\n    components.push(\"Ensure the design is scalable, memorable, and stands out among other app icons\");\n    components.push(`with a ${categoryInfo.style} aesthetic`);\n    return components.join(\", \") + \".\";\n}\n/**\n * Creates a structured prompt for the AI to generate an even better icon prompt\n */ function createPromptEnhancementRequest(websiteData) {\n    const basicPrompt = generateIconPrompt(websiteData);\n    return `Based on the following website analysis, create an enhanced, detailed prompt for generating a perfect app icon:\n\nWebsite Information:\n- Name: ${websiteData.siteName || websiteData.title || \"Unknown\"}\n- URL: ${websiteData.url}\n- Category: ${websiteData.category}\n- Visual Style: ${websiteData.visualStyle}\n- Description: ${websiteData.description || \"No description available\"}\n- Primary Colors: ${websiteData.primaryColors?.join(\", \") || \"Not detected\"}\n\nInitial Prompt: ${basicPrompt}\n\nPlease enhance this prompt to create a more detailed, creative, and specific description for an AI image generator. Focus on:\n1. Specific visual elements that represent the website's purpose\n2. Using the EXACT hex color values provided (do not interpret or change them)\n3. Professional app icon design principles\n4. Unique characteristics that make it memorable\n5. Technical requirements for app store submission\n\nIMPORTANT: If hex color values are provided, use them exactly as specified without any color interpretation or description.\n\nEnhanced Prompt:`;\n}\n/**\n * Validates and cleans generated prompts\n */ function validatePrompt(prompt) {\n    const issues = [];\n    let cleanedPrompt = prompt.trim();\n    // Check minimum length\n    if (cleanedPrompt.length < 20) {\n        issues.push(\"Prompt is too short\");\n    }\n    // Check maximum length (most AI models have token limits)\n    if (cleanedPrompt.length > 20000) {\n        issues.push(\"Prompt is too long\");\n        cleanedPrompt = cleanedPrompt.slice(0, 2000) + \"...\";\n    }\n    // Remove potentially problematic content\n    const problematicTerms = [\n        \"nsfw\",\n        \"explicit\",\n        \"violent\",\n        \"inappropriate\"\n    ];\n    const lowerPrompt = cleanedPrompt.toLowerCase();\n    for (const term of problematicTerms){\n        if (lowerPrompt.includes(term)) {\n            issues.push(`Contains potentially inappropriate content: ${term}`);\n        }\n    }\n    // Ensure it mentions \"icon\" or \"app icon\"\n    if (!lowerPrompt.includes(\"icon\") && !lowerPrompt.includes(\"logo\")) {\n        cleanedPrompt = \"App icon: \" + cleanedPrompt;\n    }\n    return {\n        isValid: issues.length === 0,\n        cleanedPrompt,\n        issues: issues.length > 0 ? issues : undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/promptGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeWebsiteContent: () => (/* binding */ analyzeWebsiteContent),\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Analyzes website content to determine category and style\n */ function analyzeWebsiteContent(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Determine content category\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\") || allText.includes(\"product\") || allText.includes(\"store\") || allText.includes(\"ecommerce\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\") || allText.includes(\"post\") || $(\"article\").length > 0 || $(\".blog\").length > 0) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\") || allText.includes(\"project\") || $(\".portfolio\").length > 0 || $(\".gallery\").length > 0) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\") || allText.includes(\"app\") || allText.includes(\"platform\") || allText.includes(\"dashboard\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\") || allText.includes(\"corporate\") || allText.includes(\"enterprise\") || allText.includes(\"about us\")) {\n        category = \"corporate\";\n    } else if (allText.includes(\"design\") || allText.includes(\"creative\") || allText.includes(\"art\") || allText.includes(\"studio\") || allText.includes(\"agency\")) {\n        category = \"creative\";\n    } else if (allText.includes(\"learn\") || allText.includes(\"course\") || allText.includes(\"education\") || allText.includes(\"tutorial\") || allText.includes(\"school\")) {\n        category = \"educational\";\n    }\n    // Determine visual style based on CSS classes and content\n    let visualStyle = \"modern\";\n    const hasMinimalClasses = $(\".minimal, .clean, .simple\").length > 0;\n    const hasBoldClasses = $(\".bold, .strong, .vibrant\").length > 0;\n    const hasElegantClasses = $(\".elegant, .luxury, .premium\").length > 0;\n    const hasPlayfulClasses = $(\".fun, .playful, .colorful\").length > 0;\n    const hasCorporateClasses = $(\".corporate, .professional, .business\").length > 0;\n    if (hasMinimalClasses || allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (hasBoldClasses || allText.includes(\"bold\") || allText.includes(\"vibrant\")) {\n        visualStyle = \"bold\";\n    } else if (hasElegantClasses || allText.includes(\"elegant\") || allText.includes(\"luxury\")) {\n        visualStyle = \"elegant\";\n    } else if (hasPlayfulClasses || allText.includes(\"fun\") || allText.includes(\"playful\")) {\n        visualStyle = \"playful\";\n    } else if (hasCorporateClasses || category === \"corporate\") {\n        visualStyle = \"corporate\";\n    } else if (allText.includes(\"classic\") || allText.includes(\"traditional\")) {\n        visualStyle = \"classic\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    }\n                });\n            }\n        }\n        // Check for brand-related elements (headers, logos, buttons)\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    $('style, link[rel=\"stylesheet\"]').each((_, element)=>{\n        const css = $(element).html() || \"\";\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                }\n            });\n        }\n    });\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    return sortedColors;\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Analyze content\n        const { category, visualStyle } = analyzeWebsiteContent(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();