"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-website/route";
exports.ids = ["app/api/analyze-website/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-website/route.ts */ \"(rsc)/./src/app/api/analyze-website/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-website/route\",\n        pathname: \"/api/analyze-website\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-website/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-website/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_website_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-website/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-website/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/analyze-website/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n/* harmony import */ var _utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/promptGenerator */ \"(rsc)/./src/utils/promptGenerator.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Scrape and analyze the website\n        const websiteData = await (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.scrapeWebsite)(url.trim());\n        // Generate the initial prompt\n        const generatedPrompt = (0,_utils_promptGenerator__WEBPACK_IMPORTED_MODULE_2__.generateIconPrompt)(websiteData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: websiteData,\n            generatedPrompt\n        });\n    } catch (error) {\n        console.error(\"Error analyzing website:\", error);\n        // Handle specific error types\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else if (error.message.includes(\"Invalid URL\")) {\n                errorMessage = \"Please provide a valid URL (e.g., https://example.com).\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-website/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/promptGenerator.ts":
/*!**************************************!*\
  !*** ./src/utils/promptGenerator.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPromptEnhancementRequest: () => (/* binding */ createPromptEnhancementRequest),\n/* harmony export */   generateIconPrompt: () => (/* binding */ generateIconPrompt),\n/* harmony export */   validatePrompt: () => (/* binding */ validatePrompt)\n/* harmony export */ });\n/**\n * Maps content categories to icon style descriptions\n */ const CATEGORY_STYLES = {\n    ecommerce: {\n        elements: [\n            \"shopping bag\",\n            \"cart\",\n            \"storefront\",\n            \"package\",\n            \"credit card\"\n        ],\n        style: \"commercial and trustworthy\",\n        mood: \"professional yet approachable\"\n    },\n    blog: {\n        elements: [\n            \"pen\",\n            \"notebook\",\n            \"speech bubble\",\n            \"document\",\n            \"quill\"\n        ],\n        style: \"editorial and readable\",\n        mood: \"informative and engaging\"\n    },\n    portfolio: {\n        elements: [\n            \"easel\",\n            \"brush\",\n            \"camera\",\n            \"frame\",\n            \"gallery\"\n        ],\n        style: \"creative and artistic\",\n        mood: \"inspiring and professional\"\n    },\n    saas: {\n        elements: [\n            \"cloud\",\n            \"dashboard\",\n            \"graph\",\n            \"network\",\n            \"gear\"\n        ],\n        style: \"tech-forward and modern\",\n        mood: \"innovative and reliable\"\n    },\n    corporate: {\n        elements: [\n            \"building\",\n            \"handshake\",\n            \"briefcase\",\n            \"chart\",\n            \"globe\"\n        ],\n        style: \"professional and established\",\n        mood: \"trustworthy and authoritative\"\n    },\n    creative: {\n        elements: [\n            \"palette\",\n            \"lightbulb\",\n            \"star\",\n            \"magic wand\",\n            \"rainbow\"\n        ],\n        style: \"artistic and expressive\",\n        mood: \"imaginative and vibrant\"\n    },\n    educational: {\n        elements: [\n            \"book\",\n            \"graduation cap\",\n            \"apple\",\n            \"chalkboard\",\n            \"lightbulb\"\n        ],\n        style: \"educational and clear\",\n        mood: \"knowledgeable and approachable\"\n    },\n    other: {\n        elements: [\n            \"circle\",\n            \"square\",\n            \"diamond\",\n            \"star\",\n            \"hexagon\"\n        ],\n        style: \"versatile and clean\",\n        mood: \"neutral and professional\"\n    }\n};\n/**\n * Maps visual styles to design descriptions\n */ const VISUAL_STYLE_DESCRIPTIONS = {\n    modern: \"sleek, contemporary design with clean lines and subtle gradients\",\n    minimalist: \"ultra-clean, simple design with plenty of white space and minimal elements\",\n    corporate: \"professional, business-like appearance with structured layout and conservative colors\",\n    playful: \"fun, energetic design with rounded corners and vibrant, cheerful elements\",\n    elegant: \"sophisticated, refined design with premium feel and subtle luxury touches\",\n    bold: \"strong, impactful design with high contrast and dynamic visual elements\",\n    classic: \"timeless, traditional design with balanced proportions and established conventions\"\n};\n/**\n * Generates a comprehensive, detailed prompt for icon generation based on website data\n */ function generateIconPrompt(websiteData) {\n    const category = websiteData.category || \"other\";\n    const visualStyle = websiteData.visualStyle || \"modern\";\n    const categoryInfo = CATEGORY_STYLES[category];\n    const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle];\n    // Build comprehensive prompt components\n    const components = [];\n    // 1. Detailed site identification and branding\n    const siteName = websiteData.siteName || websiteData.title || \"website\";\n    const siteNameInfo = websiteData.siteName && websiteData.title && websiteData.siteName !== websiteData.title ? `\"${websiteData.siteName}\" (${websiteData.title})` : `\"${siteName}\"`;\n    components.push(`Create a professional app icon for ${siteNameInfo}`);\n    // 2. Complete website description and context\n    if (websiteData.description) {\n        components.push(`Website description: \"${websiteData.description}\"`);\n    }\n    // 3. AI Analysis insights (if available)\n    if (websiteData.aiAnalysis) {\n        components.push(`AI Analysis - Primary Purpose: ${websiteData.aiAnalysis.primaryPurpose}`);\n        components.push(`Brand Personality: ${websiteData.aiAnalysis.brandPersonality}`);\n        components.push(`Target Audience: ${websiteData.aiAnalysis.targetAudience}`);\n        components.push(`Industry Context: ${websiteData.aiAnalysis.industryContext}`);\n        if (websiteData.aiAnalysis.keyFeatures.length > 0) {\n            components.push(`Key Features: ${websiteData.aiAnalysis.keyFeatures.join(\", \")}`);\n        }\n        if (websiteData.aiAnalysis.designCharacteristics.length > 0) {\n            components.push(`Design Characteristics: ${websiteData.aiAnalysis.designCharacteristics.join(\", \")}`);\n        }\n    }\n    // 4. Comprehensive keyword context\n    if (websiteData.keywords && websiteData.keywords.length > 0) {\n        components.push(`Key website themes and topics: ${websiteData.keywords.join(\", \")}`);\n    }\n    // 4. Category-specific design elements with multiple options\n    const allElements = categoryInfo.elements;\n    if (allElements.length > 1) {\n        components.push(`Incorporate design elements that represent ${category} websites, such as: ${allElements.join(\", \")}`);\n    } else {\n        components.push(`incorporating a ${allElements[0]} as a key design element`);\n    }\n    // 5. Detailed visual style specification\n    components.push(`Design the icon in a ${visualStyle} style characterized by: ${styleDescription}`);\n    components.push(`The overall aesthetic should be ${categoryInfo.style} and ${categoryInfo.mood}`);\n    // 6. Comprehensive color scheme with full context\n    if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {\n        const allColors = websiteData.primaryColors;\n        // Primary color\n        components.push(`Primary brand color: ${allColors[0]}`);\n        // Secondary colors\n        if (allColors.length > 1) {\n            components.push(`Secondary colors: ${allColors.slice(1).join(\", \")}`);\n        }\n        // Complete color palette instruction\n        components.push(`Use this exact color palette: ${allColors.join(\", \")}`);\n        components.push(`These colors represent the website's brand identity and should be prominently featured in the icon design`);\n    } else {\n        // Enhanced default color suggestions\n        const defaultColors = {\n            ecommerce: \"professional blue (#2563eb) and trust-building green (#059669) tones\",\n            blog: \"engaging orange (#ea580c) and readable blue (#2563eb) combination\",\n            portfolio: \"creative purple (#7c3aed) and modern teal (#0891b2) palette\",\n            saas: \"tech-forward blue (#1d4ed8) and sophisticated gray (#374151) scheme\",\n            corporate: \"authoritative navy (#1e3a8a) and premium silver (#6b7280) colors\",\n            creative: \"vibrant multi-color palette with artistic flair\",\n            educational: \"approachable blue (#2563eb) and optimistic yellow (#ca8a04) tones\",\n            other: \"balanced blue (#3b82f6) and neutral gray (#6b7280) combination\"\n        };\n        components.push(`Color scheme: ${defaultColors[category]}`);\n    }\n    // 7. Technical and design specifications\n    components.push(`Format: Square app icon optimized for app stores (iOS App Store, Google Play Store)`);\n    components.push(`Design requirements: Clean, scalable vector-style design that remains clear and recognizable at small sizes (16x16px to 1024x1024px)`);\n    components.push(`Visual hierarchy: Strong focal point with balanced composition and appropriate negative space`);\n    // 8. Brand and identity considerations\n    if (websiteData.url) {\n        const domain = websiteData.url.replace(/^https?:\\/\\//, \"\").replace(/^www\\./, \"\").split(\"/\")[0];\n        components.push(`Domain context: ${domain} - ensure the icon reflects this web presence`);\n    }\n    // 9. Category-specific mood and personality\n    components.push(`Emotional tone: ${categoryInfo.mood} with ${categoryInfo.style} characteristics`);\n    // 10. Additional visual context from website analysis\n    const contextualElements = [];\n    if (websiteData.category) {\n        contextualElements.push(`${websiteData.category} industry standards`);\n    }\n    if (websiteData.visualStyle) {\n        contextualElements.push(`${websiteData.visualStyle} design principles`);\n    }\n    if (contextualElements.length > 0) {\n        components.push(`Design should align with: ${contextualElements.join(\" and \")}`);\n    }\n    // 11. Final quality and uniqueness requirements\n    components.push(`The icon must be distinctive, memorable, and instantly recognizable`);\n    components.push(`Avoid generic symbols - create something unique that captures the essence of this specific website`);\n    components.push(`Ensure professional quality suitable for official app store submission and brand representation`);\n    return components.join(\". \") + \".\";\n}\n/**\n * Creates a structured prompt for the AI to generate an even better icon prompt\n */ function createPromptEnhancementRequest(websiteData) {\n    const basicPrompt = generateIconPrompt(websiteData);\n    return `Based on the following comprehensive website analysis, create an enhanced, detailed prompt for generating a perfect app icon:\n\nComplete Website Information:\n- Site Name: ${websiteData.siteName || \"Not specified\"}\n- Page Title: ${websiteData.title || \"Not specified\"}\n- URL: ${websiteData.url}\n- Category: ${websiteData.category || \"Not categorized\"}\n- Visual Style: ${websiteData.visualStyle || \"Not specified\"}\n- Full Description: ${websiteData.description || \"No description available\"}\n- All Keywords: ${websiteData.keywords?.join(\", \") || \"No keywords detected\"}\n- Complete Color Palette: ${websiteData.primaryColors?.join(\", \") || \"No colors detected\"}\n- Favicon URL: ${websiteData.favicon || \"Not available\"}\n- Open Graph Image: ${websiteData.ogImage || \"Not available\"}\n\nCurrent Generated Prompt: ${basicPrompt}\n\nPlease enhance this prompt to create an even more detailed, creative, and specific description for an AI image generator. Focus on:\n1. Leveraging ALL the website information provided above (don't truncate or summarize)\n2. Using the EXACT hex color values provided (do not interpret or change them)\n3. Incorporating insights from the keywords and full description\n4. Professional app icon design principles\n5. Unique characteristics that make it memorable and brand-appropriate\n6. Technical requirements for app store submission\n7. Visual elements that specifically represent this website's unique purpose and identity\n\nIMPORTANT:\n- Use ALL hex color values exactly as specified without any color interpretation\n- Include comprehensive context from the full description and keywords\n- Don't limit or truncate any of the provided website information\n- Create a rich, detailed prompt that takes advantage of modern AI model capabilities\n\nEnhanced Prompt:`;\n}\n/**\n * Validates and cleans generated prompts\n */ function validatePrompt(prompt) {\n    const issues = [];\n    let cleanedPrompt = prompt.trim();\n    // Check minimum length\n    if (cleanedPrompt.length < 20) {\n        issues.push(\"Prompt is too short\");\n    }\n    // Check maximum length (modern AI models can handle much longer prompts)\n    if (cleanedPrompt.length > 50000) {\n        issues.push(\"Prompt is extremely long and may hit token limits\");\n        cleanedPrompt = cleanedPrompt.slice(0, 50000) + \"...\";\n    }\n    // Remove potentially problematic content\n    const problematicTerms = [\n        \"nsfw\",\n        \"explicit\",\n        \"violent\",\n        \"inappropriate\"\n    ];\n    const lowerPrompt = cleanedPrompt.toLowerCase();\n    for (const term of problematicTerms){\n        if (lowerPrompt.includes(term)) {\n            issues.push(`Contains potentially inappropriate content: ${term}`);\n        }\n    }\n    // Ensure it mentions \"icon\" or \"app icon\"\n    if (!lowerPrompt.includes(\"icon\") && !lowerPrompt.includes(\"logo\")) {\n        cleanedPrompt = \"App icon: \" + cleanedPrompt;\n    }\n    return {\n        isValid: issues.length === 0,\n        cleanedPrompt,\n        issues: issues.length > 0 ? issues : undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/promptGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Performs AI-powered analysis of website content using Gemini directly\n */ async function performAIAnalysis(html, metadata) {\n    try {\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            console.warn(\"Google AI API key not configured, skipping AI analysis\");\n            return undefined;\n        }\n        // Extract text content from HTML (simplified)\n        const textContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 8000); // Limit text for API efficiency\n        const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.\n\nWebsite Information:\n- URL: ${metadata.url || \"Not provided\"}\n- Title: ${metadata.title || \"Not provided\"}\n- Description: ${metadata.description || \"Not provided\"}\n- Site Name: ${metadata.siteName || \"Not provided\"}\n- Keywords: ${metadata.keywords?.join(\", \") || \"Not provided\"}\n\nWebsite Content (first 8000 characters):\n${textContent}\n\nPlease analyze this website and respond with a JSON object containing the following fields:\n\n{\n  \"category\": \"one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other\",\n  \"visualStyle\": \"one of: modern, minimalist, corporate, playful, elegant, bold, classic\",\n  \"brandPersonality\": \"brief description of the brand's personality and tone\",\n  \"targetAudience\": \"description of the primary target audience\",\n  \"primaryPurpose\": \"main purpose or function of the website\",\n  \"keyFeatures\": [\"array\", \"of\", \"key\", \"features\", \"or\", \"services\"],\n  \"industryContext\": \"industry or sector this website operates in\",\n  \"designCharacteristics\": [\"array\", \"of\", \"visual\", \"design\", \"characteristics\"]\n}\n\nBase your analysis on:\n1. The actual content and purpose of the website\n2. The language, tone, and messaging used\n3. The apparent target audience and use cases\n4. The industry context and competitive landscape\n5. Visual and functional characteristics that would inform icon design\n\nRespond ONLY with the JSON object, no additional text.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            console.warn(\"Gemini API request failed:\", response.status, response.statusText);\n            return undefined;\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let analysisText = result.candidates[0].content.parts[0].text.trim();\n            // Clean up the response to extract JSON\n            analysisText = analysisText.replace(/```json\\s*/, \"\").replace(/```\\s*$/, \"\").trim();\n            try {\n                const analysis = JSON.parse(analysisText);\n                // Validate the response structure\n                if (!analysis.category || !analysis.visualStyle) {\n                    console.warn(\"Invalid AI analysis structure received\");\n                    return undefined;\n                }\n                return analysis;\n            } catch (parseError) {\n                console.warn(\"Failed to parse AI analysis JSON:\", parseError);\n                return undefined;\n            }\n        } else {\n            console.warn(\"No valid response from Gemini API\");\n            return undefined;\n        }\n    } catch (error) {\n        console.warn(\"AI analysis error:\", error);\n        return undefined;\n    }\n}\n/**\n * Basic fallback analysis when AI analysis fails\n */ function basicFallbackAnalysis(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Basic category determination\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\")) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\")) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\")) {\n        category = \"corporate\";\n    }\n    // Basic style determination\n    let visualStyle = \"modern\";\n    if (allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (allText.includes(\"corporate\") || allText.includes(\"professional\")) {\n        visualStyle = \"corporate\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    }\n                });\n            }\n        }\n        // Check for brand-related elements (headers, logos, buttons)\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    $('style, link[rel=\"stylesheet\"]').each((_, element)=>{\n        const css = $(element).html() || \"\";\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                }\n            });\n        }\n    });\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    return sortedColors;\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Perform AI-powered analysis\n        const aiAnalysis = await performAIAnalysis(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        // Use AI analysis if available, otherwise fall back to basic analysis\n        let category = \"other\";\n        let visualStyle = \"modern\";\n        if (aiAnalysis) {\n            category = aiAnalysis.category;\n            visualStyle = aiAnalysis.visualStyle;\n        } else {\n            // Fallback to basic analysis\n            const fallback = basicFallbackAnalysis(html, metadata);\n            category = fallback.category;\n            visualStyle = fallback.visualStyle;\n        }\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage,\n            aiAnalysis\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-website%2Froute&page=%2Fapi%2Fanalyze-website%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-website%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();