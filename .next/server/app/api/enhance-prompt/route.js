"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/enhance-prompt/route";
exports.ids = ["app/api/enhance-prompt/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fenhance-prompt%2Froute&page=%2Fapi%2Fenhance-prompt%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-prompt%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fenhance-prompt%2Froute&page=%2Fapi%2Fenhance-prompt%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-prompt%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_enhance_prompt_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/enhance-prompt/route.ts */ \"(rsc)/./src/app/api/enhance-prompt/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/enhance-prompt/route\",\n        pathname: \"/api/enhance-prompt\",\n        filename: \"route\",\n        bundlePath: \"app/api/enhance-prompt/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/enhance-prompt/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_enhance_prompt_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/enhance-prompt/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fenhance-prompt%2Froute&page=%2Fapi%2Fenhance-prompt%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-prompt%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/enhance-prompt/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/enhance-prompt/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        let { userIdea } = body;\n        // Use default idea if none provided\n        if (!userIdea || userIdea.trim() === \"\") {\n            userIdea = \"fun and engaging app icon for attention span training\";\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: `Given the following brief idea for an attention span training app icon, elaborate it into a detailed, creative, and visually descriptive prompt suitable for an image generation AI. Focus on making it fun, engaging, and relevant to attention span improvement.\n        Idea: ${userIdea.trim()}\n        Elaborated Prompt:`\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"Prompt enhancement failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const enhancedPrompt = result.candidates[0].content.parts[0].text;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                enhancedPrompt\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not enhance prompt. Please try again.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error enhancing prompt:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: `Failed to enhance prompt: ${errorMessage}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9lbmhhbmNlLXByb21wdC9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3RDtBQUdqRCxlQUFlQyxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsT0FBNkIsTUFBTUQsUUFBUUUsSUFBSTtRQUNyRCxJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHRjtRQUVuQixvQ0FBb0M7UUFDcEMsSUFBSSxDQUFDRSxZQUFZQSxTQUFTQyxJQUFJLE9BQU8sSUFBSTtZQUN2Q0QsV0FBVztRQUNiO1FBRUEsbURBQW1EO1FBQ25ELE1BQU1FLFNBQVNDLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCO1FBQzVDLE1BQU1DLGNBQWNILFFBQVFDLEdBQUcsQ0FBQ0csWUFBWSxJQUFJO1FBRWhELElBQUksQ0FBQ0wsUUFBUTtZQUNYLE9BQU9QLHFEQUFZQSxDQUFDSSxJQUFJLENBQ3RCO2dCQUFFUyxTQUFTO2dCQUFPQyxPQUFPO1lBQTZFLEdBQ3RHO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxjQUFjO1lBQUM7Z0JBQ25CQyxNQUFNO2dCQUNOQyxPQUFPO29CQUFDO3dCQUNOQyxNQUFNLENBQUM7Y0FDRCxFQUFFZCxTQUFTQyxJQUFJLEdBQUc7MEJBQ04sQ0FBQztvQkFDckI7aUJBQUU7WUFDSjtTQUFFO1FBRUYsTUFBTWMsVUFBVTtZQUFFQyxVQUFVTDtRQUFZO1FBQ3hDLE1BQU1NLFNBQVMsQ0FBQyx3REFBd0QsRUFBRVgsWUFBWSxxQkFBcUIsRUFBRUosT0FBTyxDQUFDO1FBRXJILE1BQU1nQixXQUFXLE1BQU1DLE1BQU1GLFFBQVE7WUFDbkNHLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFDOUN2QixNQUFNd0IsS0FBS0MsU0FBUyxDQUFDUjtRQUN2QjtRQUVBLElBQUksQ0FBQ0csU0FBU00sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVAsU0FBU25CLElBQUk7WUFDckMsTUFBTSxJQUFJMkIsTUFBTUQsVUFBVWhCLEtBQUssRUFBRWtCLFdBQVc7UUFDOUM7UUFFQSxNQUFNQyxTQUFTLE1BQU1WLFNBQVNuQixJQUFJO1FBRWxDLElBQUk2QixPQUFPQyxVQUFVLElBQUlELE9BQU9DLFVBQVUsQ0FBQ0MsTUFBTSxHQUFHLEtBQ2hERixPQUFPQyxVQUFVLENBQUMsRUFBRSxDQUFDRSxPQUFPLElBQUlILE9BQU9DLFVBQVUsQ0FBQyxFQUFFLENBQUNFLE9BQU8sQ0FBQ2xCLEtBQUssSUFDbEVlLE9BQU9DLFVBQVUsQ0FBQyxFQUFFLENBQUNFLE9BQU8sQ0FBQ2xCLEtBQUssQ0FBQ2lCLE1BQU0sR0FBRyxHQUFHO1lBQ2pELE1BQU1FLGlCQUFpQkosT0FBT0MsVUFBVSxDQUFDLEVBQUUsQ0FBQ0UsT0FBTyxDQUFDbEIsS0FBSyxDQUFDLEVBQUUsQ0FBQ0MsSUFBSTtZQUNqRSxPQUFPbkIscURBQVlBLENBQUNJLElBQUksQ0FBd0I7Z0JBQzlDUyxTQUFTO2dCQUNUd0I7WUFDRjtRQUNGLE9BQU87WUFDTCxPQUFPckMscURBQVlBLENBQUNJLElBQUksQ0FDdEI7Z0JBQUVTLFNBQVM7Z0JBQU9DLE9BQU87WUFBOEMsR0FDdkU7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtJQUNGLEVBQUUsT0FBT0QsT0FBTztRQUNkd0IsUUFBUXhCLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU15QixlQUFlekIsaUJBQWlCaUIsUUFBUWpCLE1BQU1rQixPQUFPLEdBQUc7UUFDOUQsT0FBT2hDLHFEQUFZQSxDQUFDSSxJQUFJLENBQ3RCO1lBQUVTLFNBQVM7WUFBT0MsT0FBTyxDQUFDLDBCQUEwQixFQUFFeUIsYUFBYSxDQUFDO1FBQUMsR0FDckU7WUFBRXhCLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaWNvbi1nZW5lcmF0b3IvLi9zcmMvYXBwL2FwaS9lbmhhbmNlLXByb21wdC9yb3V0ZS50cz8xYWE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBFbmhhbmNlUHJvbXB0UmVxdWVzdCwgRW5oYW5jZVByb21wdFJlc3BvbnNlIH0gZnJvbSAnQC90eXBlcyc7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keTogRW5oYW5jZVByb21wdFJlcXVlc3QgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBsZXQgeyB1c2VySWRlYSB9ID0gYm9keTtcblxuICAgIC8vIFVzZSBkZWZhdWx0IGlkZWEgaWYgbm9uZSBwcm92aWRlZFxuICAgIGlmICghdXNlcklkZWEgfHwgdXNlcklkZWEudHJpbSgpID09PSAnJykge1xuICAgICAgdXNlcklkZWEgPSBcImZ1biBhbmQgZW5nYWdpbmcgYXBwIGljb24gZm9yIGF0dGVudGlvbiBzcGFuIHRyYWluaW5nXCI7XG4gICAgfVxuXG4gICAgLy8gR2V0IEFQSSBrZXkgYW5kIG1vZGVsIGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuR09PR0xFX0FJX0FQSV9LRVk7XG4gICAgY29uc3QgZ2VtaW5pTW9kZWwgPSBwcm9jZXNzLmVudi5HRU1JTklfTU9ERUwgfHwgJ2dlbWluaS0yLjUtZmxhc2gtcHJldmlldy0wNS0yMCc7XG5cbiAgICBpZiAoIWFwaUtleSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uPEVuaGFuY2VQcm9tcHRSZXNwb25zZT4oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQVBJIGtleSBub3QgY29uZmlndXJlZC4gUGxlYXNlIHNldCBHT09HTEVfQUlfQVBJX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZS4nIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBjaGF0SGlzdG9yeSA9IFt7XG4gICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgIHBhcnRzOiBbeyBcbiAgICAgICAgdGV4dDogYEdpdmVuIHRoZSBmb2xsb3dpbmcgYnJpZWYgaWRlYSBmb3IgYW4gYXR0ZW50aW9uIHNwYW4gdHJhaW5pbmcgYXBwIGljb24sIGVsYWJvcmF0ZSBpdCBpbnRvIGEgZGV0YWlsZWQsIGNyZWF0aXZlLCBhbmQgdmlzdWFsbHkgZGVzY3JpcHRpdmUgcHJvbXB0IHN1aXRhYmxlIGZvciBhbiBpbWFnZSBnZW5lcmF0aW9uIEFJLiBGb2N1cyBvbiBtYWtpbmcgaXQgZnVuLCBlbmdhZ2luZywgYW5kIHJlbGV2YW50IHRvIGF0dGVudGlvbiBzcGFuIGltcHJvdmVtZW50LlxuICAgICAgICBJZGVhOiAke3VzZXJJZGVhLnRyaW0oKX1cbiAgICAgICAgRWxhYm9yYXRlZCBQcm9tcHQ6YCBcbiAgICAgIH1dXG4gICAgfV07XG5cbiAgICBjb25zdCBwYXlsb2FkID0geyBjb250ZW50czogY2hhdEhpc3RvcnkgfTtcbiAgICBjb25zdCBhcGlVcmwgPSBgaHR0cHM6Ly9nZW5lcmF0aXZlbGFuZ3VhZ2UuZ29vZ2xlYXBpcy5jb20vdjFiZXRhL21vZGVscy8ke2dlbWluaU1vZGVsfTpnZW5lcmF0ZUNvbnRlbnQ/a2V5PSR7YXBpS2V5fWA7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGFwaVVybCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHBheWxvYWQpXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yPy5tZXNzYWdlIHx8ICdQcm9tcHQgZW5oYW5jZW1lbnQgZmFpbGVkLicpO1xuICAgIH1cblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgIGlmIChyZXN1bHQuY2FuZGlkYXRlcyAmJiByZXN1bHQuY2FuZGlkYXRlcy5sZW5ndGggPiAwICYmXG4gICAgICAgIHJlc3VsdC5jYW5kaWRhdGVzWzBdLmNvbnRlbnQgJiYgcmVzdWx0LmNhbmRpZGF0ZXNbMF0uY29udGVudC5wYXJ0cyAmJlxuICAgICAgICByZXN1bHQuY2FuZGlkYXRlc1swXS5jb250ZW50LnBhcnRzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IGVuaGFuY2VkUHJvbXB0ID0gcmVzdWx0LmNhbmRpZGF0ZXNbMF0uY29udGVudC5wYXJ0c1swXS50ZXh0O1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uPEVuaGFuY2VQcm9tcHRSZXNwb25zZT4oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBlbmhhbmNlZFByb21wdFxuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbjxFbmhhbmNlUHJvbXB0UmVzcG9uc2U+KFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0NvdWxkIG5vdCBlbmhhbmNlIHByb21wdC4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZW5oYW5jaW5nIHByb21wdDonLCBlcnJvcik7XG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvciBvY2N1cnJlZCc7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uPEVuaGFuY2VQcm9tcHRSZXNwb25zZT4oXG4gICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYEZhaWxlZCB0byBlbmhhbmNlIHByb21wdDogJHtlcnJvck1lc3NhZ2V9YCB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImpzb24iLCJ1c2VySWRlYSIsInRyaW0iLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0FJX0FQSV9LRVkiLCJnZW1pbmlNb2RlbCIsIkdFTUlOSV9NT0RFTCIsInN1Y2Nlc3MiLCJlcnJvciIsInN0YXR1cyIsImNoYXRIaXN0b3J5Iiwicm9sZSIsInBhcnRzIiwidGV4dCIsInBheWxvYWQiLCJjb250ZW50cyIsImFwaVVybCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiSlNPTiIsInN0cmluZ2lmeSIsIm9rIiwiZXJyb3JEYXRhIiwiRXJyb3IiLCJtZXNzYWdlIiwicmVzdWx0IiwiY2FuZGlkYXRlcyIsImxlbmd0aCIsImNvbnRlbnQiLCJlbmhhbmNlZFByb21wdCIsImNvbnNvbGUiLCJlcnJvck1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/enhance-prompt/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fenhance-prompt%2Froute&page=%2Fapi%2Fenhance-prompt%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-prompt%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();