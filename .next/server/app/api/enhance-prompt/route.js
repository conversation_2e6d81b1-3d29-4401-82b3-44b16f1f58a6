"use strict";(()=>{var e={};e.id=826,e.ids=[826],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6675:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>l,staticGenerationAsyncStorage:()=>u});var r={};n.r(r),n.d(r,{POST:()=>p});var a=n(9303),o=n(8716),s=n(670),i=n(7070);async function p(e){try{let{userIdea:t}=await e.json();t&&""!==t.trim()||(t="fun and engaging app icon for attention span training");let n=process.env.GOOGLE_AI_API_KEY,r=process.env.GEMINI_MODEL||"gemini-2.5-flash-preview-05-20";if(!n)return i.NextResponse.json({success:!1,error:"API key not configured. Please set GOOGLE_AI_API_KEY environment variable."},{status:500});let a=[{role:"user",parts:[{text:`Given the following brief idea for an attention span training app icon, elaborate it into a detailed, creative, and visually descriptive prompt suitable for an image generation AI. Focus on making it fun, engaging, and relevant to attention span improvement.
        Idea: ${t.trim()}
        Elaborated Prompt:`}]}],o=`https://generativelanguage.googleapis.com/v1beta/models/${r}:generateContent?key=${n}`,s=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:a})});if(!s.ok){let e=await s.json();throw Error(e.error?.message||"Prompt enhancement failed.")}let p=await s.json();if(!p.candidates||!(p.candidates.length>0)||!p.candidates[0].content||!p.candidates[0].content.parts||!(p.candidates[0].content.parts.length>0))return i.NextResponse.json({success:!1,error:"Could not enhance prompt. Please try again."},{status:500});{let e=p.candidates[0].content.parts[0].text;return i.NextResponse.json({success:!0,enhancedPrompt:e})}}catch(t){console.error("Error enhancing prompt:",t);let e=t instanceof Error?t.message:"Unknown error occurred";return i.NextResponse.json({success:!1,error:`Failed to enhance prompt: ${e}`},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/enhance-prompt/route",pathname:"/api/enhance-prompt",filename:"route",bundlePath:"app/api/enhance-prompt/route"},resolvedPagePath:"/Users/<USER>/src/personal/icon-generator/src/app/api/enhance-prompt/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:u,serverHooks:l}=c,g="/api/enhance-prompt/route";function m(){return(0,s.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:u})}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[948,972],()=>n(6675));module.exports=r})();