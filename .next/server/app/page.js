(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},1982:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u}),r(4196),r(2029),r(5866);var n=r(3191),i=r(8716),a=r(7922),o=r.n(a),s=r(5231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4196)),"/Users/<USER>/src/personal/icon-generator/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"/Users/<USER>/src/personal/icon-generator/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/src/personal/icon-generator/src/app/page.tsx"],d="/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},4895:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6764:()=>{},8959:(e,t,r)=>{Promise.resolve().then(r.bind(r,8280))},2481:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let n=r(1174),i=r(8374),a=r(326),o=i._(r(7577)),s=n._(r(962)),l=n._(r(815)),u=r(3078),c=r(5248),d=r(1206);r(576);let f=r(131),p=n._(r(6820)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,n,i,a,o){let s=null==e?void 0:e.src;e&&e["data-loaded-src"]!==s&&(e["data-loaded-src"]=s,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function h(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let x=(0,o.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:s,width:l,decoding:u,className:c,style:d,fetchPriority:f,placeholder:p,loading:m,unoptimized:x,fill:y,onLoadRef:b,onLoadingCompleteRef:v,setBlurComplete:E,setShowAltText:_,sizesInput:P,onLoad:j,onError:w,...R}=e;return(0,a.jsx)("img",{...R,...h(f),loading:m,width:l,height:s,decoding:u,"data-nimg":y?"fill":"1",className:c,style:d,sizes:i,srcSet:n,src:r,ref:(0,o.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(w&&(e.src=e.src),e.complete&&g(e,p,b,v,E,x,P))},[r,p,b,v,E,w,x,P,t]),onLoad:e=>{g(e.currentTarget,p,b,v,E,x,P)},onError:e=>{_(!0),"empty"!==p&&E(!0),w&&w(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...h(r.fetchPriority)};return t&&s.default.preload?(s.default.preload(r.src,n),null):(0,a.jsx)(l.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let b=(0,o.forwardRef)((e,t)=>{let r=(0,o.useContext)(f.RouterContext),n=(0,o.useContext)(d.ImageConfigContext),i=(0,o.useMemo)(()=>{var e;let t=m||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:a}},[n]),{onLoad:s,onLoadingComplete:l}=e,g=(0,o.useRef)(s);(0,o.useEffect)(()=>{g.current=s},[s]);let h=(0,o.useRef)(l);(0,o.useEffect)(()=>{h.current=l},[l]);let[b,v]=(0,o.useState)(!1),[E,_]=(0,o.useState)(!1),{props:P,meta:j}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:b,showAltText:E});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x,{...P,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:g,onLoadingCompleteRef:h,setBlurComplete:v,setShowAltText:_,sizesInput:e.sizes,ref:t}),j.priority?(0,a.jsx)(y,{isAppRouter:!r,imgAttributes:P}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3484:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.AmpContext},1157:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.HeadManagerContext},1206:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.ImageConfigContext},131:(e,t,r)=>{"use strict";e.exports=r(1616).vendored.contexts.RouterContext},8710:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},3078:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(576);let n=r(380),i=r(5248);function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let l,u,c,{src:d,sizes:f,unoptimized:p=!1,priority:m=!1,loading:g,className:h,quality:x,width:y,height:b,fill:v=!1,style:E,overrideSrc:_,onLoad:P,onLoadingComplete:j,placeholder:w="empty",blurDataURL:R,fetchPriority:S,decoding:A="async",layout:O,objectFit:N,objectPosition:C,lazyBoundary:T,lazyRoot:M,...I}=e,{imgConf:k,showAltText:U,blurComplete:$,defaultLoader:D}=t,z=k||i.imageConfigDefault;if("allSizes"in z)l=z;else{let e=[...z.deviceSizes,...z.imageSizes].sort((e,t)=>e-t),t=z.deviceSizes.sort((e,t)=>e-t),n=null==(r=z.qualities)?void 0:r.sort((e,t)=>e-t);l={...z,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===D)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let L=I.loader||D;delete I.loader,delete I.srcSet;let F="__next_img_default"in L;if(F){if("custom"===l.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=L;L=t=>{let{config:r,...n}=t;return e(n)}}if(O){"fill"===O&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(E={...E,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!f&&(f=t)}let G="",q=o(y),W=o(b);if("object"==typeof(s=d)&&(a(s)||void 0!==s.src)){let e=a(d)?d.default:d;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,c=e.blurHeight,R=R||e.blurDataURL,G=e.src,!v){if(q||W){if(q&&!W){let t=q/e.width;W=Math.round(e.height*t)}else if(!q&&W){let t=W/e.height;q=Math.round(e.width*t)}}else q=e.width,W=e.height}}let X=!m&&("lazy"===g||void 0===g);(!(d="string"==typeof d?d:G)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,X=!1),l.unoptimized&&(p=!0),F&&d.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(p=!0),m&&(S="high");let H=o(x),Q=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:N,objectPosition:C}:{},U?{}:{color:"transparent"},E),K=$||"empty"===w?null:"blur"===w?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:W,blurWidth:u,blurHeight:c,blurDataURL:R||"",objectFit:Q.objectFit})+'")':'url("'+w+'")',Y=K?{backgroundSize:Q.objectFit||"cover",backgroundPosition:Q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},B=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:a,sizes:o,loader:s}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,o),c=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((e,n)=>s({config:t,src:r,quality:a,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:s({config:t,src:r,quality:a,width:l[c]})}}({config:l,src:d,unoptimized:p,width:q,quality:H,sizes:f,loader:L});return{props:{...I,loading:X?"lazy":g,fetchPriority:S,width:q,height:W,decoding:A,className:h,style:{...Q,...Y},sizes:B.sizes,srcSet:B.srcSet,src:_||B.src},meta:{unoptimized:p,priority:m,placeholder:w,fill:v}}}},815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return d}});let n=r(1174),i=r(8374),a=r(326),o=i._(r(7577)),s=n._(r(8003)),l=r(3484),u=r(1157),c=r(8710);function d(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(576);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let a=!0,o=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){o=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?a=!1:t.add(i.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!o)&&r.has(e)?a=!1:(r.add(e),n[t]=r)}}}}return a}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:n})})}let g=function(e){let{children:t}=e,r=(0,o.useContext)(l.AmpStateContext),n=(0,o.useContext)(u.HeadManagerContext);return(0,a.jsx)(s.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},380:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:a,objectFit:o}=e,s=n?40*n:t,l=i?40*i:r,u=s&&l?"viewBox='0 0 "+s+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5248:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},9029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return s}});let n=r(1174),i=r(3078),a=r(2481),o=n._(r(6820));function s(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},6820:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:i,quality:a}=e,o=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+o}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},8003:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(7577),i=()=>{},a=()=>{};function o(e){var t;let{headManager:r,reduceComponentsToState:o}=e;function s(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(o(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),s(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=s),()=>{r&&(r._pendingUpdate=s)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},8280:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var n=r(326),i=r(7577);function a({value:e,onChange:t,placeholder:r="e.g., A cute owl focusing on a book, vibrant colors",disabled:i=!1}){return(0,n.jsxs)("div",{className:"w-full",children:[n.jsx("label",{htmlFor:"promptInput",className:"block text-gray-700 text-lg font-medium mb-2",children:"Describe your app icon:"}),n.jsx("input",{id:"promptInput",type:"text",value:e,onChange:e=>t(e.target.value),placeholder:r,disabled:i,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed"})]})}function o({value:e,onChange:t,onAnalyze:r,placeholder:i="e.g., https://example.com or example.com",disabled:a=!1,isAnalyzing:o=!1}){return(0,n.jsxs)("div",{className:"w-full space-y-4",children:[(0,n.jsxs)("div",{children:[n.jsx("label",{htmlFor:"urlInput",className:"block text-gray-700 text-lg font-medium mb-2",children:"Enter website URL:"}),n.jsx("input",{id:"urlInput",type:"url",value:e,onChange:e=>t(e.target.value),onKeyPress:t=>{"Enter"===t.key&&!a&&!o&&e.trim()&&r()},placeholder:i,disabled:a,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed"})]}),n.jsx("button",{onClick:r,disabled:a||o||!e.trim(),className:"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:o?(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"}),"Analyzing Website..."]}):n.jsx(n.Fragment,{children:"\uD83D\uDD0D Analyze Website"})})]})}function s({websiteData:e,generatedPrompt:t,onPromptChange:r,onGenerateIcon:i,isGenerating:a=!1}){let o,s;return(0,n.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-6 space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-start gap-4",children:[e.favicon&&n.jsx("img",{src:e.favicon,alt:"Website favicon",className:"w-8 h-8 rounded",onError:e=>{e.target.style.display="none"}}),(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:e.siteName||e.title||"Website"}),n.jsx("p",{className:"text-sm text-gray-600 break-all",children:e.url})]})]}),e.description&&(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Description:"}),n.jsx("p",{className:"text-sm text-gray-600 line-clamp-3",children:e.description})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Category:"}),n.jsx("span",{className:"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full",children:(o=e.category||"other").charAt(0).toUpperCase()+o.slice(1)})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Style:"}),n.jsx("span",{className:"inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full",children:(s=e.visualStyle||"modern").charAt(0).toUpperCase()+s.slice(1)})]})]}),e.primaryColors&&e.primaryColors.length>0&&(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Primary Colors:"}),n.jsx("div",{className:"flex gap-2 flex-wrap",children:e.primaryColors.slice(0,5).map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[n.jsx("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:e},title:e}),n.jsx("span",{className:"text-xs text-gray-600",children:e})]},t))})]}),e.keywords&&e.keywords.length>0&&(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Keywords:"}),n.jsx("div",{className:"flex gap-1 flex-wrap",children:e.keywords.slice(0,8).map((e,t)=>n.jsx("span",{className:"inline-block bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,n.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,n.jsxs)("div",{className:"mb-4",children:[n.jsx("label",{htmlFor:"promptEditor",className:"block text-sm font-medium text-gray-700 mb-2",children:"Generated Prompt (editable):"}),n.jsx("textarea",{id:"promptEditor",value:t,onChange:e=>r(e.target.value),disabled:a,rows:6,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm font-mono",placeholder:"Generated prompt will appear here..."}),n.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"You can edit this prompt before generating the icon to fine-tune the results."})]}),n.jsx("button",{onClick:i,disabled:a||!t.trim(),className:"w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:a?(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"}),"Generating Icon..."]}):n.jsx(n.Fragment,{children:"✨ Generate Icon from Website"})})]})]})}var l=r(9029),u=r.n(l);function c({imageUrl:e,isLoading:t,placeholder:r="Your generated icon will appear here."}){return n.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-2xl min-h-[200px] w-full flex justify-center items-center overflow-hidden bg-gray-50",children:t?(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[n.jsx("div",{className:"border-4 border-gray-200 border-t-indigo-600 rounded-full w-10 h-10 animate-spin"}),n.jsx("p",{className:"text-gray-500 text-sm",children:"Generating your icon..."})]}):e?n.jsx("div",{className:"relative w-full h-full min-h-[200px] flex items-center justify-center p-4",children:n.jsx(u(),{src:e,alt:"Generated App Icon",width:300,height:300,className:"max-w-full max-h-full object-contain rounded-xl shadow-lg",priority:!0})}):n.jsx("p",{className:"text-gray-500 text-center px-4",children:r})})}function d({message:e}){return e.visible?n.jsx("div",{className:`rounded-xl border px-4 py-3 text-center font-medium transition-all duration-300 ${(()=>{switch(e.type){case"error":return"bg-red-100 text-red-800 border-red-200";case"success":return"bg-green-100 text-green-800 border-green-200";default:return"bg-yellow-100 text-yellow-800 border-yellow-200"}})()}`,role:"alert",children:e.text}):null}function f(){let[e,t]=(0,i.useState)("text"),[r,l]=(0,i.useState)(""),[u,f]=(0,i.useState)(""),[p,m]=(0,i.useState)(null),[g,h]=(0,i.useState)(""),[x,y]=(0,i.useState)(!1),[b,v]=(0,i.useState)(),[E,_]=(0,i.useState)(!1),[P,j]=(0,i.useState)({text:"",type:"info",visible:!1}),w=(e,t="info")=>{j({text:e,type:t,visible:!0})},R=()=>{j(e=>({...e,visible:!1}))},S=e=>{t(e),R(),v(void 0),"text"===e?(f(""),m(null),h("")):l("")},A=async()=>{if(!u.trim()){w("Please enter a valid URL.","error");return}R(),y(!0),m(null),h("");try{let e=await fetch("/api/analyze-website",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:u.trim()})}),t=await e.json();t.success&&t.data?(m(t.data),h(t.generatedPrompt||""),w("Website analyzed successfully! Review the details and prompt below, then generate your icon.","success")):w(t.error||"Failed to analyze website. Please try again.","error")}catch(e){console.error("Error analyzing website:",e),w("Failed to analyze website. Please check the URL and try again.","error")}finally{y(!1)}},O=async()=>{if(!p||!g.trim()){w("Please analyze a website and ensure the prompt is not empty.","error");return}R(),_(!0),v(void 0);try{let e=await fetch("/api/generate-icon",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:g})}),t=await e.json();t.success&&t.imageUrl?(v(t.imageUrl),w("Icon generated successfully from website analysis!","success")):w(t.error||"Failed to generate icon. Please try again.","error")}catch(e){console.error("Error generating icon from website:",e),w("Failed to generate icon from website. Please try again.","error")}finally{_(!1)}},N=async()=>{R(),_(!0);try{let e=await fetch("/api/enhance-prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userIdea:r})}),t=await e.json();t.success&&t.enhancedPrompt?(l(t.enhancedPrompt),w("Prompt enhanced successfully! Now try generating an icon.","success")):w(t.error||"Could not enhance prompt. Please try again.","error")}catch(e){console.error("Error enhancing prompt:",e),w("Failed to enhance prompt. Please try again.","error")}finally{_(!1)}},C=async()=>{if(!r.trim()){w("Please enter a description for the app icon.","error");return}R(),_(!0),v(void 0);try{let e=await fetch("/api/generate-icon",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:r})}),t=await e.json();t.success&&t.imageUrl?(v(t.imageUrl),w("Icon generated successfully!","success")):w(t.error||"Failed to generate icon. Please try again.","error")}catch(e){console.error("Error generating icon:",e),w("Failed to generate icon. Please try again.","error")}finally{_(!1)}};return n.jsx("div",{className:"w-full max-w-4xl mx-auto",children:(0,n.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"App Icon Generator"}),n.jsx("p",{className:"text-gray-600",children:"Create stunning app icons with AI"})]}),(0,n.jsxs)("div",{className:"flex bg-gray-100 rounded-xl p-1",children:[n.jsx("button",{onClick:()=>S("text"),className:`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${"text"===e?"bg-white text-indigo-600 shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:"\uD83D\uDCDD Text Prompt"}),n.jsx("button",{onClick:()=>S("url"),className:`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${"url"===e?"bg-white text-indigo-600 shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:"\uD83C\uDF10 From Website"})]}),"text"===e?(0,n.jsxs)(n.Fragment,{children:[n.jsx(a,{value:r,onChange:l,disabled:E}),(0,n.jsxs)("div",{className:"flex gap-4 flex-col sm:flex-row",children:[n.jsx("button",{onClick:N,disabled:E,className:"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"✨ Enhance Prompt ✨"}),n.jsx("button",{onClick:C,disabled:E,className:"flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"Generate Icon"})]})]}):(0,n.jsxs)(n.Fragment,{children:[n.jsx(o,{value:u,onChange:f,onAnalyze:A,disabled:E||x,isAnalyzing:x}),p&&n.jsx(s,{websiteData:p,generatedPrompt:g,onPromptChange:h,onGenerateIcon:O,isGenerating:E})]}),n.jsx(c,{imageUrl:b,isLoading:E}),n.jsx(d,{message:P})]})})}},338:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return l},NEXT_DID_POSTPONE_HEADER:function(){return c},NEXT_ROUTER_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_RSC_UNION_QUERY:function(){return u},NEXT_URL:function(){return o},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",a="Next-Router-Prefetch",o="Next-Url",s="text/x-component",l=[[r],[i],[a]],u="_rsc",c="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7255:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var l=a[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},1792:(e,t)=>{"use strict";function r(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var s=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,o="[^"+a(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var m=d("CHAR"),g=d("NAME"),h=d("PATTERN");if(g||h){var x=m||"";-1===i.indexOf(x)&&(c+=x,x=""),c&&(s.push(c),c=""),s.push({name:g||l++,prefix:x,suffix:"",pattern:h||o,modifier:d("MODIFIER")||""});continue}var y=m||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(s.push(c),c=""),d("OPEN")){var x=p(),b=d("NAME")||"",v=d("PATTERN")||"",E=p();f("CLOSE"),s.push({name:b||(v?l++:""),pattern:b&&!v?o:v,prefix:x,suffix:E,modifier:d("MODIFIER")||""});continue}f("END")}return s}function n(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<o.length;d++){var f=i(o[d],a);if(s&&!l[n].test(f))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var f=i(String(o),a);if(s&&!l[n].test(f))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function i(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:a,index:o,params:s}}}function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+a(r.endsWith||"")+"]|$",f="["+a(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",m=0;m<e.length;m++){var g=e[m];if("string"==typeof g)p+=a(c(g));else{var h=a(c(g.prefix)),x=a(c(g.suffix));if(g.pattern){if(t&&t.push(g),h||x){if("+"===g.modifier||"*"===g.modifier){var y="*"===g.modifier?"?":"";p+="(?:"+h+"((?:"+g.pattern+")(?:"+x+h+"(?:"+g.pattern+"))*)"+x+")"+y}else p+="(?:"+h+"("+g.pattern+")"+x+")"+g.modifier}else p+="("+g.pattern+")"+g.modifier}else p+="(?:"+h+x+")"+g.modifier}}if(void 0===l||l)i||(p+=f+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],v="string"==typeof b?f.indexOf(b[b.length-1])>-1:void 0===b;i||(p+="(?:"+f+"(?="+d+"))?"),v||(p+="(?="+f+"|"+d+")")}return new RegExp(p,o(r))}function l(e,t,n){return e instanceof RegExp?function(e,t){if(!t)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)t.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}(e,t):Array.isArray(e)?RegExp("(?:"+e.map(function(e){return l(e,t,n).source}).join("|")+")",o(n)):s(r(e,n),t,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=r,t.compile=function(e,t){return n(r(e,t),t)},t.tokensToFunction=n,t.match=function(e,t){var r=[];return i(l(e,r,t),r,t)},t.regexpToFunction=i,t.tokensToRegexp=s,t.pathToRegexp=l},6621:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataRoute:function(){return f}});let n=r(616),i=function(e){return e&&e.__esModule?e:{default:e}}(r(1293)),a=r(7262),o=r(5679),s=r(8785),l=r(8168),u=r(1040);function c(e){let t="";return(e.includes("(")&&e.includes(")")||e.includes("@"))&&(t=(0,s.djb2Hash)(e).toString(36).slice(0,6)),t}function d(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,o.getNamedRouteRegex)(n,!1),d=(0,a.interpolateDynamicPath)(n,t,s),f=c(e),p=f?`-${f}`:"",{name:m,ext:g}=i.default.parse(r);return(0,u.normalizePathSep)(i.default.join(d,`${m}${p}${g}`))}function f(e){if(!(0,n.isMetadataRoute)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":e.endsWith("/sitemap")?t+=".xml":r=c(e.slice(0,-(i.default.basename(e).length+1))),!t.endsWith("/route")){let{dir:a,name:o,ext:s}=i.default.parse(t),l=(0,n.isStaticMetadataRoute)(e);t=i.default.posix.join(a,`${o}${r?`-${r}`:""}${s}`,l?"":"[[...__metadata_id__]]","route")}return t}},616:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_METADATA_IMAGES:function(){return i},isMetadataRoute:function(){return c},isMetadataRouteFile:function(){return s},isStaticMetadataRoute:function(){return u},isStaticMetadataRouteFile:function(){return l}});let n=r(1040),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},a=["js","jsx","ts","tsx"],o=e=>`(?:${e.join("|")})`;function s(e,t,r){let a=[RegExp(`^[\\\\/]robots${r?`\\.${o(t.concat("txt"))}$`:""}`),RegExp(`^[\\\\/]manifest${r?`\\.${o(t.concat("webmanifest","json"))}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${r?`\\.${o(t.concat("xml"))}$`:""}`),RegExp(`[\\\\/]${i.icon.filename}\\d?${r?`\\.${o(t.concat(i.icon.extensions))}$`:""}`),RegExp(`[\\\\/]${i.apple.filename}\\d?${r?`\\.${o(t.concat(i.apple.extensions))}$`:""}`),RegExp(`[\\\\/]${i.openGraph.filename}\\d?${r?`\\.${o(t.concat(i.openGraph.extensions))}$`:""}`),RegExp(`[\\\\/]${i.twitter.filename}\\d?${r?`\\.${o(t.concat(i.twitter.extensions))}$`:""}`)],s=(0,n.normalizePathSep)(e);return a.some(e=>e.test(s))}function l(e){return s(e,[],!0)}function u(e){return"/robots"===e||"/manifest"===e||l(e)}function c(e){let t=e.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),!t.endsWith("/page")&&s(t,a,!1)}},6975:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(7255);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},1586:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let n=r(8168),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?`/${a}`:t+"/"+a;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);a=o.slice(0,-2).concat(a).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:a}}},7262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return f}});let n=r(7360),i=r(5014),a=r(3707),o=r(5679),s=r(3525),l=r(5257),u=r(7847),c=r(8168),d=r(1943);function f(e,t,r,i,a){if(i&&t&&a){let t=(0,n.parse)(e.url,!0);for(let e of(delete t.search,Object.keys(t.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),i=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||(r||Object.keys(a.groups)).includes(e))&&delete t.query[e]}e.url=(0,n.format)(t)}}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let{optional:i,repeat:a}=r.groups[n],o=`[${a?"...":""}${n}]`;i&&(o=`[${o}]`);let s=e.indexOf(o);if(s>-1){let r;let i=t[n];r=Array.isArray(i)?i.map(e=>e&&encodeURIComponent(e)).join("/"):i?encodeURIComponent(i):"",e=e.slice(0,s)+r+e.slice(s+o.length)}}return e}function m(e,t,r,n){let i=!0;return r?{params:e=Object.keys(r.groups).reduce((a,o)=>{let s=e[o];"string"==typeof s&&(s=(0,c.normalizeRscURL)(s)),Array.isArray(s)&&(s=s.map(e=>("string"==typeof e&&(e=(0,c.normalizeRscURL)(e)),e)));let l=n[o],u=r.groups[o].optional;return((Array.isArray(l)?l.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(l))||void 0===s&&!(u&&t))&&(i=!1),u&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${o}]]`))&&(s=void 0,delete e[o]),s&&"string"==typeof s&&r.groups[o].repeat&&(s=s.split("/")),s&&(a[o]=s),a},{}),hasValidParams:i}:{params:e,hasValidParams:!1}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:g,caseSensitive:h}){let x,y,b;return c&&(x=(0,o.getNamedRouteRegex)(e,!1),b=(y=(0,s.getRouteMatcher)(x))(e)),{handleRewrites:function(o,s){let d={},f=s.pathname,p=n=>{let u=(0,a.getPathMatch)(n.source+(g?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!h})(s.pathname);if((n.has||n.missing)&&u){let e=(0,l.matchHas)(o,s.query,n.has,n.missing);e?Object.assign(u,e):u=!1}if(u){let{parsedDestination:a,destQuery:o}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:u,query:s.query});if(a.protocol)return!0;if(Object.assign(d,o,u),Object.assign(s.query,a.query),delete a.query,Object.assign(s,a),f=s.pathname,r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||u.nextInternalLocale}if(f===e)return!0;if(c&&y){let e=y(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(f||"");return t===(0,u.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return d},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e,r,n){return(0,s.getRouteMatcher)(function(){let{groups:e,routeKeys:i}=x;return{re:{exec:a=>{let o=Object.fromEntries(new URLSearchParams(a)),s=t&&n&&o["1"]===n;for(let e of Object.keys(o)){let t=o[e];e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX)&&(o[e.substring(d.NEXT_QUERY_PARAM_PREFIX.length)]=t,delete o[e])}let l=Object.keys(i||{}),u=e=>{if(t){let i=Array.isArray(e),a=i?e[0]:e;if("string"==typeof a&&t.locales.some(e=>e.toLowerCase()===a.toLowerCase()&&(n=e,r.locale=n,!0)))return i&&e.splice(0,1),!i||0===e.length}return!1};return l.every(e=>o[e])?l.reduce((t,r)=>{let n=null==i?void 0:i[r];return n&&!u(o[r])&&(t[e[n].pos]=o[r]),t},{}):Object.keys(o).reduce((e,t)=>{if(!u(o[t])){let r=t;return s&&(r=parseInt(t,10)-1+""),Object.assign(e,{[r]:o[t]})}return e},{})}},groups:e}}())(e.headers["x-now-route-matches"])},normalizeDynamicRouteParams:(e,t)=>m(e,t,x,b),normalizeVercelUrl:(e,t,r)=>f(e,t,r,c,x),interpolateDynamicPath:(e,t)=>p(e,t,x)}}},4080:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},8785:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},5014:(e,t)=>{"use strict";function r(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},1293:(e,t,r)=>{"use strict";let n;n=r(5315),e.exports=n},1555:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},1040:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},8168:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let n=r(1555),i=r(5406);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},1942:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(2569);let n=r(4869);function i(e,t){let r=new URL("http://n"),i=t?new URL(t,r):e.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:s,hash:l,href:u,origin:c}=new URL(e,i);if(c!==r.origin)throw Error("invariant: invalid relative URL, router received "+e);return{pathname:a,query:(0,n.searchParamsToUrlQuery)(o),search:s,hash:l,href:u.slice(r.origin.length)}}},3226:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(4869),i=r(1942);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},3707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(1792);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},5257:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},prepareDestination:function(){return f}});let n=r(1792),i=r(4080),a=r(3226),o=r(1586),s=r(338),l=r(6975);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n;let a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!!r.every(e=>a(e))&&!n.some(e=>a(e))&&i}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t;let r=Object.assign({},e.query);delete r.__nextLocale,delete r.__nextDefaultLocale,delete r.__nextDataReq,delete r.__nextInferredLocaleFromDefault,delete r[s.NEXT_RSC_UNION_QUERY];let l=e.destination;for(let t of Object.keys({...e.params,...r}))l=l.replace(RegExp(":"+(0,i.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t);let c=(0,a.parseUrl)(l),f=c.query,p=u(""+c.pathname+(c.hash||"")),m=u(c.hostname||""),g=[],h=[];(0,n.pathToRegexp)(p,g),(0,n.pathToRegexp)(m,h);let x=[];g.forEach(e=>x.push(e.name)),h.forEach(e=>x.push(e.name));let y=(0,n.compile)(p,{validate:!1}),b=(0,n.compile)(m,{validate:!1});for(let[t,r]of Object.entries(f))Array.isArray(r)?f[t]=r.map(t=>d(u(t),e.params)):"string"==typeof r&&(f[t]=d(u(r),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>x.includes(e)))for(let t of v)t in f||(f[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){e.params["0"]=r;break}}try{let[r,n]=(t=y(e.params)).split("#",2);c.hostname=b(e.params),c.pathname=r,c.hash=(n?"#":"")+(n||""),delete c.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match");throw e}return c.query={...r,...c.query},{newUrl:t,destQuery:f,parsedDestination:c}}},4869:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,i]=e;Array.isArray(i)?i.forEach(e=>t.append(r,n(e))):t.set(r,n(i))}),t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},7847:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},3525:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(2569);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},o={};return Object.keys(r).forEach(e=>{let t=r[e],n=i[t.pos];void 0!==n&&(o[e]=~n.indexOf("/")?n.split("/").map(e=>a(e)):t.repeat?[a(n)]:a(n))}),o}}},5679:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return p},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return s}});let n=r(1943),i=r(1586),a=r(4080),o=r(7847);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e){let t=(0,o.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=i.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:i,repeat:l}=s(o[1]);return r[e]={pos:n++,repeat:l,optional:i},"/"+(0,a.escapeStringRegexp)(t)+"([^/]+?)"}if(!o)return"/"+(0,a.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:i}=s(o[1]);return r[e]={pos:n++,repeat:t,optional:i},t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function u(e){let{parameterizedRoute:t,groups:r}=l(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:i,keyPrefix:o}=e,{key:l,optional:u,repeat:c}=s(n),d=l.replace(/\W/g,"");o&&(d=""+o+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),o?i[d]=""+o+l:i[d]=l;let p=t?(0,a.escapeStringRegexp)(t):"";return c?u?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let s=(0,o.removeTrailingSlash)(e).slice(1).split("/"),l=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:s.map(e=>{let r=i.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&o){let[r]=e.split(o[0]);return c({getSafeRouteKey:l,interceptionMarker:r,segment:o[1],routeKeys:u,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return o?c({getSafeRouteKey:l,segment:o[1],routeKeys:u,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,a.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function f(e,t){let r=d(e,t);return{...u(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=l(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=d(e,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},5406:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return i},PAGE_SEGMENT_KEY:function(){return n},isGroupSegment:function(){return r}});let n="__PAGE__",i="__DEFAULT__"},2569:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return g},PageNotFoundError:function(){return h},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var n=r(9510),i=r(2504),a=r.n(i),o=r(5606),s=r.n(o);r(5023);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{className:`${a().variable} ${s().variable} antialiased`,children:e})})}},4196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(9510);let i=(0,r(8570).createProxy)(String.raw`/Users/<USER>/src/personal/icon-generator/src/components/IconGenerator.tsx#default`);function a(){return n.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:n.jsx(i,{})})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(6621);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[948,849],()=>r(1982));module.exports=n})();