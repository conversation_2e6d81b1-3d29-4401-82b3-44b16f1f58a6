/*
 React
 react-dom-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("util"),ca=require("crypto"),da=require("async_hooks"),fa=require("next/dist/compiled/react"),ha=require("react-dom"),ka=Symbol.for("react.element"),la=Symbol.for("react.portal"),ma=Symbol.for("react.fragment"),sa=Symbol.for("react.strict_mode"),ta=Symbol.for("react.profiler"),ua=Symbol.for("react.provider"),Da=Symbol.for("react.consumer"),Ea=Symbol.for("react.context"),Fa=Symbol.for("react.forward_ref"),Ga=Symbol.for("react.suspense"),Ha=Symbol.for("react.suspense_list"),Ia=Symbol.for("react.memo"),
Ja=Symbol.for("react.lazy"),Ka=Symbol.for("react.scope"),Ta=Symbol.for("react.debug_trace_mode"),Ua=Symbol.for("react.offscreen"),Va=Symbol.for("react.legacy_hidden"),Wa=Symbol.for("react.cache"),Xa=Symbol.iterator,Ya=Array.isArray;function Za(a){"function"===typeof a.flush&&a.flush()}var k=null,n=0,$a=!0;
function u(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<n&&(jb(a,k.subarray(0,n)),k=new Uint8Array(2048),n=0),jb(a,qb.encode(b));else{var c=k;0<n&&(c=k.subarray(n));c=qb.encodeInto(b,c);var d=c.read;n+=c.written;d<b.length&&(jb(a,k.subarray(0,n)),k=new Uint8Array(2048),n=qb.encodeInto(b.slice(d),k).written);2048===n&&(jb(a,k),k=new Uint8Array(2048),n=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<n&&(jb(a,k.subarray(0,n)),k=new Uint8Array(2048),n=0),jb(a,b)):(c=k.length-n,c<
b.byteLength&&(0===c?jb(a,k):(k.set(b.subarray(0,c),n),n+=c,jb(a,k),b=b.subarray(c)),k=new Uint8Array(2048),n=0),k.set(b,n),n+=b.byteLength,2048===n&&(jb(a,k),k=new Uint8Array(2048),n=0)))}function jb(a,b){a=a.write(b);$a=$a&&a}function z(a,b){u(a,b);return $a}function rb(a){k&&0<n&&a.write(k.subarray(0,n));k=null;n=0;$a=!0}var qb=new aa.TextEncoder;function B(a){return qb.encode(a)}
var C=Object.assign,D=Object.prototype.hasOwnProperty,sb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tb={},ub={};
function vb(a){if(D.call(ub,a))return!0;if(D.call(tb,a))return!1;if(sb.test(a))return ub[a]=!0;tb[a]=!0;return!1}
var wb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),xb=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),yb=/["'&<>]/;
function E(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=yb.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var zb=/([A-Z])/g,Ab=/^ms-/,Bb=fa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Cb={pending:!1,data:null,method:null,action:null},Db=ha.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Wb={prefetchDNS:Eb,preconnect:Fb,preload:Rb,preloadModule:Sb,preinitStyle:Tb,preinitScript:Ub,preinitModuleScript:Vb},F=[],Xb=B('"></template>'),Yb=B("<script>"),Zb=B("\x3c/script>"),$b=B('<script src="'),ac=B('<script type="module" src="'),bc=B('" nonce="'),cc=B('" integrity="'),dc=B('" crossorigin="'),
ec=B('" async="">\x3c/script>'),fc=/(<\/|<)(s)(cript)/gi;function gc(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var hc=B('<script type="importmap">'),ic=B("\x3c/script>");function K(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function jc(a,b,c){switch(b){case "noscript":return K(2,null,a.tagScope|1);case "select":return K(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return K(3,null,a.tagScope);case "picture":return K(2,null,a.tagScope|2);case "math":return K(4,null,a.tagScope);case "foreignObject":return K(2,null,a.tagScope);case "table":return K(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return K(6,null,a.tagScope);case "colgroup":return K(8,null,a.tagScope);case "tr":return K(7,null,a.tagScope)}return 5<=
a.insertionMode?K(2,null,a.tagScope):0===a.insertionMode?"html"===b?K(1,null,a.tagScope):K(2,null,a.tagScope):1===a.insertionMode?K(2,null,a.tagScope):a}var kc=B("\x3c!-- --\x3e");function lc(a,b,c,d){if(""===b)return d;d&&a.push(kc);a.push(E(b));return!0}var mc=new Map,nc=B(' style="'),oc=B(":"),pc=B(";");
function qc(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(D.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=E(d);e=E((""+e).trim())}else f=mc.get(d),void 0===f&&(f=B(E(d.replace(zb,"-$1").toLowerCase().replace(Ab,"-ms-"))),mc.set(d,f)),e="number"===typeof e?0===e||wb.has(d)?""+e:e+"px":
E((""+e).trim());c?(c=!1,a.push(nc,f,oc,e)):a.push(pc,f,oc,e)}}c||a.push(L)}var N=B(" "),rc=B('="'),L=B('"'),sc=B('=""');function tc(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(N,b,sc)}function O(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(N,b,rc,E(c),L)}function Hc(a){var b=a.nextFormID++;return a.idPrefix+b}var Ic=B(E("javascript:throw new Error('React form unexpectedly submitted.')")),Jc=B('<input type="hidden"');
function Kc(a,b){this.push(Jc);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");O(this,"name",b);O(this,"value",a);this.push(Lc)}
function Mc(a,b,c,d,e,f,g,h){var l=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Hc(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,l=b.data):(a.push(N,"formAction",rc,Ic,L),g=f=e=d=h=null,Nc(b,c)));null!=h&&P(a,"name",h);null!=d&&P(a,"formAction",d);null!=e&&P(a,"formEncType",e);null!=f&&P(a,"formMethod",f);null!=g&&P(a,"formTarget",g);return l}
function P(a,b,c){switch(b){case "className":O(a,"class",c);break;case "tabIndex":O(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":O(a,b,c);break;case "style":qc(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(N,b,rc,E(""+c),L);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;
case "autoFocus":case "multiple":case "muted":tc(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(N,"xlink:href",rc,E(""+c),L);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(N,b,rc,E(c),L);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(N,b,sc);break;case "capture":case "download":!0===c?a.push(N,b,sc):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(N,b,rc,E(c),L);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(N,b,rc,E(c),L);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(N,b,rc,E(c),L);break;case "xlinkActuate":O(a,"xlink:actuate",c);break;case "xlinkArcrole":O(a,
"xlink:arcrole",c);break;case "xlinkRole":O(a,"xlink:role",c);break;case "xlinkShow":O(a,"xlink:show",c);break;case "xlinkTitle":O(a,"xlink:title",c);break;case "xlinkType":O(a,"xlink:type",c);break;case "xmlBase":O(a,"xml:base",c);break;case "xmlLang":O(a,"xml:lang",c);break;case "xmlSpace":O(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=xb.get(b)||b,vb(b)){switch(typeof c){case "function":case "symbol":return;case "boolean":var d=b.toLowerCase().slice(0,
5);if("data-"!==d&&"aria-"!==d)return}a.push(N,b,rc,E(c),L)}}}var R=B(">"),Lc=B("/>");function Oc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Pc(a){var b="";fa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var Qc=B(' selected=""'),Rc=B('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'React form unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.ownerDocument||c,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,d,b))}});');
function Nc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,Rc,Zb))}var Sc=B("\x3c!--F!--\x3e"),Tc=B("\x3c!--F--\x3e");function S(a,b){a.push(U("link"));for(var c in b)if(D.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:P(a,c,d)}}a.push(Lc);return null}
function Uc(a,b,c){a.push(U(c));for(var d in b)if(D.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:P(a,d,e)}}a.push(Lc);return null}
function Vc(a,b){a.push(U("title"));var c=null,d=null,e;for(e in b)if(D.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:P(a,e,f)}}a.push(R);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(E(""+b));Oc(a,d,c);a.push(Wc("title"));return null}
function Xc(a,b){a.push(U("script"));var c=null,d=null,e;for(e in b)if(D.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:P(a,e,f)}}a.push(R);Oc(a,d,c);"string"===typeof c&&a.push(E(c));a.push(Wc("script"));return null}
function Yc(a,b,c){a.push(U(c));var d=c=null,e;for(e in b)if(D.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:P(a,e,f)}}a.push(R);Oc(a,d,c);return"string"===typeof c?(a.push(E(c)),null):c}var Zc=B("\n"),$c=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,ad=new Map;function U(a){var b=ad.get(a);if(void 0===b){if(!$c.test(a))throw Error("Invalid tag: "+a);b=B("<"+a);ad.set(a,b)}return b}var bd=B("<!DOCTYPE html>");
function cd(a,b,c,d,e,f,g,h,l){switch(b){case "div":case "span":case "svg":case "path":break;case "a":break;case "g":case "p":case "li":break;case "select":a.push(U("select"));var p=null,q=null,m;for(m in c)if(D.call(c,m)){var v=c[m];if(null!=v)switch(m){case "children":p=v;break;case "dangerouslySetInnerHTML":q=v;break;case "defaultValue":case "value":break;default:P(a,m,v)}}a.push(R);Oc(a,q,p);return p;case "option":var r=g.selectedValue;a.push(U("option"));var x=null,w=null,t=null,T=null,V;for(V in c)if(D.call(c,
V)){var I=c[V];if(null!=I)switch(V){case "children":x=I;break;case "selected":t=I;break;case "dangerouslySetInnerHTML":T=I;break;case "value":w=I;default:P(a,V,I)}}if(null!=r){var Q=null!==w?""+w:Pc(x);if(Ya(r))for(var y=0;y<r.length;y++){if(""+r[y]===Q){a.push(Qc);break}}else""+r===Q&&a.push(Qc)}else t&&a.push(Qc);a.push(R);Oc(a,T,x);return x;case "textarea":a.push(U("textarea"));var A=null,na=null,Z=null,H;for(H in c)if(D.call(c,H)){var G=c[H];if(null!=G)switch(H){case "children":Z=G;break;case "value":A=
G;break;case "defaultValue":na=G;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:P(a,H,G)}}null===A&&null!==na&&(A=na);a.push(R);if(null!=Z){if(null!=A)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ya(Z)){if(1<Z.length)throw Error("<textarea> can only have at most one child.");A=""+Z[0]}A=""+Z}"string"===typeof A&&"\n"===A[0]&&a.push(Zc);null!==A&&a.push(E(""+A));return null;case "input":a.push(U("input"));
var ia=null,va=null,wa=null,La=null,xa=null,Ma=null,oa=null,uc=null,vc=null,ab;for(ab in c)if(D.call(c,ab)){var ba=c[ab];if(null!=ba)switch(ab){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":ia=ba;break;case "formAction":va=ba;break;case "formEncType":wa=ba;break;case "formMethod":La=ba;break;case "formTarget":xa=ba;break;case "defaultChecked":vc=ba;break;case "defaultValue":oa=
ba;break;case "checked":uc=ba;break;case "value":Ma=ba;break;default:P(a,ab,ba)}}var Ld=Mc(a,d,e,va,wa,La,xa,ia);null!==uc?tc(a,"checked",uc):null!==vc&&tc(a,"checked",vc);null!==Ma?P(a,"value",Ma):null!==oa&&P(a,"value",oa);a.push(Lc);null!==Ld&&Ld.forEach(Kc,a);return null;case "button":a.push(U("button"));var bb=null,Md=null,Nd=null,Od=null,Pd=null,Qd=null,Rd=null,cb;for(cb in c)if(D.call(c,cb)){var ja=c[cb];if(null!=ja)switch(cb){case "children":bb=ja;break;case "dangerouslySetInnerHTML":Md=ja;
break;case "name":Nd=ja;break;case "formAction":Od=ja;break;case "formEncType":Pd=ja;break;case "formMethod":Qd=ja;break;case "formTarget":Rd=ja;break;default:P(a,cb,ja)}}var Sd=Mc(a,d,e,Od,Pd,Qd,Rd,Nd);a.push(R);null!==Sd&&Sd.forEach(Kc,a);Oc(a,Md,bb);if("string"===typeof bb){a.push(E(bb));var Td=null}else Td=bb;return Td;case "form":a.push(U("form"));var db=null,Ud=null,pa=null,eb=null,fb=null,gb=null,hb;for(hb in c)if(D.call(c,hb)){var qa=c[hb];if(null!=qa)switch(hb){case "children":db=qa;break;
case "dangerouslySetInnerHTML":Ud=qa;break;case "action":pa=qa;break;case "encType":eb=qa;break;case "method":fb=qa;break;case "target":gb=qa;break;default:P(a,hb,qa)}}var wc=null,xc=null;if("function"===typeof pa)if("function"===typeof pa.$$FORM_ACTION){var Of=Hc(d),Na=pa.$$FORM_ACTION(Of);pa=Na.action||"";eb=Na.encType;fb=Na.method;gb=Na.target;wc=Na.data;xc=Na.name}else a.push(N,"action",rc,Ic,L),gb=fb=eb=pa=null,Nc(d,e);null!=pa&&P(a,"action",pa);null!=eb&&P(a,"encType",eb);null!=fb&&P(a,"method",
fb);null!=gb&&P(a,"target",gb);a.push(R);null!==xc&&(a.push(Jc),O(a,"name",xc),a.push(Lc),null!==wc&&wc.forEach(Kc,a));Oc(a,Ud,db);if("string"===typeof db){a.push(E(db));var Vd=null}else Vd=db;return Vd;case "menuitem":a.push(U("menuitem"));for(var Gb in c)if(D.call(c,Gb)){var Wd=c[Gb];if(null!=Wd)switch(Gb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:P(a,Gb,Wd)}}a.push(R);return null;case "title":if(3===g.insertionMode||
g.tagScope&1||null!=c.itemProp)var yc=Vc(a,c);else l?yc=null:(Vc(e.hoistableChunks,c),yc=void 0);return yc;case "link":var Pf=c.rel,ra=c.href,Hb=c.precedence;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Pf||"string"!==typeof ra||""===ra){S(a,c);var ib=null}else if("stylesheet"===c.rel)if("string"!==typeof Hb||null!=c.disabled||c.onLoad||c.onError)ib=S(a,c);else{var Oa=e.styles.get(Hb),Ib=d.styleResources.hasOwnProperty(ra)?d.styleResources[ra]:void 0;if(null!==Ib){d.styleResources[ra]=
null;Oa||(Oa={precedence:E(Hb),rules:[],hrefs:[],sheets:new Map},e.styles.set(Hb,Oa));var Jb={state:0,props:C({},c,{"data-precedence":c.precedence,precedence:null})};if(Ib){2===Ib.length&&dd(Jb.props,Ib);var zc=e.preloads.stylesheets.get(ra);zc&&0<zc.length?zc.length=0:Jb.state=1}Oa.sheets.set(ra,Jb);f&&f.stylesheets.add(Jb)}else if(Oa){var Xd=Oa.sheets.get(ra);Xd&&f&&f.stylesheets.add(Xd)}h&&a.push(kc);ib=null}else c.onLoad||c.onError?ib=S(a,c):(h&&a.push(kc),ib=l?null:S(e.hoistableChunks,c));return ib;
case "script":var Ac=c.async;if("string"!==typeof c.src||!c.src||!Ac||"function"===typeof Ac||"symbol"===typeof Ac||c.onLoad||c.onError||3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var Yd=Xc(a,c);else{var Kb=c.src;if("module"===c.type){var Lb=d.moduleScriptResources;var Zd=e.preloads.moduleScripts}else Lb=d.scriptResources,Zd=e.preloads.scripts;var Mb=Lb.hasOwnProperty(Kb)?Lb[Kb]:void 0;if(null!==Mb){Lb[Kb]=null;var Bc=c;if(Mb){2===Mb.length&&(Bc=C({},c),dd(Bc,Mb));var $d=Zd.get(Kb);$d&&($d.length=
0)}var ae=[];e.scripts.add(ae);Xc(ae,Bc)}h&&a.push(kc);Yd=null}return Yd;case "style":var Nb=c.precedence,ya=c.href;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Nb||"string"!==typeof ya||""===ya){a.push(U("style"));var Pa=null,be=null,kb;for(kb in c)if(D.call(c,kb)){var Ob=c[kb];if(null!=Ob)switch(kb){case "children":Pa=Ob;break;case "dangerouslySetInnerHTML":be=Ob;break;default:P(a,kb,Ob)}}a.push(R);var lb=Array.isArray(Pa)?2>Pa.length?Pa[0]:null:Pa;"function"!==typeof lb&&
"symbol"!==typeof lb&&null!==lb&&void 0!==lb&&a.push(E(""+lb));Oc(a,be,Pa);a.push(Wc("style"));var ce=null}else{var za=e.styles.get(Nb);if(null!==(d.styleResources.hasOwnProperty(ya)?d.styleResources[ya]:void 0)){d.styleResources[ya]=null;za?za.hrefs.push(E(ya)):(za={precedence:E(Nb),rules:[],hrefs:[E(ya)],sheets:new Map},e.styles.set(Nb,za));var de=za.rules,Qa=null,ee=null,Pb;for(Pb in c)if(D.call(c,Pb)){var Cc=c[Pb];if(null!=Cc)switch(Pb){case "children":Qa=Cc;break;case "dangerouslySetInnerHTML":ee=
Cc}}var mb=Array.isArray(Qa)?2>Qa.length?Qa[0]:null:Qa;"function"!==typeof mb&&"symbol"!==typeof mb&&null!==mb&&void 0!==mb&&de.push(E(""+mb));Oc(de,ee,Qa)}za&&f&&f.styles.add(za);h&&a.push(kc);ce=void 0}return ce;case "meta":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var fe=Uc(a,c,"meta");else h&&a.push(kc),fe=l?null:"string"===typeof c.charSet?Uc(e.charsetChunks,c,"meta"):"viewport"===c.name?Uc(e.viewportChunks,c,"meta"):Uc(e.hoistableChunks,c,"meta");return fe;case "listing":case "pre":a.push(U(b));
var nb=null,ob=null,pb;for(pb in c)if(D.call(c,pb)){var Qb=c[pb];if(null!=Qb)switch(pb){case "children":nb=Qb;break;case "dangerouslySetInnerHTML":ob=Qb;break;default:P(a,pb,Qb)}}a.push(R);if(null!=ob){if(null!=nb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof ob||!("__html"in ob))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");
var Aa=ob.__html;null!==Aa&&void 0!==Aa&&("string"===typeof Aa&&0<Aa.length&&"\n"===Aa[0]?a.push(Zc,Aa):a.push(""+Aa))}"string"===typeof nb&&"\n"===nb[0]&&a.push(Zc);return nb;case "img":var M=c.src,J=c.srcSet;if(!("lazy"===c.loading||!M&&!J||"string"!==typeof M&&null!=M||"string"!==typeof J&&null!=J)&&"low"!==c.fetchPriority&&!1===!!(g.tagScope&2)&&("string"!==typeof M||":"!==M[4]||"d"!==M[0]&&"D"!==M[0]||"a"!==M[1]&&"A"!==M[1]||"t"!==M[2]&&"T"!==M[2]||"a"!==M[3]&&"A"!==M[3])&&("string"!==typeof J||
":"!==J[4]||"d"!==J[0]&&"D"!==J[0]||"a"!==J[1]&&"A"!==J[1]||"t"!==J[2]&&"T"!==J[2]||"a"!==J[3]&&"A"!==J[3])){var ge="string"===typeof c.sizes?c.sizes:void 0,Ra=J?J+"\n"+(ge||""):M,Dc=e.preloads.images,Ba=Dc.get(Ra);if(Ba){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Dc.delete(Ra),e.highImagePreloads.add(Ba)}else if(!d.imageResources.hasOwnProperty(Ra)){d.imageResources[Ra]=F;var Ec=c.crossOrigin;var he="string"===typeof Ec?"use-credentials"===Ec?Ec:"":void 0;var ea=e.headers,Fc;ea&&0<
ea.remainingCapacity&&("high"===c.fetchPriority||500>ea.highImagePreloads.length)&&(Fc=ed(M,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:he,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(ea.remainingCapacity-=Fc.length))?(e.resets.image[Ra]=F,ea.highImagePreloads&&(ea.highImagePreloads+=", "),ea.highImagePreloads+=Fc):(Ba=[],S(Ba,{rel:"preload",as:"image",href:J?void 0:M,imageSrcSet:J,imageSizes:ge,crossOrigin:he,
integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ba):(e.bulkPreloads.add(Ba),Dc.set(Ra,Ba)))}}return Uc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Uc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>g.insertionMode&&null===e.headChunks){e.headChunks=[];var ie=Yc(e.headChunks,c,"head")}else ie=Yc(a,c,"head");return ie;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[bd];var je=Yc(e.htmlChunks,c,"html")}else je=Yc(a,c,"html");return je;default:if(-1!==b.indexOf("-")){a.push(U(b));var Gc=null,ke=null,Sa;for(Sa in c)if(D.call(c,Sa)){var Ca=c[Sa];if(null!=Ca){var Qf=Sa;switch(Sa){case "children":Gc=Ca;break;case "dangerouslySetInnerHTML":ke=Ca;break;case "style":qc(a,
Ca);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;default:vb(Sa)&&"function"!==typeof Ca&&"symbol"!==typeof Ca&&a.push(N,Qf,rc,E(Ca),L)}}}a.push(R);Oc(a,ke,Gc);return Gc}}return Yc(a,c,b)}var fd=new Map;function Wc(a){var b=fd.get(a);void 0===b&&(b=B("</"+a+">"),fd.set(a,b));return b}function gd(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)u(a,b[c]);return c<b.length?(c=b[c],b.length=0,z(a,c)):!0}
var hd=B('<template id="'),id=B('"></template>'),jd=B("\x3c!--$--\x3e"),kd=B('\x3c!--$?--\x3e<template id="'),ld=B('"></template>'),md=B("\x3c!--$!--\x3e"),nd=B("\x3c!--/$--\x3e"),od=B("<template"),pd=B('"'),qd=B(' data-dgst="');B(' data-msg="');B(' data-stck="');var rd=B("></template>");function sd(a,b,c){u(a,kd);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");u(a,b.boundaryPrefix);u(a,c.toString(16));return z(a,ld)}
var td=B('<div hidden id="'),ud=B('">'),vd=B("</div>"),wd=B('<svg aria-hidden="true" style="display:none" id="'),xd=B('">'),yd=B("</svg>"),zd=B('<math aria-hidden="true" style="display:none" id="'),Ad=B('">'),Bd=B("</math>"),Cd=B('<table hidden id="'),Dd=B('">'),Ed=B("</table>"),Fd=B('<table hidden><tbody id="'),Gd=B('">'),Hd=B("</tbody></table>"),Id=B('<table hidden><tr id="'),Jd=B('">'),Kd=B("</tr></table>"),le=B('<table hidden><colgroup id="'),me=B('">'),ne=B("</colgroup></table>");
function oe(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return u(a,td),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,ud);case 3:return u(a,wd),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,xd);case 4:return u(a,zd),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,Ad);case 5:return u(a,Cd),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,Dd);case 6:return u(a,Fd),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,Gd);case 7:return u(a,Id),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,Jd);case 8:return u(a,
le),u(a,b.segmentPrefix),u(a,d.toString(16)),z(a,me);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function pe(a,b){switch(b.insertionMode){case 0:case 1:case 2:return z(a,vd);case 3:return z(a,yd);case 4:return z(a,Bd);case 5:return z(a,Ed);case 6:return z(a,Hd);case 7:return z(a,Kd);case 8:return z(a,ne);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var qe=B('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),re=B('$RS("'),se=B('","'),te=B('")\x3c/script>'),ue=B('<template data-rsi="" data-sid="'),ve=B('" data-pid="'),we=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
xe=B('$RC("'),ye=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
ze=B('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Ae=B('$RR("'),Be=B('","'),Ce=B('",'),De=B('"'),Ee=B(")\x3c/script>"),Fe=B('<template data-rci="" data-bid="'),Ge=B('<template data-rri="" data-bid="'),He=B('" data-sid="'),Ie=B('" data-sty="'),Je=B('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),Ke=B('$RX("'),Le=B('"'),Me=B(","),Ne=B(")\x3c/script>"),Oe=B('<template data-rxi="" data-bid="'),Pe=B('" data-dgst="'),
Qe=B('" data-msg="'),Re=B('" data-stck="'),Se=/[<\u2028\u2029]/g;function Te(a){return JSON.stringify(a).replace(Se,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Ue=/[&><\u2028\u2029]/g;
function Ve(a){return JSON.stringify(a).replace(Ue,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var We=B('<style media="not all" data-precedence="'),Xe=B('" data-href="'),Ye=B('">'),Ze=B("</style>"),$e=!1,af=!0;function bf(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){u(this,We);u(this,a.precedence);for(u(this,Xe);d<c.length-1;d++)u(this,c[d]),u(this,cf);u(this,c[d]);u(this,Ye);for(d=0;d<b.length;d++)u(this,b[d]);af=z(this,Ze);$e=!0;b.length=0;c.length=0}}function df(a){return 2!==a.state?$e=!0:!1}
function ef(a,b,c){$e=!1;af=!0;b.styles.forEach(bf,a);b.stylesheets.forEach(df);$e&&(c.stylesToHoist=!0);return af}function ff(a){for(var b=0;b<a.length;b++)u(this,a[b]);a.length=0}var gf=[];function hf(a){S(gf,a.props);for(var b=0;b<gf.length;b++)u(this,gf[b]);gf.length=0;a.state=2}var jf=B('<style data-precedence="'),kf=B('" data-href="'),cf=B(" "),lf=B('">'),mf=B("</style>");
function nf(a){var b=0<a.sheets.size;a.sheets.forEach(hf,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){u(this,jf);u(this,a.precedence);a=0;if(d.length){for(u(this,kf);a<d.length-1;a++)u(this,d[a]),u(this,cf);u(this,d[a])}u(this,lf);for(a=0;a<c.length;a++)u(this,c[a]);u(this,mf);c.length=0;d.length=0}}
function of(a){if(0===a.state){a.state=1;var b=a.props;S(gf,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<gf.length;a++)u(this,gf[a]);gf.length=0}}function pf(a){a.sheets.forEach(of,this);a.sheets.clear()}var qf=B("["),rf=B(",["),sf=B(","),tf=B("]");
function uf(a,b){u(a,qf);var c=qf;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)u(a,c),u(a,Ve(""+d.props.href)),u(a,tf),c=rf;else{u(a,c);var e=d.props["data-precedence"],f=d.props;u(a,Ve(""+d.props.href));e=""+e;u(a,sf);u(a,Ve(e));for(var g in f)if(D.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!vb(g))break a;h=""+h}u(e,sf);u(e,Ve(l));u(e,sf);u(e,Ve(h))}}}u(a,
tf);c=rf;d.state=3}});u(a,tf)}
function vf(a,b){u(a,qf);var c=qf;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)u(a,c),u(a,E(JSON.stringify(""+d.props.href))),u(a,tf),c=rf;else{u(a,c);var e=d.props["data-precedence"],f=d.props;u(a,E(JSON.stringify(""+d.props.href)));e=""+e;u(a,sf);u(a,E(JSON.stringify(e)));for(var g in f)if(D.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!vb(g))break a;h=""+h}u(e,sf);u(e,E(JSON.stringify(l)));u(e,sf);u(e,
E(JSON.stringify(h)))}}}u(a,tf);c=rf;d.state=3}});u(a,tf)}function wf(){return{styles:new Set,stylesheets:new Set}}
function Eb(a){var b=xf();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(yf,zf)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],S(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Af(b)}}}
function Fb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(yf,zf)+">; rel=preconnect";if("string"===typeof b){var l=(""+b).replace(Bf,Cf);h+='; crossorigin="'+l+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],S(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Af(c)}}}
function Rb(a,b,c){var d=xf();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}var p=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(p))return;e.imageResources[p]=F;e=f.headers;var q;e&&0<e.remainingCapacity&&"high"===l&&(q=ed(a,b,c),2<=(e.remainingCapacity-=q.length))?(f.resets.image[p]=F,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=q):(e=[],S(e,C({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(p,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];S(g,C({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?F:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
S(g,C({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?F:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=F;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(p=ed(a,b,c),2<=(e.remainingCapacity-=p.length)))f.resets.font[a]=F,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=p;else switch(e=[],
a=C({rel:"preload",href:a,as:b},c),S(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Af(d)}}}
function Sb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?F:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=F}S(f,C({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Af(c)}}}
function Tb(a,b,c){var d=xf();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:E(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:C({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&dd(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Af(d))}}}
function Ub(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=C({src:a,async:!0},b),f&&(2===f.length&&dd(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Xc(a,b),Af(c))}}}
function Vb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=C({src:a,type:"module",async:!0},b),f&&(2===f.length&&dd(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Xc(a,b),Af(c))}}}function dd(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function ed(a,b,c){a=(""+a).replace(yf,zf);b=(""+b).replace(Bf,Cf);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)D.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Bf,Cf)+'"'));return b}var yf=/[<>\r\n]/g;
function zf(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Bf=/["';,\r\n]/g;
function Cf(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Df(a){this.styles.add(a)}function Ef(a){this.stylesheets.add(a)}var Ff=new da.AsyncLocalStorage,Gf=Symbol.for("react.client.reference");
function Hf(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===Gf?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ma:return"Fragment";case la:return"Portal";case ta:return"Profiler";case sa:return"StrictMode";case Ga:return"Suspense";case Ha:return"SuspenseList";case Wa:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case ua:return(a._context.displayName||"Context")+".Provider";case Ea:return(a.displayName||"Context")+".Consumer";case Fa:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Ia:return b=a.displayName||null,null!==b?b:Hf(a.type)||"Memo";case Ja:b=a._payload;a=a._init;try{return Hf(a(b))}catch(c){}}return null}var If={};function Jf(a,b){a=a.contextTypes;if(!a)return If;var c={},d;for(d in a)c[d]=b[d];return c}var Kf=null;
function Lf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Lf(a,c)}b.context._currentValue=b.value}}function Mf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Mf(a)}
function Nf(a){var b=a.parent;null!==b&&Nf(b);a.context._currentValue=a.value}function Rf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Lf(a,b):Rf(a,b)}
function Sf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Lf(a,c):Sf(a,c);b.context._currentValue=b.value}function Tf(a){var b=Kf;b!==a&&(null===b?Nf(a):null===a?Mf(b):b.depth===a.depth?Lf(b,a):b.depth>a.depth?Rf(b,a):Sf(b,a),Kf=a)}
var Uf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Vf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Uf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:C({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Uf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=C({},f,h)):C(f,h))}a.state=f}else f.queue=null}
var Wf={id:1,overflow:""};function Xf(a,b,c){var d=a.id;a=a.overflow;var e=32-Yf(d)-1;d&=~(1<<e);c+=1;var f=32-Yf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Yf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Yf=Math.clz32?Math.clz32:Zf,$f=Math.log,ag=Math.LN2;function Zf(a){a>>>=0;return 0===a?32:31-($f(a)/ag|0)|0}var bg=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function cg(){}function dg(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(cg,cg),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}eg=b;throw bg;}}var eg=null;
function fg(){if(null===eg)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=eg;eg=null;return a}function gg(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var hg="function"===typeof Object.is?Object.is:gg,ig=null,jg=null,kg=null,lg=null,mg=null,W=null,ng=!1,og=!1,pg=0,qg=0,rg=-1,sg=0,tg=null,ug=null,vg=0;
function wg(){if(null===ig)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return ig}
function xg(){if(0<vg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function yg(){null===W?null===mg?(ng=!1,mg=W=xg()):(ng=!0,W=mg):null===W.next?(ng=!1,W=W.next=xg()):(ng=!0,W=W.next);return W}function zg(){var a=tg;tg=null;return a}function Ag(){lg=kg=jg=ig=null;og=!1;mg=null;vg=0;W=ug=null}function Bg(a,b){return"function"===typeof b?b(a):b}
function Cg(a,b,c){ig=wg();W=yg();if(ng){var d=W.queue;b=d.dispatch;if(null!==ug&&(c=ug.get(d),void 0!==c)){ug.delete(d);d=W.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);W.memoizedState=d;return[d,b]}return[W.memoizedState,b]}a=a===Bg?"function"===typeof b?b():b:void 0!==c?c(b):b;W.memoizedState=a;a=W.queue={last:null,dispatch:null};a=a.dispatch=Dg.bind(null,ig,a);return[W.memoizedState,a]}
function Eg(a,b){ig=wg();W=yg();b=void 0===b?null:b;if(null!==W){var c=W.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!hg(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();W.memoizedState=[a,b];return a}
function Dg(a,b,c){if(25<=vg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===ig)if(og=!0,a={action:c,next:null},null===ug&&(ug=new Map),c=ug.get(b),void 0===c)ug.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Fg(){throw Error("startTransition cannot be called during server rendering.");}function Gg(){throw Error("Cannot update optimistic state while rendering.");}
function Hg(a,b,c){if(void 0!==a)return"p"+a;a=JSON.stringify([b,null,c]);b=ca.createHash("md5");b.update(a);return"k"+b.digest("hex")}function Ig(a){var b=sg;sg+=1;null===tg&&(tg=[]);return dg(tg,a,b)}function Jg(){throw Error("Cache cannot be refreshed during server rendering.");}function Kg(){}
var Mg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Ig(a);if(a.$$typeof===Ea)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){wg();return a._currentValue},useMemo:Eg,useReducer:Cg,useRef:function(a){ig=wg();W=yg();var b=W.memoizedState;return null===b?(a={current:a},W.memoizedState=a):b},useState:function(a){return Cg(Bg,a)},useInsertionEffect:Kg,
useLayoutEffect:Kg,useCallback:function(a,b){return Eg(function(){return a},b)},useImperativeHandle:Kg,useEffect:Kg,useDebugValue:Kg,useDeferredValue:function(a){wg();return a},useTransition:function(){wg();return[!1,Fg]},useId:function(){var a=jg.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Yf(a)-1)).toString(32)+b;var c=Lg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=pg++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));
return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Jg},useHostTransitionStatus:function(){wg();return Cb},useOptimistic:function(a){wg();return[a,Gg]},useFormState:function(a,b,c){wg();var d=qg++,e=kg;if("function"===typeof a.$$FORM_ACTION){var f=null,g=lg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===
typeof h){var l=e[1];h.call(a,e[2],e[3])&&(f=Hg(c,g,d),l===f&&(rg=d,b=e[0]))}var p=a.bind(null,b);a=function(m){p(m)};"function"===typeof p.$$FORM_ACTION&&(a.$$FORM_ACTION=function(m){m=p.$$FORM_ACTION(m);void 0!==c&&(c+="",m.action=c);var v=m.data;v&&(null===f&&(f=Hg(c,g,d)),v.append("$ACTION_KEY",f));return m});return[b,a]}var q=a.bind(null,b);return[b,function(m){q(m)}]}},Lg=null,Ng={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");
}},Og;function Pg(a){if(void 0===Og)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Og=b&&b[1]||""}return"\n"+Og+a}var Qg=!1;
function Rg(a,b){if(!a||Qg)return"";Qg=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var m=function(){throw Error();};Object.defineProperty(m.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(m,[])}catch(r){var v=r}Reflect.construct(a,[],m)}else{try{m.call()}catch(r){v=r}a.call(m.prototype)}}else{try{throw Error();}catch(r){v=r}(m=a())&&"function"===typeof m.catch&&
m.catch(function(){})}}catch(r){if(r&&v&&"string"===typeof r.stack)return[r.stack,v.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var l=g.split("\n"),p=h.split("\n");for(e=d=0;d<l.length&&!l[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<p.length&&!p[e].includes("DetermineComponentFrameRoot");)e++;if(d===l.length||e===p.length)for(d=l.length-1,e=p.length-1;1<=d&&0<=e&&l[d]!==p[e];)e--;for(;1<=d&&0<=e;d--,e--)if(l[d]!==p[e]){if(1!==d||1!==e){do if(d--,e--,0>e||l[d]!==p[e]){var q="\n"+l[d].replace(" at new "," at ");a.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",a.displayName));return q}while(1<=d&&0<=e)}break}}}finally{Qg=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?Pg(c):""}
var Sg=Bb.ReactCurrentDispatcher,Tg=Bb.ReactCurrentCache;function Ug(a){console.error(a);return null}function Vg(){}var Wg=null;function xf(){if(Wg)return Wg;var a=Ff.getStore();return a?a:null}function Xg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Yg(a)}))}
function Zg(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,contentState:wf(),fallbackState:wf(),trackedContentKeyPath:null,trackedFallbackNode:null}}
function $g(a,b,c,d,e,f,g,h,l,p,q,m,v,r,x){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var w={replay:null,node:c,childIndex:d,ping:function(){return Xg(a,w)},blockedBoundary:e,blockedSegment:f,hoistableState:g,abortSet:h,keyPath:l,formatContext:p,legacyContext:q,context:m,treeContext:v,componentStack:r,thenableState:b,isFallback:x};h.add(w);return w}
function ah(a,b,c,d,e,f,g,h,l,p,q,m,v,r,x){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var w={replay:c,node:d,childIndex:e,ping:function(){return Xg(a,w)},blockedBoundary:f,blockedSegment:null,hoistableState:g,abortSet:h,keyPath:l,formatContext:p,legacyContext:q,context:m,treeContext:v,componentStack:r,thenableState:b,isFallback:x};h.add(w);return w}
function bh(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function ch(a,b){return{tag:0,parent:a.componentStack,type:b}}
function dh(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=Pg(b.type,null);break;case 1:a+=Rg(b.type,!1);break;case 2:a+=Rg(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function X(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function eh(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function fh(a,b,c,d,e,f){var g=b.thenableState;b.thenableState=null;ig={};jg=b;kg=a;lg=c;qg=pg=0;rg=-1;sg=0;tg=g;for(a=d(e,f);og;)og=!1,qg=pg=0,rg=-1,sg=0,vg+=1,W=null,a=d(e,f);Ag();return a}
function gh(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((Hf(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=C({},c,d)}b.legacyContext=e;Y(a,b,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Y(a,b,f,-1),b.keyPath=e}
function hh(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var p=0;p<f;p++)p===g?l.push(Sc):l.push(Tc)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Xf(c,1,0),ih(a,b,d,-1),b.treeContext=c):h?ih(a,b,d,-1):Y(a,b,d,-1);b.keyPath=f}function jh(a,b){if(a&&a.defaultProps){b=C({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function kh(a,b,c,d,e,f){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){f=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:d};var g=Jf(d,b.legacyContext);var h=d.contextType;h=new d(e,"object"===typeof h&&null!==h?h._currentValue:g);Vf(h,d,e,g);gh(a,b,c,h,d);b.componentStack=f}else{f=Jf(d,b.legacyContext);g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d};h=fh(a,b,c,d,e,f);var l=0!==pg,p=qg,q=rg;"object"===typeof h&&null!==h&&"function"===
typeof h.render&&void 0===h.$$typeof?(Vf(h,d,e,f),gh(a,b,c,h,d)):hh(a,b,c,h,l,p,q);b.componentStack=g}else if("string"===typeof d){f=b.componentStack;b.componentStack=ch(b,d);g=b.blockedSegment;if(null===g)g=e.children,h=b.formatContext,l=b.keyPath,b.formatContext=jc(h,d,e),b.keyPath=c,ih(a,b,g,-1),b.formatContext=h,b.keyPath=l;else{l=cd(g.chunks,d,e,a.resumableState,a.renderState,b.hoistableState,b.formatContext,g.lastPushedText,b.isFallback);g.lastPushedText=!1;h=b.formatContext;p=b.keyPath;b.formatContext=
jc(h,d,e);b.keyPath=c;ih(a,b,l,-1);b.formatContext=h;b.keyPath=p;a:{c=g.chunks;a=a.resumableState;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(Wc(d))}g.lastPushedText=!1}b.componentStack=
f}else{switch(d){case Va:case Ta:case sa:case ta:case ma:d=b.keyPath;b.keyPath=c;Y(a,b,e.children,-1);b.keyPath=d;return;case Ua:"hidden"!==e.mode&&(d=b.keyPath,b.keyPath=c,Y(a,b,e.children,-1),b.keyPath=d);return;case Ha:d=b.componentStack;b.componentStack=ch(b,"SuspenseList");f=b.keyPath;b.keyPath=c;Y(a,b,e.children,-1);b.keyPath=f;b.componentStack=d;return;case Ka:throw Error("ReactDOMServer does not yet support scope components.");case Ga:a:if(null!==b.replay){d=b.keyPath;b.keyPath=c;c=e.children;
try{ih(a,b,c,-1)}finally{b.keyPath=d}}else{var m=b.componentStack;d=b.componentStack=ch(b,"Suspense");var v=b.keyPath;f=b.blockedBoundary;var r=b.hoistableState,x=b.blockedSegment;g=e.fallback;var w=e.children;e=new Set;h=Zg(a,e);null!==a.trackedPostpones&&(h.trackedContentKeyPath=c);l=bh(a,x.chunks.length,h,b.formatContext,!1,!1);x.children.push(l);x.lastPushedText=!1;var t=bh(a,0,null,b.formatContext,!1,!1);t.parentFlushed=!0;b.blockedBoundary=h;b.hoistableState=h.contentState;b.blockedSegment=
t;b.keyPath=c;try{if(ih(a,b,w,-1),t.lastPushedText&&t.textEmbedded&&t.chunks.push(kc),t.status=1,lh(h,t),0===h.pendingTasks&&0===h.status){h.status=1;b.componentStack=m;break a}}catch(T){t.status=4,h.status=4,p=dh(a,b.componentStack),q=X(a,T,p),h.errorDigest=q,mh(a,h)}finally{b.blockedBoundary=f,b.hoistableState=r,b.blockedSegment=x,b.keyPath=v,b.componentStack=m}p=[c[0],"Suspense Fallback",c[2]];q=a.trackedPostpones;null!==q&&(m=[p[1],p[2],[],null],q.workingMap.set(p,m),5===h.status?q.workingMap.get(c)[4]=
m:h.trackedFallbackNode=m);b=$g(a,null,g,-1,f,l,h.fallbackState,e,p,b.formatContext,b.legacyContext,b.context,b.treeContext,d,!0);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Fa:g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d.render};e=fh(a,b,c,d.render,e,f);hh(a,b,c,e,0!==pg,qg,rg);b.componentStack=g;return;case Ia:d=d.type;e=jh(d,e);kh(a,b,c,d,e,f);return;case ua:g=e.children;f=b.keyPath;d=d._context;e=e.value;h=d._currentValue;
d._currentValue=e;l=Kf;Kf=e={parent:l,depth:null===l?0:l.depth+1,context:d,parentValue:h,value:e};b.context=e;b.keyPath=c;Y(a,b,g,-1);a=Kf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");a.context._currentValue=a.parentValue;a=Kf=a.parent;b.context=a;b.keyPath=f;return;case Ea:e=e.children;e=e(d._currentValue);d=b.keyPath;b.keyPath=c;Y(a,b,e,-1);b.keyPath=d;return;case Da:case Ja:f=b.componentStack;b.componentStack=ch(b,"Lazy");g=d._init;d=g(d._payload);
e=jh(d,e);kh(a,b,c,d,e,void 0);b.componentStack=f;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==d?d:typeof d)+"."));}}
function nh(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=bh(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,ih(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(lh(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Y(a,b,c,d){if(null!==b.replay&&"number"===typeof b.replay.slots)nh(a,b,b.replay.slots,c,d);else if(b.node=c,b.childIndex=d,null!==c){if("object"===typeof c){switch(c.$$typeof){case ka:var e=c.type,f=c.key,g=c.props;var h=c.ref;var l=Hf(e),p=null==f?-1===d?0:d:f;f=[b.keyPath,l,p];if(null!==b.replay)a:{var q=b.replay;d=q.nodes;for(c=0;c<d.length;c++){var m=d[c];if(p===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");var v=m[2];l=m[3];p=b.node;b.replay={nodes:v,slots:l,pendingTasks:1};try{kh(a,b,f,e,g,h);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(A){if("object"===typeof A&&null!==A&&(A===bg||"function"===typeof A.then))throw b.node===p&&(b.replay=q),A;
b.replay.pendingTasks--;g=dh(a,b.componentStack);f=a;a=b.blockedBoundary;e=A;g=X(f,e,g);oh(f,a,v,l,e,g)}b.replay=q}else{if(e!==Ga)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(Hf(e)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{q=void 0;e=m[5];h=m[2];l=m[3];p=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];var r=b.componentStack,x=b.componentStack=ch(b,"Suspense"),w=b.keyPath,t=b.replay,T=b.blockedBoundary,
V=b.hoistableState,I=g.children;g=g.fallback;var Q=new Set,y=Zg(a,Q);y.parentFlushed=!0;y.rootSegmentID=e;b.blockedBoundary=y;b.hoistableState=y.contentState;b.replay={nodes:h,slots:l,pendingTasks:1};try{ih(a,b,I,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===y.pendingTasks&&0===y.status){y.status=1;a.completedBoundaries.push(y);
break b}}catch(A){y.status=4,v=dh(a,b.componentStack),q=X(a,A,v),y.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(y)}finally{b.blockedBoundary=T,b.hoistableState=V,b.replay=t,b.keyPath=w,b.componentStack=r}b=ah(a,null,{nodes:p,slots:m,pendingTasks:0},g,-1,T,y.fallbackState,Q,[f[0],"Suspense Fallback",f[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,x,!0);a.pingedTasks.push(b)}}d.splice(c,1);break a}}}else kh(a,b,f,e,g,h);return;case la:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case Ja:g=b.componentStack;b.componentStack=ch(b,"Lazy");f=c._init;c=f(c._payload);b.componentStack=g;Y(a,b,c,d);return}if(Ya(c)){ph(a,b,c,d);return}null===c||"object"!==typeof c?g=null:(g=Xa&&c[Xa]||c["@@iterator"],g="function"===typeof g?g:null);if(g&&(g=g.call(c))){c=g.next();if(!c.done){f=[];do f.push(c.value),c=g.next();while(!c.done);ph(a,b,f,d)}return}if("function"===typeof c.then)return b.thenableState=null,Y(a,b,Ig(c),d);if(c.$$typeof===Ea)return Y(a,b,c._currentValue,d);d=Object.prototype.toString.call(c);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===d?"object with keys {"+Object.keys(c).join(", ")+"}":d)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof c?(d=b.blockedSegment,null!==d&&(d.lastPushedText=lc(d.chunks,c,a.renderState,d.lastPushedText))):"number"===typeof c&&(d=b.blockedSegment,null!==d&&(d.lastPushedText=lc(d.chunks,""+c,a.renderState,d.lastPushedText)))}}
function ph(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{ph(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(m){if("object"===typeof m&&
null!==m&&(m===bg||"function"===typeof m.then))throw m;b.replay.pendingTasks--;c=dh(a,b.componentStack);var p=b.blockedBoundary,q=m;c=X(a,q,c);oh(a,p,d,l,q,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)l=c[d],b.treeContext=Xf(f,g,d),p=h[d],"number"===typeof p?(nh(a,b,p,l,d),delete h[d]):ih(a,b,l,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Xf(f,g,h),
ih(a,b,d,h);b.treeContext=f;b.keyPath=e}function mh(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function ih(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,p=b.componentStack,q=b.blockedSegment;if(null===q)try{return Y(a,b,c,d)}catch(r){if(Ag(),c=r===bg?fg():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=zg();a=ah(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.hoistableState,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,
a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=p;Tf(g);return}}else{var m=q.children.length,v=q.chunks.length;try{return Y(a,b,c,d)}catch(r){if(Ag(),q.children.length=m,q.chunks.length=v,c=r===bg?fg():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=zg();q=b.blockedSegment;m=bh(a,q.chunks.length,null,b.formatContext,q.lastPushedText,!0);q.children.push(m);q.lastPushedText=!1;a=$g(a,d,b.node,b.childIndex,b.blockedBoundary,m,b.hoistableState,
b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=p;Tf(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;Tf(g);throw c;}function qh(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,rh(this,b,a))}
function oh(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)oh(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,p=f,q=Zg(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=p;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var m in d)delete d[m]}}
function sh(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){X(b,c,d);eh(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=X(b,c,d),oh(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&th(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=dh(b,a.componentStack),a=X(b,c,a),d.errorDigest=a,mh(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return sh(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&uh(b)}
function vh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var l=h.value.sheets.values(),p=l.next();0<e.remainingCapacity&&!p.done;p=l.next()){var q=p.value,m=q.props,v=m.href,r=q.props,x=ed(r.href,"style",{crossOrigin:r.crossOrigin,integrity:r.integrity,
nonce:r.nonce,type:r.type,fetchPriority:r.fetchPriority,referrerPolicy:r.referrerPolicy,media:r.media});if(2<=(e.remainingCapacity-=x.length))c.resets.style[v]=F,f&&(f+=", "),f+=x,c.resets.style[v]="string"===typeof m.crossOrigin||"string"===typeof m.integrity?[m.crossOrigin,m.integrity]:F;else break b}}f?d({Link:f}):d({})}}}catch(w){X(a,w,{})}}function th(a){null===a.trackedPostpones&&vh(a,!0);a.onShellError=Vg;a=a.onShellReady;a()}
function uh(a){vh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function lh(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&lh(a,c)}else a.completedSegments.push(b)}
function rh(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&th(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&lh(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(qh,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(lh(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&uh(a)}
function Yg(a){if(2!==a.status){var b=Kf,c=Sg.current;Sg.current=Mg;var d=Tg.current;Tg.current=Ng;var e=Wg;Wg=a;var f=Lg;Lg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],p=a,q=l.blockedSegment;if(null===q){var m=p;if(0!==l.replay.pendingTasks){Tf(l.context);try{Y(m,l,l.node,l.childIndex);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);rh(m,l.blockedBoundary,null)}catch(G){Ag();var v=G===bg?fg():G;if("object"===typeof v&&null!==v&&"function"===typeof v.then){var r=l.ping;v.then(r,r);l.thenableState=zg()}else{l.replay.pendingTasks--;l.abortSet.delete(l);var x=dh(m,l.componentStack);p=void 0;var w=m,t=l.blockedBoundary,T=v,V=l.replay.nodes,I=l.replay.slots;p=X(w,T,x);oh(w,t,V,I,T,p);m.pendingRootTasks--;0===m.pendingRootTasks&&th(m);m.allPendingTasks--;0===m.allPendingTasks&&uh(m)}}finally{}}}else if(m=
void 0,w=q,0===w.status){Tf(l.context);var Q=w.children.length,y=w.chunks.length;try{Y(p,l,l.node,l.childIndex),w.lastPushedText&&w.textEmbedded&&w.chunks.push(kc),l.abortSet.delete(l),w.status=1,rh(p,l.blockedBoundary,w)}catch(G){Ag();w.children.length=Q;w.chunks.length=y;var A=G===bg?fg():G;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var na=l.ping;A.then(na,na);l.thenableState=zg()}else{var Z=dh(p,l.componentStack);l.abortSet.delete(l);w.status=4;var H=l.blockedBoundary;m=X(p,
A,Z);null===H?eh(p,A):(H.pendingTasks--,4!==H.status&&(H.status=4,H.errorDigest=m,mh(p,H),H.parentFlushed&&p.clientRenderedBoundaries.push(H)));p.allPendingTasks--;0===p.allPendingTasks&&uh(p)}}finally{}}}g.splice(0,h);null!==a.destination&&wh(a,a.destination)}catch(G){X(a,G,{}),eh(a,G)}finally{Lg=f,Sg.current=c,Tg.current=d,c===Mg&&Tf(b),Wg=e}}}
function xh(a,b,c,d){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:return d=c.id,c.lastPushedText=!1,c.textEmbedded=!1,a=a.renderState,u(b,hd),u(b,a.placeholderPrefix),a=d.toString(16),u(b,a),z(b,id);case 1:c.status=2;var e=!0,f=c.chunks,g=0;c=c.children;for(var h=0;h<c.length;h++){for(e=c[h];g<e.index;g++)u(b,f[g]);e=yh(a,b,e,d)}for(;g<f.length-1;g++)u(b,f[g]);g<f.length&&(e=z(b,f[g]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function yh(a,b,c,d){var e=c.boundary;if(null===e)return xh(a,b,c,d);e.parentFlushed=!0;if(4===e.status)e=e.errorDigest,z(b,md),u(b,od),e&&(u(b,qd),u(b,E(e)),u(b,pd)),z(b,rd),xh(a,b,c,d);else if(1!==e.status)0===e.status&&(e.rootSegmentID=a.nextSegmentId++),0<e.completedSegments.length&&a.partialBoundaries.push(e),sd(b,a.renderState,e.rootSegmentID),d&&(e=e.fallbackState,e.styles.forEach(Df,d),e.stylesheets.forEach(Ef,d)),xh(a,b,c,d);else if(e.byteSize>a.progressiveChunkSize)e.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(e),
sd(b,a.renderState,e.rootSegmentID),xh(a,b,c,d);else{d&&(c=e.contentState,c.styles.forEach(Df,d),c.stylesheets.forEach(Ef,d));z(b,jd);c=e.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");yh(a,b,c[0],d)}return z(b,nd)}function zh(a,b,c,d){oe(b,a.renderState,c.parentFormatContext,c.id);yh(a,b,c,d);return pe(b,c.parentFormatContext)}
function Ah(a,b,c){for(var d=c.completedSegments,e=0;e<d.length;e++)Bh(a,b,c,d[e]);d.length=0;ef(b,c.contentState,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.contentState;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(u(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,u(b,ye)):0===(d.instructions&8)?(d.instructions|=8,u(b,ze)):u(b,Ae):0===(d.instructions&2)?(d.instructions|=2,u(b,we)):u(b,xe)):f?u(b,Ge):u(b,Fe);d=e.toString(16);
u(b,a.boundaryPrefix);u(b,d);g?u(b,Be):u(b,He);u(b,a.segmentPrefix);u(b,d);f?g?(u(b,Ce),uf(b,c)):(u(b,Ie),vf(b,c)):g&&u(b,De);d=g?z(b,Ee):z(b,Xb);return gd(b,a)&&d}
function Bh(a,b,c,d){if(2===d.status)return!0;var e=c.contentState,f=d.id;if(-1===f){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return zh(a,b,d,e)}if(f===c.rootSegmentID)return zh(a,b,d,e);zh(a,b,d,e);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(u(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,u(b,qe)):u(b,re)):u(b,ue);u(b,a.segmentPrefix);f=f.toString(16);u(b,f);d?u(b,se):u(b,ve);u(b,a.placeholderPrefix);
u(b,f);b=d?z(b,te):z(b,Xb);return b}
function wh(a,b){k=new Uint8Array(2048);n=0;$a=!0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var p=e.htmlChunks,q=e.headChunks,m;if(p){for(m=0;m<p.length;m++)u(b,p[m]);if(q)for(m=0;m<q.length;m++)u(b,q[m]);
else u(b,U("head")),u(b,R)}else if(q)for(m=0;m<q.length;m++)u(b,q[m]);var v=e.charsetChunks;for(m=0;m<v.length;m++)u(b,v[m]);v.length=0;e.preconnects.forEach(ff,b);e.preconnects.clear();var r=e.viewportChunks;for(m=0;m<r.length;m++)u(b,r[m]);r.length=0;e.fontPreloads.forEach(ff,b);e.fontPreloads.clear();e.highImagePreloads.forEach(ff,b);e.highImagePreloads.clear();e.styles.forEach(nf,b);var x=e.importMapChunks;for(m=0;m<x.length;m++)u(b,x[m]);x.length=0;e.bootstrapScripts.forEach(ff,b);e.scripts.forEach(ff,
b);e.scripts.clear();e.bulkPreloads.forEach(ff,b);e.bulkPreloads.clear();var w=e.hoistableChunks;for(m=0;m<w.length;m++)u(b,w[m]);w.length=0;p&&null===q&&u(b,Wc("head"));yh(a,b,d,null);a.completedRootSegment=null;gd(b,a.renderState)}else return;var t=a.renderState;d=0;var T=t.viewportChunks;for(d=0;d<T.length;d++)u(b,T[d]);T.length=0;t.preconnects.forEach(ff,b);t.preconnects.clear();t.fontPreloads.forEach(ff,b);t.fontPreloads.clear();t.highImagePreloads.forEach(ff,b);t.highImagePreloads.clear();t.styles.forEach(pf,
b);t.scripts.forEach(ff,b);t.scripts.clear();t.bulkPreloads.forEach(ff,b);t.bulkPreloads.clear();var V=t.hoistableChunks;for(d=0;d<V.length;d++)u(b,V[d]);V.length=0;var I=a.clientRenderedBoundaries;for(c=0;c<I.length;c++){var Q=I[c];t=b;var y=a.resumableState,A=a.renderState,na=Q.rootSegmentID,Z=Q.errorDigest,H=Q.errorMessage,G=Q.errorComponentStack,ia=0===y.streamingFormat;ia?(u(t,A.startInlineScript),0===(y.instructions&4)?(y.instructions|=4,u(t,Je)):u(t,Ke)):u(t,Oe);u(t,A.boundaryPrefix);u(t,na.toString(16));
ia&&u(t,Le);if(Z||H||G)ia?(u(t,Me),u(t,Te(Z||""))):(u(t,Pe),u(t,E(Z||"")));if(H||G)ia?(u(t,Me),u(t,Te(H||""))):(u(t,Qe),u(t,E(H||"")));G&&(ia?(u(t,Me),u(t,Te(G))):(u(t,Re),u(t,E(G))));if(ia?!z(t,Ne):!z(t,Xb)){a.destination=null;c++;I.splice(0,c);return}}I.splice(0,c);var va=a.completedBoundaries;for(c=0;c<va.length;c++)if(!Ah(a,b,va[c])){a.destination=null;c++;va.splice(0,c);return}va.splice(0,c);rb(b);k=new Uint8Array(2048);n=0;$a=!0;var wa=a.partialBoundaries;for(c=0;c<wa.length;c++){var La=wa[c];
a:{I=a;Q=b;var xa=La.completedSegments;for(y=0;y<xa.length;y++)if(!Bh(I,Q,La,xa[y])){y++;xa.splice(0,y);var Ma=!1;break a}xa.splice(0,y);Ma=ef(Q,La.contentState,I.renderState)}if(!Ma){a.destination=null;c++;wa.splice(0,c);return}}wa.splice(0,c);var oa=a.completedBoundaries;for(c=0;c<oa.length;c++)if(!Ah(a,b,oa[c])){a.destination=null;c++;oa.splice(0,c);return}oa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?
(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&u(b,Wc("body")),c.hasHtml&&u(b,Wc("html")),rb(b),Za(b),b.end(),a.destination=null):(rb(b),Za(b))}}function Ch(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return Ff.run(a,Yg,a)});null===a.trackedPostpones&&setImmediate(function(){return Ff.run(a,Dh,a)})}function Dh(a){vh(a,0===a.pendingRootTasks)}
function Af(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setImmediate(function(){var b=a.destination;b?wh(a,b):a.flushScheduled=!1}))}function Eh(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{wh(a,b)}catch(c){X(a,c,{}),eh(a,c)}}}
function Fh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return sh(e,a,d)});c.clear()}null!==a.destination&&wh(a,a.destination)}catch(e){X(a,e,{}),eh(a,e)}}function Gh(a,b){return function(){return Eh(b,a)}}function Hh(a,b){return function(){a.destination=null;Fh(a,Error(b))}}
function Ih(a,b){var c=b?b.identifierPrefix:void 0;var d=0;void 0!==(b?b.unstable_externalRuntimeSrc:void 0)&&(d=1);c={idPrefix:void 0===c?"":c,nextFormID:0,streamingFormat:d,bootstrapScriptContent:b?b.bootstrapScriptContent:void 0,bootstrapScripts:b?b.bootstrapScripts:void 0,bootstrapModules:b?b.bootstrapModules:void 0,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},
moduleUnknownResources:{},moduleScriptResources:{}};var e=b?b.nonce:void 0,f=b?b.unstable_externalRuntimeSrc:void 0,g=b?b.importMap:void 0;d=b?b.onHeaders:void 0;var h=b?b.maxHeadersLength:void 0,l=void 0===e?Yb:B('<script nonce="'+E(e)+'">'),p=c.idPrefix,q=[],m=null,v=c.bootstrapScriptContent,r=c.bootstrapScripts,x=c.bootstrapModules;void 0!==v&&q.push(l,(""+v).replace(fc,gc),Zb);void 0!==f&&("string"===typeof f?(m={src:f,chunks:[]},Xc(m.chunks,{src:f,async:!0,integrity:void 0,nonce:e})):(m={src:f.src,
chunks:[]},Xc(m.chunks,{src:f.src,async:!0,integrity:f.integrity,nonce:e})));f=[];void 0!==g&&(f.push(hc),f.push((""+JSON.stringify(g)).replace(fc,gc)),f.push(ic));g=d?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof h?h:2E3}:null;d={placeholderPrefix:B(p+"P:"),segmentPrefix:B(p+"S:"),boundaryPrefix:B(p+"B:"),startInlineScript:l,htmlChunks:null,headChunks:null,externalRuntimeScript:m,bootstrapChunks:q,importMapChunks:f,onHeaders:d,headers:g,resets:{font:{},
dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],viewportChunks:[],hoistableChunks:[],preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:e,hoistableState:null,stylesToHoist:!1};if(void 0!==r)for(l=0;l<r.length;l++)f=r[l],g=m=void 0,h={rel:"preload",as:"script",fetchPriority:"low",
nonce:e},"string"===typeof f?h.href=p=f:(h.href=p=f.src,h.integrity=g="string"===typeof f.integrity?f.integrity:void 0,h.crossOrigin=m="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=c,v=p,f.scriptResources[v]=null,f.moduleScriptResources[v]=null,f=[],S(f,h),d.bootstrapScripts.add(f),q.push($b,E(p)),e&&q.push(bc,E(e)),"string"===typeof g&&q.push(cc,E(g)),"string"===typeof m&&q.push(dc,E(m)),q.push(ec);if(void 0!==x)for(r=0;r<x.length;r++)h=
x[r],m=p=void 0,g={rel:"modulepreload",fetchPriority:"low",nonce:e},"string"===typeof h?g.href=l=h:(g.href=l=h.src,g.integrity=m="string"===typeof h.integrity?h.integrity:void 0,g.crossOrigin=p="string"===typeof h||null==h.crossOrigin?void 0:"use-credentials"===h.crossOrigin?"use-credentials":""),h=c,f=l,h.scriptResources[f]=null,h.moduleScriptResources[f]=null,h=[],S(h,g),d.bootstrapScripts.add(h),q.push(ac,E(l)),e&&q.push(bc,E(e)),"string"===typeof m&&q.push(cc,E(m)),"string"===typeof p&&q.push(dc,
E(p)),q.push(ec);e=b?b.namespaceURI:void 0;e=K("http://www.w3.org/2000/svg"===e?3:"http://www.w3.org/1998/Math/MathML"===e?4:0,null,0);x=b?b.progressiveChunkSize:void 0;r=b?b.onError:void 0;l=b?b.onAllReady:void 0;p=b?b.onShellReady:void 0;m=b?b.onShellError:void 0;g=b?b.onPostpone:void 0;h=b?b.formState:void 0;Db.current=Wb;b=[];q=new Set;c={destination:null,flushScheduled:!1,resumableState:c,renderState:d,rootFormatContext:e,progressiveChunkSize:void 0===x?12800:x,status:0,fatalError:null,nextSegmentId:0,
allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:q,pingedTasks:b,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===r?Ug:r,onPostpone:void 0===g?Vg:g,onAllReady:void 0===l?Vg:l,onShellReady:void 0===p?Vg:p,onShellError:void 0===m?Vg:m,onFatalError:Vg,formState:void 0===h?null:h};d=bh(c,0,null,e,!1,!1);d.parentFlushed=!0;a=$g(c,null,a,-1,null,d,null,q,null,e,If,null,Wf,null,!1);b.push(a);return c}
exports.renderToPipeableStream=function(a,b){var c=Ih(a,b),d=!1;Ch(c);return{pipe:function(e){if(d)throw Error("React currently only supports piping to one writable stream.");d=!0;vh(c,null===c.trackedPostpones?0===c.pendingRootTasks:null===c.completedRootSegment?0===c.pendingRootTasks:5!==c.completedRootSegment.status);Eh(c,e);e.on("drain",Gh(e,c));e.on("error",Hh(c,"The destination stream errored while writing data."));e.on("close",Hh(c,"The destination stream closed early."));return e},abort:function(e){Fh(c,
e)}}};exports.version="18.3.0-canary-178c267a4e-20241218";

//# sourceMappingURL=react-dom-server.node.production.min.js.map
