{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "NextFetchEvent", "NextRequest", "NextResponse", "relativizeURL", "waitUntilSymbol", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_PARAMETERS", "ensureInstrumentationRegistered", "RequestAsyncStorageWrapper", "requestAsyncStorage", "getTracer", "MiddlewareSpan", "getEdgePreviewProps", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "self", "__BUILD_MANIFEST", "url", "requestUrl", "nextConfig", "searchParams", "value", "getAll", "normalizedKey", "delete", "val", "append", "buildId", "isNextDataRequest", "pathname", "requestHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCacheShared", "IncrementalCache", "__incrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "event", "response", "cookiesFromResponse", "isMiddleware", "trace", "execute", "spanName", "nextUrl", "attributes", "wrap", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "Promise", "all", "fetchMetrics"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,EAAEC,uBAAuB,QAAQ,UAAS;AAC9E,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SAASC,iBAAiB,QAAQ,6CAA4C;AAC9E,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,OAAO,MAAMC,wBAAwBd;IAInCe,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIzB,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAG,cAAc;QACZ,MAAM,IAAI1B,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,YAAY;QACV,MAAM,IAAI3B,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAASvB;IACf,OAAOuB,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;IAEtBqB;IACA,MAAM7B;IAEN,yCAAyC;IACzC,MAAMqC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IAEzD/B,OAAOK,OAAO,CAAC2B,GAAG,GAAG1C,gBAAgBU,OAAOK,OAAO,CAAC2B,GAAG;IAEvD,MAAMC,aAAa,IAAI7C,QAAQY,OAAOK,OAAO,CAAC2B,GAAG,EAAE;QACjDtB,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMzB,OAAO;WAAIwB,WAAWE,YAAY,CAAC1B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM2B,QAAQH,WAAWE,YAAY,CAACE,MAAM,CAACvB;QAE7ChC,wBAAwBgC,KAAK,CAACwB;YAC5BL,WAAWE,YAAY,CAACI,MAAM,CAACD;YAE/B,KAAK,MAAME,OAAOJ,MAAO;gBACvBH,WAAWE,YAAY,CAACM,MAAM,CAACH,eAAeE;YAChD;YACAP,WAAWE,YAAY,CAACI,MAAM,CAACzB;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAM4B,UAAUT,WAAWS,OAAO;IAClCT,WAAWS,OAAO,GAAG;IAErB,MAAMC,oBAAoB3C,OAAOK,OAAO,CAACK,OAAO,CAAC,gBAAgB;IAEjE,IAAIiC,qBAAqBV,WAAWW,QAAQ,KAAK,UAAU;QACzDX,WAAWW,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBhE,4BAA4BmB,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAMoC,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAAClB,iBAAiB;QACpB,KAAK,MAAMmB,SAASzD,kBAAmB;YACrC,MAAMuB,MAAMkC,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMd,QAAQS,eAAehC,GAAG,CAACC;YACjC,IAAIsB,OAAO;gBACTU,cAAcK,GAAG,CAACrC,KAAK+B,eAAehC,GAAG,CAACC;gBAC1C+B,eAAeN,MAAM,CAACzB;YACxB;QACF;IACF;IAEA,MAAMsC,eAAe9B,QAAQC,GAAG,CAAC8B,kCAAkC,GAC/D,IAAIC,IAAItD,OAAOK,OAAO,CAAC2B,GAAG,IAC1BC;IAEJ,MAAM5B,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOZ,0BAA0B+D,cAAc,MAAMH,QAAQ;QAC7D/C,MAAM;YACJqD,MAAMvD,OAAOK,OAAO,CAACkD,IAAI;YACzBC,KAAKxD,OAAOK,OAAO,CAACmD,GAAG;YACvB9C,SAASmC;YACTY,IAAIzD,OAAOK,OAAO,CAACoD,EAAE;YACrBC,QAAQ1D,OAAOK,OAAO,CAACqD,MAAM;YAC7BxB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;YACrCyB,QAAQ3D,OAAOK,OAAO,CAACsD,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIhB,mBAAmB;QACrBiB,OAAOC,cAAc,CAACxD,SAAS,YAAY;YACzCyD,YAAY;YACZ1B,OAAO;QACT;IACF;IAEA,IACE,6CAA6C;IAC7C,8CAA8C;IAC9C,6CAA6C;IAC7C,CAAC,AAAC2B,WAAmBC,wBAAwB,IAC7C,AAAChE,OAAeiE,gBAAgB,EAChC;QACEF,WAAmBG,kBAAkB,GAAG,IAAI,AAC5ClE,OACAiE,gBAAgB,CAAC;YACjBE,QAAQ;YACRC,YAAY;YACZC,aAAa/C,QAAQC,GAAG,CAAC+C,QAAQ,KAAK;YACtCC,qBAAqBjD,QAAQC,GAAG,CAACiD,6BAA6B;YAC9DC,KAAKnD,QAAQC,GAAG,CAAC+C,QAAQ,KAAK;YAC9BzB,gBAAgB7C,OAAOK,OAAO,CAACK,OAAO;YACtCgE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASnF;gBACX;YACF;QACF;IACF;IAEA,MAAMoF,QAAQ,IAAIlG,eAAe;QAAEsB;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAI8E;IACJ,IAAIC;IAEJD,WAAW,MAAMlE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAM+E,eACJpF,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAIgF,cAAc;YAChB,OAAOzF,YAAY0F,KAAK,CACtBzF,eAAe0F,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAElF,QAAQqD,MAAM,CAAC,CAAC,EAAErD,QAAQmF,OAAO,CAAC5C,QAAQ,CAAC,CAAC;gBACpE6C,YAAY;oBACV,eAAepF,QAAQmF,OAAO,CAAC5C,QAAQ;oBACvC,eAAevC,QAAQqD,MAAM;gBAC/B;YACF,GACA,IACEjE,2BAA2BiG,IAAI,CAC7BhG,qBACA;oBACEiG,KAAKtF;oBACLuF,YAAY;wBACVC,iBAAiB,CAACC;4BAChBX,sBAAsBW;wBACxB;wBACA,2EAA2E;wBAC3EC,cAAclG;oBAChB;gBACF,GACA,IAAMG,OAAOgG,OAAO,CAAC3F,SAAS4E;QAGtC;QACA,OAAOjF,OAAOgG,OAAO,CAAC3F,SAAS4E;IACjC;IAEA,yCAAyC;IACzC,IAAIC,YAAY,CAAEA,CAAAA,oBAAoBe,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIhB,YAAYC,qBAAqB;QACnCD,SAASxE,OAAO,CAACyC,GAAG,CAAC,cAAcgC;IACrC;IAEA;;;;;GAKC,GACD,MAAMgB,UAAUjB,4BAAAA,SAAUxE,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIqE,YAAYiB,WAAW,CAACtE,iBAAiB;QAC3C,MAAMuE,aAAa,IAAIhH,QAAQ+G,SAAS;YACtCE,aAAa;YACb3F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA,IAAI,CAACZ,QAAQC,GAAG,CAAC8B,kCAAkC,EAAE;YACnD,IAAI+C,WAAWE,IAAI,KAAKjG,QAAQmF,OAAO,CAACc,IAAI,EAAE;gBAC5CF,WAAW1D,OAAO,GAAGA,WAAW0D,WAAW1D,OAAO;gBAClDwC,SAASxE,OAAO,CAACyC,GAAG,CAAC,wBAAwBoD,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqBtH,cACzBqH,OAAOH,aACPG,OAAOtE;QAGT,IACEU,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACErB,CAAAA,QAAQC,GAAG,CAACkF,0CAA0C,IACtDD,mBAAmBE,KAAK,CAAC,gBAAe,GAE1C;YACAxB,SAASxE,OAAO,CAACyC,GAAG,CAAC,oBAAoBqD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMG,WAAWzB,4BAAAA,SAAUxE,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIqE,YAAYyB,YAAY,CAAC9E,iBAAiB;QAC5C,MAAM+E,cAAc,IAAIxH,QAAQuH,UAAU;YACxCN,aAAa;YACb3F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA;;;KAGC,GACDgD,WAAW,IAAIe,SAASf,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAAC5D,QAAQC,GAAG,CAAC8B,kCAAkC,EAAE;YACnD,IAAIuD,YAAYN,IAAI,KAAKjG,QAAQmF,OAAO,CAACc,IAAI,EAAE;gBAC7CM,YAAYlE,OAAO,GAAGA,WAAWkE,YAAYlE,OAAO;gBACpDwC,SAASxE,OAAO,CAACyC,GAAG,CAAC,YAAYoD,OAAOK;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAIjE,mBAAmB;YACrBuC,SAASxE,OAAO,CAAC6B,MAAM,CAAC;YACxB2C,SAASxE,OAAO,CAACyC,GAAG,CAClB,qBACAjE,cAAcqH,OAAOK,cAAcL,OAAOtE;QAE9C;IACF;IAEA,MAAM4E,gBAAgB3B,WAAWA,WAAWjG,aAAa6H,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAcnG,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMmG,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACjG,KAAKsB,MAAM,IAAIU,cAAe;YACxC+D,cAAcnG,OAAO,CAACyC,GAAG,CAAC,CAAC,qBAAqB,EAAErC,IAAI,CAAC,EAAEsB;YACzD4E,mBAAmBC,IAAI,CAACnG;QAC1B;QAEA,IAAIkG,mBAAmBE,MAAM,GAAG,GAAG;YACjCL,cAAcnG,OAAO,CAACyC,GAAG,CACvB,iCACA4D,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLjC,UAAU2B;QACVtG,WAAW6G,QAAQC,GAAG,CAACpC,KAAK,CAAC9F,gBAAgB;QAC7CmI,cAAcjH,QAAQiH,YAAY;IACpC;AACF"}