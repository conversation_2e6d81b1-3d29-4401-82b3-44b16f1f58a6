{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "names": ["webpack", "needsExperimentalReact", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "qualities", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "getDefineEnv", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "experimental", "ppr", "deploymentId", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeFonts", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "analyticsId", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "undefined", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAKA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAE9E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AAuCA;;CAEC,GACD,SAASC;IACP,MAAMC,YAAuB,CAAC;IAC9B,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASK,iBAAiBb,MAA0B;IAClD,sCAAsC;IACtC,MAAMQ,YAAuB,CAAC;IAC9B,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,SAAS,MAAM;YACjBb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,IAAI,CAAC,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASM,mBAAmBN,SAAoB;IAC9C,MAAMO,uBAA4C,CAAC;IACnD,IAAK,MAAMd,OAAOO,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACP,IAAI;QAC5Bc,oBAAoB,CAACd,IAAI,GAAGe,KAAKC,SAAS,CAACL;IAC7C;IAEA,OAAOG;AACT;AAEA,SAASG,eACPlB,MAA0B,EAC1BmB,GAAY;QAUKnB,gBAKSA,iBACDA;IAdzB,OAAO;QACL,iCAAiC;YAC/BoB,aAAapB,OAAOqB,MAAM,CAACD,WAAW;YACtCE,YAAYtB,OAAOqB,MAAM,CAACC,UAAU;YACpCC,WAAWvB,OAAOqB,MAAM,CAACE,SAAS;YAClCC,MAAMxB,OAAOqB,MAAM,CAACG,IAAI;YACxBC,QAAQzB,OAAOqB,MAAM,CAACI,MAAM;YAC5BC,qBAAqB1B,OAAOqB,MAAM,CAACK,mBAAmB;YACtDC,WAAW,EAAE3B,2BAAAA,iBAAAA,OAAQqB,MAAM,qBAAdrB,eAAgB2B,WAAW;YACxC,GAAIR,MACA;gBACE,6DAA6D;gBAC7DS,SAAS5B,OAAOqB,MAAM,CAACO,OAAO;gBAC9BC,cAAc,GAAE7B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe6B,cAAc;gBAC7CC,aAAa,GAAE9B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe8B,aAAa;gBAC3CC,QAAQ/B,OAAO+B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,mBAAmB,EACnBlC,MAAM,EACNmB,GAAG,EACHgB,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QAmCN1C,iCAETA,kCAGSA,kCAETA,kCA6C6BA;IAtFrC,MAAMQ,YAAuB;QAC3B,+CAA+C;QAC/CmC,mBAAmB;QAEnB,GAAGpC,mCAAmC;QACtC,GAAGM,iBAAiBb,OAAO;QAC3B,GAAI,CAACuC,eACD,CAAC,IACD;YACEK,aACE;;;;aAIC,GACDnC,QAAQC,GAAG,CAACmC,0BAA0B,IAAI;QAC9C,CAAC;QACL,qBAAqBZ;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBAAwBd,MAAM,gBAAgB;QAC9C,4BAA4BoB,eACxB,SACAE,eACA,WACA;QACJ,4BAA4B;QAC5B,0BAA0BzC,OAAO8C,YAAY,CAACC,GAAG,KAAK;QACtD,kCAAkC/C,OAAOgD,YAAY,IAAI;QACzD,6CAA6CZ,uBAAuB;QACpE,0CAA0CM,sBAAsB,EAAE;QAClE,8CACE1C,OAAO8C,YAAY,CAACG,oBAAoB,IAAI;QAC9C,sDAAsDjC,KAAKC,SAAS,CAClEiC,MAAMC,QAAOnD,kCAAAA,OAAO8C,YAAY,CAACM,UAAU,qBAA9BpD,gCAAgCqD,OAAO,KAChD,GAAG,aAAa;YAChBrD,mCAAAA,OAAO8C,YAAY,CAACM,UAAU,qBAA9BpD,iCAAgCqD,OAAO;QAE7C,qDAAqDrC,KAAKC,SAAS,CACjEiC,MAAMC,QAAOnD,mCAAAA,OAAO8C,YAAY,CAACM,UAAU,qBAA9BpD,iCAAgCsD,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBtD,mCAAAA,OAAO8C,YAAY,CAACM,UAAU,qBAA9BpD,iCAAgCsD,MAAM;QAE5C,mDACEtD,OAAO8C,YAAY,CAACS,kBAAkB,IAAI;QAC5C,6CACErB,CAAAA,uCAAAA,oBAAqBsB,YAAY,KAAI;QACvC,6CACEtB,CAAAA,uCAAAA,oBAAqBuB,aAAa,KAAI;QACxC,8CACEzD,OAAO8C,YAAY,CAACY,qBAAqB,IAAI;QAC/C,0CACE1D,OAAO8C,YAAY,CAACa,kBAAkB,IAAI;QAC5C,mCAAmC3D,OAAO4D,WAAW;QACrD,mBAAmBtB;QACnB,gCAAgC7B,QAAQC,GAAG,CAACmD,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAI1C,OAAQmB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqCnC,OAAO8D,aAAa;QACzD,sCACE9D,OAAO+D,aAAa,CAACC,aAAa,IAAI;QACxC,+CACEhE,OAAO+D,aAAa,CAACE,qBAAqB,IAAI;QAChD,kCACEjE,OAAOkE,eAAe,KAAK,OAAO,QAAQlE,OAAOkE,eAAe;QAClE,sCACE,6EAA6E;QAC7ElE,OAAOkE,eAAe,KAAK,OAAO,OAAOlE,OAAOkE,eAAe;QACjE,qCAAqC,CAAC/C,OAAOnB,OAAOmE,aAAa;QACjE,mCACE,AAACnE,CAAAA,OAAO8C,YAAY,CAACsB,WAAW,IAAI,CAACjD,GAAE,KAAM;QAC/C,qCACE,AAACnB,CAAAA,OAAO8C,YAAY,CAACuB,iBAAiB,IAAI,CAAClD,GAAE,KAAM;QACrD,yCACEnB,OAAO8C,YAAY,CAACwB,iBAAiB,IAAI;QAC3C,GAAGpD,eAAelB,QAAQmB,IAAI;QAC9B,sCAAsCnB,OAAOuE,QAAQ;QACrD,uCACEvE,OAAO8C,YAAY,CAAC0B,cAAc,IAAI;QACxC,mCAAmCnC;QACnC,oCAAoCrC,OAAO+B,MAAM;QACjD,mCAAmC,CAAC,CAAC/B,OAAOyE,IAAI;QAChD,mCAAmCzE,EAAAA,eAAAA,OAAOyE,IAAI,qBAAXzE,aAAa4B,OAAO,KAAI;QAC3D,mCAAmC5B,OAAO0E,WAAW;QACrD,kDACE1E,OAAO2E,0BAA0B;QACnC,0DACE3E,OAAO8C,YAAY,CAAC8B,iCAAiC,IAAI;QAC3D,4CACE5E,OAAO6E,yBAAyB;QAClC,iDACE,AAAC7E,CAAAA,OAAO8C,YAAY,CAACgC,oBAAoB,IACvC9E,OAAO8C,YAAY,CAACgC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACE/E,OAAO8C,YAAY,CAACgC,oBAAoB,IAAI;QAC9C,0CACE9E,OAAO8C,YAAY,CAACkC,gBAAgB,IAAI;QAC1C,mCAAmChF,OAAOiF,WAAW;QACrD,GAAIzC,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACA0C,SAAS;QACb,GAAI1C,0BACA;YACE,yCACE1C,uBAAuBE;QAC3B,IACAkF,SAAS;IACf;IACA,OAAOpE,mBAAmBN;AAC5B;AAEA,OAAO,SAAS2E,mBAAmBC,OAA+B;IAChE,OAAO,IAAIvF,QAAQwF,YAAY,CAACrD,aAAaoD;AAC/C"}