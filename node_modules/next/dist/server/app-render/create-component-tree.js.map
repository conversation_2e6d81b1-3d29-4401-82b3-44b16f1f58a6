{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "names": ["createComponentTree", "props", "getTracer", "trace", "NextNodeServerSpan", "spanName", "createComponentTreeInternal", "createSegmentPath", "loaderTree", "tree", "parentParams", "firstItem", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "asNotFound", "metadataOutlet", "ctx", "missingSlots", "renderOpts", "nextConfigOutput", "experimental", "staticGenerationStore", "componentMod", "NotFoundBoundary", "LayoutRouter", "RenderFromTemplateContext", "ClientPageRoot", "createUntrackedSearchParams", "createDynamicallyTrackedSearchParams", "serverHooks", "DynamicServerError", "Postpone", "pagePath", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "layoutOrPagePath", "segment", "components", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "layoutOrPageMod", "getLayoutOrPageModule", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "dynamic", "StaticGenBailoutError", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "prerenderState", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "validateRevalidate", "urlPathname", "defaultRevalidate", "dynamicUsageErr", "LayoutOrPage", "interopDefault", "undefined", "Component", "parallelKeys", "Object", "keys", "hasSlot<PERSON>ey", "length", "componentProps", "NotFoundComponent", "RootLayoutComponent", "params", "process", "env", "NODE_ENV", "isValidElementType", "require", "Error", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "parallelRouteMap", "Promise", "all", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "currentSegmentPath", "parallelRoute", "notFoundComponent", "childCacheNodeSeedData", "hasLoadingComponentInTree", "ppr", "parsedTree", "endsWith", "PARALLEL_ROUTE_DEFAULT_PATH", "add", "seedData", "child", "parallel<PERSON><PERSON>er<PERSON>ey", "segmentPath", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingData", "children", "reason", "pathname", "isClientComponent", "isClientReference", "meta", "name", "content", "console", "segmentElement", "searchParams"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;;8DAxBE;iCACgB;8BACI;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;4BACP;sCACS;wBAClB;2BACS;yCACG;;;;;;AAU/B,SAASA,oBAAoBC,KAanC;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,6BAAkB,CAACJ,mBAAmB,EACtC;QACEK,UAAU;IACZ,GACA,IAAMC,4BAA4BL;AAEtC;AAEA,eAAeK,4BAA4B,EACzCC,iBAAiB,EACjBC,YAAYC,IAAI,EAChBC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,UAAU,EACVC,cAAc,EACdC,GAAG,EACHC,YAAY,EAcb;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,qBAAqB,EACrBC,cAAc,EACZC,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,2BAA2B,EAC3BC,oCAAoC,EACpCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDC,QAAQ,EACRC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAGnB;IAEJ,MAAM,EAAEoB,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,cAAc,EAAE,GACnEC,IAAAA,gCAAe,EAAClC;IAElB,MAAM,EAAEmC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,aAAaC,QAAQ,EAAE,GAAGP;IAEpE,MAAMQ,+BAA+B,IAAIC,IAAIrC;IAC7C,MAAMsC,8BAA8B,IAAID,IAAIpC;IAC5C,MAAMsC,2CAA2C,IAAIF,IACnDnC;IAGF,MAAMsC,cAAcC,IAAAA,8BAAc,EAAC;QACjCpC;QACAqB;QACA1B,aAAaoC;QACbnC,YAAYqC;QACZpC,yBAAyBqC;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGZ,WAChD,MAAMa,IAAAA,gEAA+B,EAAC;QACpCxC;QACAyC,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzBhC,aAAaoC;QACbnC,YAAYqC;IACd,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGnB,QAChD,MAAMY,IAAAA,gEAA+B,EAAC;QACpCxC;QACAyC,UAAUb,KAAK,CAAC,EAAE;QAClBc,cAAcd,KAAK,CAAC,EAAE;QACtBjC,aAAaoC;QACbnC,YAAYqC;IACd,KACA,EAAE;IAEN,MAAM,CAACe,SAASC,eAAeC,eAAe,GAAGrB,UAC7C,MAAMW,IAAAA,gEAA+B,EAAC;QACpCxC;QACAyC,UAAUZ,OAAO,CAAC,EAAE;QACpBa,cAAcb,OAAO,CAAC,EAAE;QACxBlC,aAAaoC;QACbnC,YAAYqC;IACd,KACA,EAAE;IAEN,MAAMkB,WAAW,OAAOzB,WAAW;IACnC,MAAM0B,SAAS,OAAOhC,SAAS;IAC/B,MAAM,CAACiC,gBAAgB,GAAG,MAAMrE,IAAAA,iBAAS,IAAGC,KAAK,CAC/CC,6BAAkB,CAACoE,qBAAqB,EACxC;QACEC,UAAU,CAAEJ,CAAAA,YAAYC,MAAK;QAC7BjE,UAAU;QACVqE,YAAY;YACV,gBAAgBlC;QAClB;IACF,GACA,IAAMgC,IAAAA,mCAAqB,EAAC/D;IAG9B;;GAEC,GACD,MAAMkE,wBAAwBN,YAAY,CAACzD;IAC3C;;GAEC,GACD,MAAMgE,uCACJhE,sBAAsB+D;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG9B,WAC/B,MAAMU,IAAAA,gEAA+B,EAAC;QACpCxC;QACAyC,UAAUX,QAAQ,CAAC,EAAE;QACrBY,cAAcZ,QAAQ,CAAC,EAAE;QACzBnC,aAAaoC;QACbnC,YAAYqC;IACd,KACA,EAAE;IAEN,IAAI4B,UAAUR,mCAAAA,gBAAiBQ,OAAO;IAEtC,IAAI1D,qBAAqB,UAAU;QACjC,IAAI,CAAC0D,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,IAAIC,8CAAqB,CAC7B,CAAC,6SAA6S,CAAC;QAEnT;IACF;IAEA,IAAI,OAAOD,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBxD,sBAAsB0D,kBAAkB,GAAG;QAC7C,OAAO,IAAIF,YAAY,iBAAiB;YACtCxD,sBAAsB2D,YAAY,GAAG;YAErC,0DAA0D;YAC1D,IACE3D,sBAAsB4D,kBAAkB,IACxC,CAAC5D,sBAAsB6D,cAAc,EACrC;gBACA,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,IAAIrD,mBACd,CAAC,qEAAqE,CAAC;gBAEzET,sBAAsB+D,uBAAuB,GAAGD,IAAIE,OAAO;gBAC3DhE,sBAAsBiE,iBAAiB,GAAGH,IAAII,KAAK;gBACnD,MAAMJ;YACR;QACF,OAAO;YACL9D,sBAAsB0D,kBAAkB,GAAG;YAC3C1D,sBAAsBmE,WAAW,GAAGX,YAAY;QAClD;IACF;IAEA,IAAI,QAAOR,mCAAAA,gBAAiBoB,UAAU,MAAK,UAAU;QACnDpE,sBAAsBoE,UAAU,GAAGpB,mCAAAA,gBAAiBoB,UAAU;IAChE;IAEA,IAAI,QAAOpB,mCAAAA,gBAAiBqB,UAAU,MAAK,aAAa;QACtDC,IAAAA,8BAAkB,EAChBtB,mCAAAA,gBAAiBqB,UAAU,EAC3BrE,sBAAsBuE,WAAW;IAErC;IAEA,IAAI,QAAOvB,mCAAAA,gBAAiBqB,UAAU,MAAK,UAAU;QACnD1E,IAAI6E,iBAAiB,GAAGxB,gBAAgBqB,UAAU;QAElD,IACE,OAAOrE,sBAAsBqE,UAAU,KAAK,eAC3C,OAAOrE,sBAAsBqE,UAAU,KAAK,YAC3CrE,sBAAsBqE,UAAU,GAAG1E,IAAI6E,iBAAiB,EAC1D;YACAxE,sBAAsBqE,UAAU,GAAG1E,IAAI6E,iBAAiB;QAC1D;QAEA,IACE,CAACxE,sBAAsBmE,WAAW,IAClCnE,sBAAsB4D,kBAAkB,IACxCjE,IAAI6E,iBAAiB,KAAK,KAC1B,wEAAwE;QACxE,0CAA0C;QAC1C,CAACxE,sBAAsB6D,cAAc,EACrC;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAE9C,QAAQ,CAAC;YACrEjB,sBAAsB+D,uBAAuB,GAAGA;YAEhD,MAAM,IAAItD,mBAAmBsD;QAC/B;IACF;IAEA,oEAAoE;IACpE,IAAI/D,sBAAsByE,eAAe,EAAE;QACzC,MAAMzE,sBAAsByE,eAAe;IAC7C;IAEA,MAAMC,eAAqD1B,kBACvD,MAAM2B,IAAAA,8BAAc,EAAC3B,mBACrB4B;IAEJ;;GAEC,GACD,IAAIC,YAAYH;IAChB,MAAMI,eAAeC,OAAOC,IAAI,CAAC7D;IACjC,MAAM8D,aAAaH,aAAaI,MAAM,GAAG;IAEzC,gGAAgG;IAChG,6EAA6E;IAC7E,4GAA4G;IAC5G,gHAAgH;IAChH,mCAAmC;IACnC,IAAID,cAAc7B,yBAAyBsB,cAAc;QACvDG,YAAY,CAACM;YACX,MAAMC,oBAAoB9B;YAC1B,MAAM+B,sBAAsBX;YAC5B,qBACE,qBAACxE;gBACCuB,UACE2D,kCACE;;wBACGtD;sCAKD,sBAACuD;4BAAoBC,QAAQH,eAAeG,MAAM;;gCAC/C/B;8CACD,qBAAC6B;;;;qBAGHR;0BAGN,cAAA,qBAACS;oBAAqB,GAAGF,cAAc;;;QAG7C;IACF;IAEA,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,AAAC5C,CAAAA,UAAU,OAAO8B,cAAc,WAAU,KAC1C,CAACa,mBAAmBb,YACpB;YACA,MAAM,IAAIe,MACR,CAAC,sDAAsD,EAAEjF,SAAS,CAAC,CAAC;QAExE;QAEA,IACE,OAAO6B,mBAAmB,eAC1B,CAACkD,mBAAmBlD,iBACpB;YACA,MAAM,IAAIoD,MACR,CAAC,8DAA8D,EAAE3E,QAAQ,CAAC;QAE9E;QAEA,IAAI,OAAO0B,YAAY,eAAe,CAAC+C,mBAAmB/C,UAAU;YAClE,MAAM,IAAIiD,MACR,CAAC,0DAA0D,EAAE3E,QAAQ,CAAC;QAE1E;QAEA,IAAI,OAAOqC,aAAa,eAAe,CAACoC,mBAAmBpC,WAAW;YACpE,MAAM,IAAIsC,MACR,CAAC,2DAA2D,EAAE3E,QAAQ,CAAC;QAE3E;IACF;IAEA,iCAAiC;IACjC,MAAM4E,eAAejF,2BAA2BK;IAChD;;GAEC,GACD,MAAM6E,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAG5G,YAAY;QACf,CAAC0G,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEA5G;IACN,4BAA4B;IAC5B,MAAM8G,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGjF;IAEhE,EAAE;IACF,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMkF,mBAAmB,MAAMC,QAAQC,GAAG,CACxCtB,OAAOC,IAAI,CAAC7D,gBAAgBmF,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,qBAAwCrH,YAC1C;YAACmH;SAAiB,GAClB;YAACN;YAAeM;SAAiB;QAErC,MAAMG,gBAAgBvF,cAAc,CAACoF,iBAAiB;QAEtD,MAAMI,oBACJrD,YAAYkD,mCAAqB,qBAAClD,gBAAcsB;QAElD,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAIgC,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,qEAAqE;QACrE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3B/F,cACC8B,CAAAA,WAAW,CAACkE,IAAAA,oDAAyB,EAACH,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAAC3G,aAAa+G,GAAG,EACjB;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIvB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB7F,cAAc;oBAKxDmH;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAa3F,IAAAA,gCAAe,EAACsF;gBACnC,KACEK,+BAAAA,WAAW/F,gBAAgB,qBAA3B+F,6BAA6BC,QAAQ,CAACC,iDAA2B,GACjE;oBACArH,aAAasH,GAAG,CAACX;gBACnB;YACF;YAEA,MAAMY,WAAW,MAAMpI,4BAA4B;gBACjDC,mBAAmB,CAACoI;oBAClB,OAAOpI,kBAAkB;2BAAIyH;2BAAuBW;qBAAM;gBAC5D;gBACAnI,YAAYyH;gBACZvH,cAAc2G;gBACdzG,oBAAoBgE;gBACpB/D,aAAaoC;gBACbnC,YAAYqC;gBACZpC,yBAAyBqC;gBACzBpC;gBACAC,gBAAgB8G,qBAAqB9G,iBAAiBkF;gBACtDjF;gBACAC;YACF;YAEAgH,yBAAyBO;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLZ;0BACA,qBAACpG;gBACCkH,mBAAmBd;gBACnBe,aAAatI,kBAAkByH;gBAC/B,sKAAsK;gBACtKlF,OAAOiB;gBACPC,aAAaA;gBACbC,cAAcA;gBACdpB,wBACE,qBAACU;8BACC,cAAA,qBAAC5B;;gBAGL6B,gBAAgBA;gBAChBC,iBAAiBA;gBACjBT,UAAUkF;gBACVpD,gBAAgBA;;YAElBqD;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIW,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMd,iBAAiBP,iBAAkB;QAC5C,MAAM,CAACI,kBAAkBkB,mBAAmBC,WAAW,GAAGhB;QAC1Da,kBAAkB,CAAChB,iBAAiB,GAAGkB;QACvCD,8BAA8B,CAACjB,iBAAiB,GAAGmB;IACrD;IAEA,MAAMC,cAAiChF,UACnC;sBAAC,qBAACA;QAAYC;QAAeC;KAAe,GAC5C;IAEJ,wIAAwI;IACxI,IAAI,CAACgC,WAAW;QACd,OAAO;YACLoB;YACAuB;YACA,wEAAwE;YACxE,sEAAsE;YACtE,wEAAwE;YACxE,uEAAuE;YACvE,oBAAoB;0BACpB;;oBACG1F;oBACAyF,mBAAmBK,QAAQ;;;YAE9BD;SACD;IACH;IAEA,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACE3H,sBAAsB2D,YAAY,IAClC3D,sBAAsB6D,cAAc,EACpC;QACA,OAAO;YACLoC;YACAuB;0BACA;;kCACE,qBAAC9G;wBACCmD,gBAAgB7D,sBAAsB6D,cAAc;wBACpDgE,QAAO;wBACPC,UAAU9H,sBAAsBuE,WAAW;;oBAE5CzC;;;YAEH6F;SACD;IACH;IAEA,MAAMI,oBAAoBC,IAAAA,kCAAiB,EAAChF;IAE5C,0EAA0E;IAC1E,MAAMtE,QAAiC6I;IAEvC,oEAAoE;IACpE,iEAAiE;IACjE,IACEjE,YACA7D,cACA,2GAA2G;IAC3G,6DAA6D;IAC7D,CAAC0G,iBAAiBjB,MAAM,EACxB;QACAxG,MAAMkJ,QAAQ,iBACZ;;8BACE,qBAACK;oBAAKC,MAAK;oBAASC,SAAQ;;gBAC3B5C,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACwC;oBAAKC,MAAK;oBAAaC,SAAQ;;gBAEjC5E;8BACD,qBAACD;;;IAGP;IAEA,yBAAyB;IACzB,IACEiC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAY8B,oBACZ;QACA,6EAA6E;QAC7Ea,QAAQ7G,KAAK,CACX,CAAC,uGAAuG,EAAEN,QAAQ,CAAC;IAEvH;IACAvC,MAAM4G,MAAM,GAAGQ;IAEf,IAAIuC;IACJ,IAAItF,QAAQ;QACV,iDAAiD;QACjD,IAAIgF,mBAAmB;YACrB,wGAAwG;YACxG,oFAAoF;YACpF,6FAA6F;YAC7F,oFAAoF;YACpF,kGAAkG;YAClGrJ,MAAM4J,YAAY,GAAGhI,4BAA4BQ;YACjDuH,+BACE;;oBACG3I;kCACD,qBAACW;wBAAe3B,OAAOA;wBAAOmG,WAAWA;;oBACxC/C;;;QAGP,OAAO;YACL,iGAAiG;YACjG,oDAAoD;YACpDpD,MAAM4J,YAAY,GAAG/H,qCAAqCO;YAC1DuH,+BACE;;oBACG3I;kCACD,qBAACmF;wBAAW,GAAGnG,KAAK;;oBACnBoD;;;QAGP;IACF,OAAO;QACL,2CAA2C;QAC3CuG,+BACE;;gBACGvG;8BACD,qBAAC+C;oBAAW,GAAGnG,KAAK;;;;IAG1B;IAEA,OAAO;QACLuH;QACAuB;sBACA;;gBACGa;gBASA;;;QAEHV;KACD;AACH"}