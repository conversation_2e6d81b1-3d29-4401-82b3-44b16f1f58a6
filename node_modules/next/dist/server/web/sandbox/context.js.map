{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "names": ["clearAllModuleContexts", "clearModuleContext", "getModuleContext", "requestStore", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "__", "moduleContexts", "Map", "pendingModuleCaches", "intervalsManager", "removeAll", "timeouts<PERSON><PERSON><PERSON>", "clear", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "injectedEnvironments", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "processPolyfill", "overriddenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "pick", "BufferImplementation", "EventsImplementation", "AsyncHooksImplementation", "AssertImplementation", "UtilImplementation", "entries", "AsyncLocalStorage", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_log_error__", "err", "onError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "response", "url", "String", "catch", "message", "stack", "__Request", "Request", "constructor", "validateURL", "next", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "performance", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "getModuleContextShared", "deferredModuleContext", "moduleName", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "readFileSync", "runInContext", "filename"], "mappings": ";;;;;;;;;;;;;;;;;IAuDsBA,sBAAsB;eAAtBA;;IAeAC,kBAAkB;eAAlBA;;IA0ZAC,gBAAgB;eAAhBA;;IA/OTC,YAAY;eAAZA;;;6BA3OqB;2BAI3B;6BACqB;oBACiB;uBACjB;sBACP;mCACY;oBACJ;mEACI;mEACA;mEACA;iEACF;wEACM;kCACa;;;;;;AAQlD,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aAAaC,QAAQ;IAC3BN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBACEK,QAAQ,oCAAoCL,mBAAmB;AACnE,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACO,GAAUC,MAAgB;AACnD;AAEA;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAKzB,eAAef;IACpBiB,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IACzBJ,eAAeM,KAAK;IACpBJ,oBAAoBI,KAAK;AAC3B;AAUO,eAAenB,mBAAmBoB,IAAY;IACnDJ,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IAEzB,MAAMI,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIV,eAAgB;QACzCQ,cAAcC,KAAKC,OAAOV;IAC5B;IACA,KAAK,MAAM,CAACS,KAAKC,MAAM,IAAIR,oBAAqB;QAC9CM,cAAcC,KAAK,MAAMC,OAAOR;IAClC;AACF;AAEA,eAAea,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,UAAS,MAAMC,YAAYC,OAAO,CACtC,MAAMC,YAAE,CAACC,QAAQ,CAACL,QAAQM,QAAQ;QAEpCV,OAAO,CAACI,QAAQO,IAAI,CAAC,GAAGN;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASY,8BACPC,oBAA4C;IAE5C,MAAMC,QAAQC,OAAOC,IAAI,CAACzC,QAAQC,GAAG,EAAE2B,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKjB,QAAQC,GAAG,CAACgB,IAAI;SAAC;IAC3E,MAAMhB,MAAMuC,OAAOE,WAAW,CAACH;IAC/B,KAAK,MAAMtB,OAAOuB,OAAOC,IAAI,CAACH,sBAAuB;QACnDrC,GAAG,CAACgB,IAAI,GAAGqB,oBAAoB,CAACrB,IAAI;IACtC;IACAhB,IAAI0C,YAAY,GAAG;IACnB,OAAO1C;AACT;AAEA,SAAS2C,yBAAyBR,IAAY;IAC5C,MAAM/B,QACJ,IAAIwC,MAAM,CAAC,uBAAuB,EAAET,KAAK;8DACiB,CAAC;IAC7DrC,oBAAoBM,OAAOyC,yBAAc,CAACC,UAAU;IACpD,MAAM1C;AACR;AAEA,SAAS2C,sBAAsB/C,GAA2B;IACxD,MAAMgD,kBAAkB;QAAEhD,KAAKoC,8BAA8BpC;IAAK;IAClE,MAAMiD,kBAAuC,CAAC;IAE9C,KAAK,MAAMjC,OAAOuB,OAAOC,IAAI,CAACzC,SAAU;QACtC,IAAIiB,QAAQ,OAAO;QACnBuB,OAAOW,cAAc,CAACF,iBAAiBhC,KAAK;YAC1CmC;gBACE,IAAIF,eAAe,CAACjC,IAAI,KAAKoC,WAAW;oBACtC,OAAOH,eAAe,CAACjC,IAAI;gBAC7B;gBACA,IAAI,OAAO,AAACjB,OAAe,CAACiB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM2B,yBAAyB,CAAC,QAAQ,EAAE3B,IAAI,CAAC;gBACxD;gBACA,OAAOoC;YACT;YACAC,KAAIC,KAAK;gBACPL,eAAe,CAACjC,IAAI,GAAGsC;YACzB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQtC,OAA+B,EAAEiB,IAAY;IAC5DI,OAAOW,cAAc,CAAChC,SAASiB,MAAM;QACnCgB;YACE,OAAO;gBACLR,yBAAyBR;YAC3B;QACF;QACAoB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACxD;QACN,IAAIA,iBAAiBuD,kBAAkB;YACrC7D,oBAAoBM,OAAOyC,yBAAc,CAACC,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C7D,oBAAoBgE,SAASC,MAAM,EAAElB,yBAAc,CAACC,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAeC,IAAAA,UAAI,EAACC,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeD,IAAAA,UAAI,EAACE,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBF,IAAAA,UAAI,EAACG,wBAAwB,EAAE;YACjD;YACA;SACD;QACD,eAAeH,IAAAA,UAAI,EAACI,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaJ,IAAAA,UAAI,EAACK,iBAAkB,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAI/D,IAAI+B,OAAOiC,OAAO,CAACP;AAChC,CAAA;AAEO,MAAMrE,eAAe,IAAI6E,8BAAiB;AAIjD;;;CAGC,GACD,eAAeC,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM,EAAEE,iBAAiB,EAAE,GAAGJ;IAC9B,MAAMpD,OAAO,MAAMD,SAASyD,kBAAkBxD,IAAI,IAAI,EAAE;IACxD,MAAMmC,UAAU,IAAIsB,wBAAW,CAAC;QAC9BC,gBACElF,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEiF,SAAS;YAAM3D,MAAM;QAAK,IAC5B6B;QACN+B,QAAQ,CAACjE;YACPA,QAAQnB,OAAO,GAAGgD,sBAAsBgC,kBAAkB/E,GAAG;YAE7DuC,OAAOW,cAAc,CAAChC,SAAS,WAAW;gBACxCqC,YAAY;gBACZD,OAAO,CAAC8B;oBACN,MAAM9B,QAAQU,gBAAgBb,GAAG,CAACiC;oBAClC,IAAI,CAAC9B,OAAO;wBACV,MAAM+B,UAAU,8BAA8BD;oBAChD;oBACA,OAAO9B;gBACT;YACF;YAEA,IAAIvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCiB,QAAQoE,kBAAkB,GAAG,SAAUC,GAAY;oBACjDZ,QAAQa,OAAO,CAACD;gBAClB;YACF;YAEArE,QAAQuE,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAM1E,MAAM0E,GAAGC,QAAQ;gBACvB,IAAI,CAACf,YAAYxD,GAAG,CAACJ,MAAM;oBACzB,MAAM4E,UAAU/F,eACd,IAAI+C,MACF,CAAC;yEAC0D,CAAC,GAE9DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQzD,IAAI,GAAG;oBACfS,MAAMiD,iBAAiB,CAACD,SAASH;oBACjCb,YAAYkB,GAAG,CAAC9E;oBAChB2D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEAxE,QAAQ8E,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAM1E,MAAM0E,GAAGC,QAAQ;gBACvB,IAAI,CAACb,mBAAmB1D,GAAG,CAACJ,MAAM;oBAChC,MAAM4E,UAAU/F,eACd,IAAI+C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQzD,IAAI,GAAG;oBACfS,MAAMiD,iBAAiB,CAACD,SAASI;oBACjClB,mBAAmBgB,GAAG,CAAC9E;oBACvB2D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEFxE,QAAQ+E,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAMpF,MAAM0E,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACrB,mBAAmB1D,GAAG,CAACJ,MAAM;oBAC1D,MAAM4E,UAAU/F,eACd,IAAI+C,MAAM,CAAC;yEACgD,CAAC,GAC5DC,yBAAc,CAACC,UAAU;oBAE3B8C,QAAQzD,IAAI,GAAG;oBACfS,MAAMiD,iBAAiB,CAACD,SAASK;oBACjCnB,mBAAmBgB,GAAG,CAAC9E;oBACvB2D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUnF,QAAQoF,KAAK;YAC7BpF,QAAQoF,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;gBACrC,MAAMC,eAAe,IAAI7D,MAAM;gBAC/B,MAAM8D,gBAAgB,MAAMC,IAAAA,mCAAgB,EAAC;oBAC3CJ;oBACAK,QAAQjC,QAAQI,iBAAiB,CAAC6B,MAAM;oBACxCC,SAASlC,QAAQkC,OAAO;oBACxB3F;gBACF;gBACA,IAAIwF,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKM,OAAO,GAAG,IAAIC,QAAQP,KAAKM,OAAO,IAAI,CAAC;gBAE5C,IAAI,CAACN,KAAKM,OAAO,CAAC1F,GAAG,CAAC,eAAe;oBACnCoF,KAAKM,OAAO,CAACzD,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM2D,WACJ,OAAOT,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMU,GAAG,EAAE;oBACjB,GAAG/C,IAAAA,UAAI,EAACqC,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPM,SAAS;wBACP,GAAGvE,OAAOE,WAAW,CAAC8D,MAAMO,OAAO,CAAC;wBACpC,GAAGvE,OAAOE,WAAW,CAAC+D,KAAKM,OAAO,CAAC;oBACrC;gBACF,KACAT,QAAQa,OAAOX,QAAQC;gBAE7B,OAAO,MAAMQ,SAASG,KAAK,CAAC,CAAC5B;oBAC3BkB,aAAaW,OAAO,GAAG7B,IAAI6B,OAAO;oBAClC7B,IAAI8B,KAAK,GAAGZ,aAAaY,KAAK;oBAC9B,MAAM9B;gBACR;YACF;YAEA,MAAM+B,YAAYpG,QAAQqG,OAAO;YACjCrG,QAAQqG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYjB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMS,MACJ,OAAOV,UAAU,YAAY,SAASA,QAClCA,MAAMU,GAAG,GACTC,OAAOX;oBACbkB,IAAAA,kBAAW,EAACR;oBACZ,KAAK,CAACA,KAAKT;oBACX,IAAI,CAACkB,IAAI,GAAGlB,wBAAAA,KAAMkB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAazG,QAAQ0G,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAC5G,QAAQ0G,QAAQ;YAClE1G,QAAQ0G,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BN,IAAAA,kBAAW,EAACM,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAM5F,QAAQ6F,qCAA0B,CAAE;gBAC7CxE,QAAQtC,SAASiB;YACnB;YAEAI,OAAO0F,MAAM,CAAC/G,SAASK;YAEvBL,QAAQgH,WAAW,GAAGA;YAEtBhH,QAAQuD,iBAAiB,GAAGA,8BAAiB;YAE7C,+DAA+D;YAC/DvD,QAAQiH,WAAW,GAAG,CAAC,GAAGJ,OACxBrH,kCAAgB,CAACoF,GAAG,CAACiC;YAEvB,+DAA+D;YAC/D7G,QAAQkH,aAAa,GAAG,CAACC,WACvB3H,kCAAgB,CAAC4H,MAAM,CAACD;YAE1B,+DAA+D;YAC/DnH,QAAQqH,UAAU,GAAG,CAAC,GAAGR,OACvBnH,iCAAe,CAACkF,GAAG,CAACiC;YAEtB,+DAA+D;YAC/D7G,QAAQsH,YAAY,GAAG,CAACC,UACtB7H,iCAAe,CAAC0H,MAAM,CAACG;YAEzB,OAAOvH;QACT;IACF;IAEA,MAAMwH,yBAAyBjF,0BAA0BC;IACzDA,QAAQxC,OAAO,CAACyH,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6B/E,8BAA8BH;IACjEA,QAAQxC,OAAO,CAACyH,gBAAgB,CAC9B,sBACAC;IAGF,OAAO;QACLlF;QACAvC,OAAO,IAAIX;QACXoE,aAAa,IAAIC;IACnB;AACF;AAWA,SAASgE,uBAAuBlE,OAA6B;IAC3D,IAAImE,wBAAwBrI,oBAAoB0C,GAAG,CAACwB,QAAQoE,UAAU;IACtE,IAAI,CAACD,uBAAuB;QAC1BA,wBAAwBpE,oBAAoBC;QAC5ClE,oBAAoB4C,GAAG,CAACsB,QAAQoE,UAAU,EAAED;IAC9C;IACA,OAAOA;AACT;AAQO,eAAenJ,iBAAiBgF,OAA6B;IAMlE,IAAIqE;IAIJ,IAAIrE,QAAQsE,QAAQ,EAAE;QACpBD,oBACEzI,eAAe4C,GAAG,CAACwB,QAAQoE,UAAU,KACpC,MAAMF,uBAAuBlE;IAClC;IAEA,IAAI,CAACqE,mBAAmB;QACtBA,oBAAoB,MAAMtE,oBAAoBC;QAC9CpE,eAAe8C,GAAG,CAACsB,QAAQoE,UAAU,EAAEC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAc/H,KAAK,CAACC,GAAG,CAACgI,WAAW;YACtC,MAAMC,UAAUC,IAAAA,gBAAY,EAACF,UAAU;YACvC,IAAI;gBACFG,IAAAA,gBAAY,EAACF,SAASH,cAAcxF,OAAO,CAACxC,OAAO,EAAE;oBACnDsI,UAAUJ;gBACZ;gBACAF,cAAc/H,KAAK,CAACkC,GAAG,CAAC+F,UAAUC;YACpC,EAAE,OAAOjJ,OAAO;gBACd,IAAIuE,QAAQsE,QAAQ,EAAE;oBACpBC,iCAAAA,cAAe/H,KAAK,CAACE,MAAM,CAAC+H;gBAC9B;gBACA,MAAMhJ;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAG8I,aAAa;QAAEC;IAAkB;AAC/C"}