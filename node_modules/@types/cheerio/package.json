{"name": "@types/cheerio", "version": "0.22.35", "description": "TypeScript definitions for cheerio", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cheerio", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "blittle", "url": "https://github.com/blittle"}, {"name": "VILIC VANE", "url": "http://vilic.info"}, {"name": "Wayne Maurer", "githubUsername": "wmaurer", "url": "https://github.com/wmaurer"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/umarniz"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/LiJinyao"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>8", "url": "https://github.com/chennak<PERSON>hna8"}, {"name": "AzSiAz", "githubUsername": "AzSiAz", "url": "https://github.com/AzSiAz"}, {"name": "<PERSON><PERSON>", "githubUsername": "nwtgck", "url": "https://github.com/nwtgck"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "privatenumber", "url": "https://github.com/privatenumber"}, {"name": "Artishevskiy Alexey", "githubUsername": "dhvcc", "url": "https://github.com/dhvcc"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cheerio"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8d8234bb2a274ff4020dfbf4e661d651aa9495a786dba47b35d079b252023cb5", "typeScriptVersion": "4.5"}