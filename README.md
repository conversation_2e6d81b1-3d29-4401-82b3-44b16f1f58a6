# App Icon Generator

A modern Next.js application that generates stunning app icons using Google's AI APIs. This application recreates and enhances the functionality from the original HTML file with improved architecture, design, and user experience.

## Features

- **AI-Powered Icon Generation**: Uses Google's Imagen 4.0 API to generate high-quality app icons from text descriptions
- **URL-to-Icon Generation**: Automatically analyze websites and create icons based on their content, style, and branding
- **AI-Powered URL Analysis**: Simply provide a URL and let Gemini AI analyze the website and generate a complete icon prompt
- **One-Step Icon Generation**: AI analyzes the website content, purpose, and branding to create a detailed icon generation prompt
- **Smart Prompt Creation**: Advanced AI understanding that captures website essence and creates appropriate icon descriptions
- **Editable Prompts**: Review and customize auto-generated prompts before icon creation
- **Prompt Enhancement**: Leverages Google's Gemini 2.5 Flash API to enhance user prompts for better icon generation
- **Dual Input Modes**: Choose between manual text prompts or automatic website analysis
- **Modern UI/UX**: Clean, responsive design with smooth animations and loading states
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **TypeScript**: Full TypeScript support for better development experience
- **Component Architecture**: Well-structured React components following modern best practices

## Prerequisites

- Node.js 18.18.0 or higher (recommended: latest LTS version)
- npm or yarn package manager
- Google AI API key

## Setup Instructions

1. **Clone and navigate to the project**:
   ```bash
   cd icon-generator
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   - Copy `.env.example` to `.env.local`:
     ```bash
     cp .env.example .env.local
     ```
   - Edit `.env.local` and add your configuration:
     ```
     GOOGLE_AI_API_KEY=your_actual_api_key_here
     GEMINI_MODEL=gemini-2.5-flash-preview-05-20
     IMAGEN_MODEL=imagen-4.0-generate-preview-06-06
     ```
   - Get your API key from: https://aistudio.google.com/app/apikey

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Usage

### Text Prompt Mode
1. **Enter a description**: Type a description of the app icon you want to generate
2. **Enhance prompt (optional)**: Click "✨ Enhance Prompt ✨" to let AI improve your description
3. **Generate icon**: Click "Generate Icon" to create your app icon
4. **Download**: Right-click on the generated icon to save it

### URL-to-Icon Mode
1. **Switch to URL mode**: Click the "🌐 From Website" tab
2. **Enter website URL**: Type or paste any website URL (e.g., https://example.com)
3. **Analyze website**: Click "🔍 Analyze Website" to let AI analyze the site and generate an icon prompt
4. **Review AI prompt**: Check the comprehensive AI-generated prompt that captures the website's essence
5. **Edit prompt**: Customize the AI-generated prompt if needed
6. **Generate icon**: Click "✨ Generate Icon from Website" to create an icon based on the analysis
7. **Download**: Right-click on the generated icon to save it

## API Endpoints

- `POST /api/generate-icon`: Generates an icon from a text prompt
- `POST /api/enhance-prompt`: Enhances a user's prompt using AI
- `POST /api/analyze-url-for-icon`: AI-powered endpoint that analyzes a URL and returns a complete icon generation prompt
- `POST /api/analyze-website`: Legacy endpoint for detailed website metadata extraction
- `POST /api/ai-analyze-website`: Direct AI analysis endpoint for website content categorization
- `POST /api/generate-prompt-from-url`: Generates an optimized icon prompt based on website analysis

## Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── generate-icon/route.ts
│   │   ├── enhance-prompt/route.ts
│   │   ├── analyze-website/route.ts
│   │   ├── ai-analyze-website/route.ts
│   │   ├── analyze-url-for-icon/route.ts
│   │   └── generate-prompt-from-url/route.ts
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── IconGenerator.tsx
│   ├── PromptInput.tsx
│   ├── UrlInput.tsx
│   ├── WebsitePreview.tsx
│   ├── GeneratedImage.tsx
│   ├── MessageBox.tsx
│   └── LoadingSpinner.tsx
├── utils/
│   ├── websiteAnalyzer.ts
│   └── promptGenerator.ts
└── types/
    └── index.ts
```

## Technologies Used

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Google AI APIs**: Imagen 4.0 and Gemini 2.5 Flash
- **Web Scraping**: Cheerio and JSDOM for website analysis
- **React Hooks**: Modern state management

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GOOGLE_AI_API_KEY` | Google AI API key for Imagen and Gemini APIs | Yes | - |
| `GEMINI_MODEL` | Gemini model for prompt enhancement | No | `gemini-2.5-flash-preview-05-20` |
| `IMAGEN_MODEL` | Imagen model for icon generation | No | `imagen-4.0-generate-preview-06-06` |

## Development

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run start`: Start production server
- `npm run lint`: Run ESLint

## License

This project is for educational and personal use.
