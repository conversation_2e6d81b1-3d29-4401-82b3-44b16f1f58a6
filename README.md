# App Icon Generator

A modern Next.js application that generates stunning app icons using Google's AI APIs. This application recreates and enhances the functionality from the original HTML file with improved architecture, design, and user experience.

## Features

- **AI-Powered Icon Generation**: Uses Google's Imagen 4.0 API to generate high-quality app icons from text descriptions
- **Prompt Enhancement**: Leverages Google's Gemini 2.0 Flash API to enhance user prompts for better icon generation
- **Modern UI/UX**: Clean, responsive design with smooth animations and loading states
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **TypeScript**: Full TypeScript support for better development experience
- **Component Architecture**: Well-structured React components following modern best practices

## Prerequisites

- Node.js 18.18.0 or higher (recommended: latest LTS version)
- npm or yarn package manager
- Google AI API key

## Setup Instructions

1. **Clone and navigate to the project**:
   ```bash
   cd icon-generator
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   - Copy `.env.example` to `.env.local`:
     ```bash
     cp .env.example .env.local
     ```
   - Edit `.env.local` and add your configuration:
     ```
     GOOGLE_AI_API_KEY=your_actual_api_key_here
     GEMINI_MODEL=gemini-2.5-flash-preview-05-20
     IMAGEN_MODEL=imagen-4.0-generate-preview-06-06
     ```
   - Get your API key from: https://aistudio.google.com/app/apikey

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Usage

1. **Enter a description**: Type a description of the app icon you want to generate
2. **Enhance prompt (optional)**: Click "✨ Enhance Prompt ✨" to let AI improve your description
3. **Generate icon**: Click "Generate Icon" to create your app icon
4. **Download**: Right-click on the generated icon to save it

## API Endpoints

- `POST /api/generate-icon`: Generates an icon from a text prompt
- `POST /api/enhance-prompt`: Enhances a user's prompt using AI

## Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── generate-icon/route.ts
│   │   └── enhance-prompt/route.ts
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── IconGenerator.tsx
│   ├── PromptInput.tsx
│   ├── GeneratedImage.tsx
│   ├── MessageBox.tsx
│   └── LoadingSpinner.tsx
└── types/
    └── index.ts
```

## Technologies Used

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Google AI APIs**: Imagen 4.0 and Gemini 2.0 Flash
- **React Hooks**: Modern state management

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `GOOGLE_AI_API_KEY` | Google AI API key for Imagen and Gemini APIs | Yes | - |
| `GEMINI_MODEL` | Gemini model for prompt enhancement | No | `gemini-2.5-flash-preview-05-20` |
| `IMAGEN_MODEL` | Imagen model for icon generation | No | `imagen-4.0-generate-preview-06-06` |

## Development

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run start`: Start production server
- `npm run lint`: Run ESLint

## License

This project is for educational and personal use.
