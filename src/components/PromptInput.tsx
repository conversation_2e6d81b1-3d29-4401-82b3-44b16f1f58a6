interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export default function PromptInput({ 
  value, 
  onChange, 
  placeholder = "e.g., A cute owl focusing on a book, vibrant colors",
  disabled = false 
}: PromptInputProps) {
  return (
    <div className="w-full">
      <label 
        htmlFor="promptInput" 
        className="block text-gray-700 text-lg font-medium mb-2"
      >
        Describe your app icon:
      </label>
      <input
        id="promptInput"
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed text-black"
      />
    </div>
  );
}
