import { SimpleWebsiteInfo } from '@/types';

interface SimpleWebsitePreviewProps {
  url: string;
  websiteInfo: SimpleWebsiteInfo;
  generatedPrompt: string;
  onPromptChange: (prompt: string) => void;
  onGenerateIcon: () => void;
  isGenerating?: boolean;
}

export default function SimpleWebsitePreview({ 
  url,
  websiteInfo,
  generatedPrompt,
  onPromptChange,
  onGenerateIcon, 
  isGenerating = false 
}: SimpleWebsitePreviewProps) {
  return (
    <div className="bg-gray-50 rounded-2xl p-6 space-y-4">
      {/* Website Info */}
      <div className="flex items-start gap-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800">
            {websiteInfo.siteName || websiteInfo.title || 'Website'}
          </h3>
          <p className="text-sm text-gray-600 break-all">
            {url}
          </p>
          {websiteInfo.description && (
            <p className="text-sm text-gray-600 mt-2 line-clamp-2">
              {websiteInfo.description}
            </p>
          )}
        </div>
      </div>

      {/* AI Analysis Badge and Colors */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <span className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
            🤖 AI Analyzed
          </span>
          <span className="text-xs text-gray-500">
            Prompt generated using AI analysis
          </span>
        </div>

        {/* Extracted Colors */}
        {websiteInfo.extractedColors && websiteInfo.extractedColors.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Extracted Brand Colors:</h4>
            <div className="flex gap-2 flex-wrap">
              {websiteInfo.extractedColors.slice(0, 5).map((color, index) => (
                <div key={index} className="flex items-center gap-1">
                  <div
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                  <span className="text-xs text-gray-600 font-mono">{color}</span>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              These colors were extracted from the website and included in the AI prompt.
            </p>
          </div>
        )}
      </div>

      {/* Generated Prompt Editor */}
      <div className="pt-4 border-t border-gray-200">
        <div className="mb-4">
          <label htmlFor="promptEditor" className="block text-sm font-medium text-gray-700 mb-2">
            AI-Generated Icon Prompt (editable):
          </label>
          <textarea
            id="promptEditor"
            value={generatedPrompt}
            onChange={(e) => onPromptChange(e.target.value)}
            disabled={isGenerating}
            rows={8}
            className="w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm font-mono text-black"
            placeholder="AI-generated prompt will appear here..."
          />
          <p className="text-xs text-gray-500 mt-1">
            The AI has analyzed the website and created this prompt. You can edit it before generating the icon.
          </p>
        </div>

        <button
          onClick={onGenerateIcon}
          disabled={isGenerating || !generatedPrompt.trim()}
          className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isGenerating ? (
            <>
              <div className="border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin" />
              Generating Icon...
            </>
          ) : (
            <>
              ✨ Generate Icon from AI Analysis
            </>
          )}
        </button>
      </div>
    </div>
  );
}
