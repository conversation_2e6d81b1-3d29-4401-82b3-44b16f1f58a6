'use client';

import { useState } from 'react';
import { MessageState, ScrapedWebsiteData } from '@/types';
import PromptInput from './PromptInput';
import UrlInput from './UrlInput';
import WebsitePreview from './WebsitePreview';
import GeneratedImage from './GeneratedImage';
import MessageBox from './MessageBox';

type GenerationMode = 'text' | 'url';

export default function IconGenerator() {
  // Mode switching
  const [mode, setMode] = useState<GenerationMode>('text');

  // Text prompt mode state
  const [prompt, setPrompt] = useState('');

  // URL mode state
  const [url, setUrl] = useState('');
  const [websiteData, setWebsiteData] = useState<ScrapedWebsiteData | null>(null);
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Shared state
  const [imageUrl, setImageUrl] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<MessageState>({
    text: '',
    type: 'info',
    visible: false
  });

  const showMessage = (text: string, type: MessageState['type'] = 'info') => {
    setMessage({ text, type, visible: true });
  };

  const hideMessage = () => {
    setMessage(prev => ({ ...prev, visible: false }));
  };

  const handleModeChange = (newMode: GenerationMode) => {
    setMode(newMode);
    hideMessage();
    setImageUrl(undefined);

    // Clear mode-specific state when switching
    if (newMode === 'text') {
      setUrl('');
      setWebsiteData(null);
      setGeneratedPrompt('');
    } else {
      setPrompt('');
    }
  };

  const handleAnalyzeWebsite = async () => {
    if (!url.trim()) {
      showMessage('Please enter a valid URL.', 'error');
      return;
    }

    hideMessage();
    setIsAnalyzing(true);
    setWebsiteData(null);
    setGeneratedPrompt('');

    try {
      const response = await fetch('/api/analyze-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: url.trim() })
      });

      const data = await response.json();

      if (data.success && data.data) {
        setWebsiteData(data.data);
        setGeneratedPrompt(data.generatedPrompt || '');
        showMessage('Website analyzed successfully! Review the details and prompt below, then generate your icon.', 'success');
      } else {
        showMessage(data.error || 'Failed to analyze website. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error analyzing website:', error);
      showMessage('Failed to analyze website. Please check the URL and try again.', 'error');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGenerateFromWebsite = async () => {
    if (!websiteData || !generatedPrompt.trim()) {
      showMessage('Please analyze a website and ensure the prompt is not empty.', 'error');
      return;
    }

    hideMessage();
    setIsLoading(true);
    setImageUrl(undefined);

    try {
      // Generate the icon using the edited prompt directly
      const iconResponse = await fetch('/api/generate-icon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: generatedPrompt })
      });

      const iconData = await iconResponse.json();

      if (iconData.success && iconData.imageUrl) {
        setImageUrl(iconData.imageUrl);
        showMessage('Icon generated successfully from website analysis!', 'success');
      } else {
        showMessage(iconData.error || 'Failed to generate icon. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error generating icon from website:', error);
      showMessage('Failed to generate icon from website. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnhancePrompt = async () => {
    hideMessage();
    setIsLoading(true);

    try {
      const response = await fetch('/api/enhance-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIdea: prompt })
      });

      const data = await response.json();

      if (data.success && data.enhancedPrompt) {
        setPrompt(data.enhancedPrompt);
        showMessage('Prompt enhanced successfully! Now try generating an icon.', 'success');
      } else {
        showMessage(data.error || 'Could not enhance prompt. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      showMessage('Failed to enhance prompt. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateIcon = async () => {
    if (!prompt.trim()) {
      showMessage('Please enter a description for the app icon.', 'error');
      return;
    }

    hideMessage();
    setIsLoading(true);
    setImageUrl(undefined);

    try {
      const response = await fetch('/api/generate-icon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt })
      });

      const data = await response.json();

      if (data.success && data.imageUrl) {
        setImageUrl(data.imageUrl);
        showMessage('Icon generated successfully!', 'success');
      } else {
        showMessage(data.error || 'Failed to generate icon. Please try again.', 'error');
      }
    } catch (error) {
      console.error('Error generating icon:', error);
      showMessage('Failed to generate icon. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white rounded-3xl shadow-xl p-8 space-y-6">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">App Icon Generator</h1>
          <p className="text-gray-600">Create stunning app icons with AI</p>
        </div>

        {/* Mode Tabs */}
        <div className="flex bg-gray-100 rounded-xl p-1">
          <button
            onClick={() => handleModeChange('text')}
            className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
              mode === 'text'
                ? 'bg-white text-indigo-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            📝 Text Prompt
          </button>
          <button
            onClick={() => handleModeChange('url')}
            className={`flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
              mode === 'url'
                ? 'bg-white text-indigo-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            🌐 From Website
          </button>
        </div>

        {/* Content based on selected mode */}
        {mode === 'text' ? (
          <>
            <PromptInput
              value={prompt}
              onChange={setPrompt}
              disabled={isLoading}
            />

            <div className="flex gap-4 flex-col sm:flex-row">
              <button
                onClick={handleEnhancePrompt}
                disabled={isLoading}
                className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]"
              >
                ✨ Enhance Prompt ✨
              </button>
              <button
                onClick={handleGenerateIcon}
                disabled={isLoading}
                className="flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]"
              >
                Generate Icon
              </button>
            </div>
          </>
        ) : (
          <>
            <UrlInput
              value={url}
              onChange={setUrl}
              onAnalyze={handleAnalyzeWebsite}
              disabled={isLoading || isAnalyzing}
              isAnalyzing={isAnalyzing}
            />

            {websiteData && (
              <WebsitePreview
                websiteData={websiteData}
                generatedPrompt={generatedPrompt}
                onPromptChange={setGeneratedPrompt}
                onGenerateIcon={handleGenerateFromWebsite}
                isGenerating={isLoading}
              />
            )}
          </>
        )}

        <GeneratedImage
          imageUrl={imageUrl}
          isLoading={isLoading}
        />

        <MessageBox message={message} />
      </div>
    </div>
  );
}
