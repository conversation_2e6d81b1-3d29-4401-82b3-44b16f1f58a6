import { ScrapedWebsiteData } from '@/types';

interface WebsitePreviewProps {
  websiteData: ScrapedWebsiteData;
  onGenerateIcon: () => void;
  isGenerating?: boolean;
}

export default function WebsitePreview({ 
  websiteData, 
  onGenerateIcon, 
  isGenerating = false 
}: WebsitePreviewProps) {
  const formatCategory = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  const formatStyle = (style: string) => {
    return style.charAt(0).toUpperCase() + style.slice(1);
  };

  return (
    <div className="bg-gray-50 rounded-2xl p-6 space-y-4">
      <div className="flex items-start gap-4">
        {websiteData.favicon && (
          <img 
            src={websiteData.favicon} 
            alt="Website favicon"
            className="w-8 h-8 rounded"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        )}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800">
            {websiteData.siteName || websiteData.title || 'Website'}
          </h3>
          <p className="text-sm text-gray-600 break-all">
            {websiteData.url}
          </p>
        </div>
      </div>

      {websiteData.description && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-1">Description:</h4>
          <p className="text-sm text-gray-600 line-clamp-3">
            {websiteData.description}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-1">Category:</h4>
          <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
            {formatCategory(websiteData.category || 'other')}
          </span>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-1">Style:</h4>
          <span className="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
            {formatStyle(websiteData.visualStyle || 'modern')}
          </span>
        </div>
      </div>

      {websiteData.primaryColors && websiteData.primaryColors.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Primary Colors:</h4>
          <div className="flex gap-2 flex-wrap">
            {websiteData.primaryColors.slice(0, 5).map((color, index) => (
              <div key={index} className="flex items-center gap-1">
                <div 
                  className="w-4 h-4 rounded border border-gray-300"
                  style={{ backgroundColor: color }}
                  title={color}
                />
                <span className="text-xs text-gray-600">{color}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {websiteData.keywords && websiteData.keywords.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Keywords:</h4>
          <div className="flex gap-1 flex-wrap">
            {websiteData.keywords.slice(0, 8).map((keyword, index) => (
              <span 
                key={index}
                className="inline-block bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded"
              >
                {keyword}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onGenerateIcon}
          disabled={isGenerating}
          className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isGenerating ? (
            <>
              <div className="border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin" />
              Generating Icon...
            </>
          ) : (
            <>
              ✨ Generate Icon from Website
            </>
          )}
        </button>
      </div>
    </div>
  );
}
