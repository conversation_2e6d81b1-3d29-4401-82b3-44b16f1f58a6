interface UrlInputProps {
  value: string;
  onChange: (value: string) => void;
  onAnalyze: () => void;
  placeholder?: string;
  disabled?: boolean;
  isAnalyzing?: boolean;
}

export default function UrlInput({ 
  value, 
  onChange, 
  onAnalyze,
  placeholder = "e.g., https://example.com or example.com",
  disabled = false,
  isAnalyzing = false
}: UrlInputProps) {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !disabled && !isAnalyzing && value.trim()) {
      onAnalyze();
    }
  };

  return (
    <div className="w-full space-y-4">
      <div>
        <label 
          htmlFor="urlInput" 
          className="block text-gray-700 text-lg font-medium mb-2"
        >
          Enter website URL:
        </label>
        <input
          id="urlInput"
          type="url"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed text-black"
        />
      </div>
      
      <button
        onClick={onAnalyze}
        disabled={disabled || isAnalyzing || !value.trim()}
        className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      >
        {isAnalyzing ? (
          <>
            <div className="border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin" />
            Analyzing Website...
          </>
        ) : (
          <>
            🔍 Analyze Website
          </>
        )}
      </button>
    </div>
  );
}
