import * as cheerio from 'cheerio';
import { JSDOM } from 'jsdom';
import { ScrapedWebsiteData } from '@/types';

/**
 * Validates if a URL is properly formatted and accessible
 */
export function validateUrl(url: string): { isValid: boolean; normalizedUrl?: string; error?: string } {
  try {
    // Add protocol if missing
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    const urlObj = new URL(normalizedUrl);
    
    // Basic validation
    if (!urlObj.hostname || urlObj.hostname.length < 3) {
      return { isValid: false, error: 'Invalid hostname' };
    }

    return { isValid: true, normalizedUrl };
  } catch (error) {
    return { isValid: false, error: 'Invalid URL format' };
  }
}

/**
 * Extracts metadata from HTML content using Cheerio
 */
export function extractMetadata(html: string, url: string): Partial<ScrapedWebsiteData> {
  const $ = cheerio.load(html);
  
  // Extract basic metadata
  const title = $('title').text().trim() || 
                $('meta[property="og:title"]').attr('content') || 
                $('meta[name="twitter:title"]').attr('content') || '';

  const description = $('meta[name="description"]').attr('content') || 
                     $('meta[property="og:description"]').attr('content') || 
                     $('meta[name="twitter:description"]').attr('content') || '';

  const siteName = $('meta[property="og:site_name"]').attr('content') || '';

  const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

  const favicon = $('link[rel="icon"]').attr('href') || 
                 $('link[rel="shortcut icon"]').attr('href') || 
                 $('link[rel="apple-touch-icon"]').attr('href') || '';

  const ogImage = $('meta[property="og:image"]').attr('content') || 
                 $('meta[name="twitter:image"]').attr('content') || '';

  return {
    url,
    title,
    description,
    siteName,
    keywords,
    favicon: favicon ? new URL(favicon, url).href : undefined,
    ogImage: ogImage ? new URL(ogImage, url).href : undefined,
  };
}

/**
 * Analyzes website content to determine category and style
 */
export function analyzeWebsiteContent(html: string, metadata: Partial<ScrapedWebsiteData>): {
  category: ScrapedWebsiteData['contentType'];
  visualStyle: ScrapedWebsiteData['visualStyle'];
} {
  const $ = cheerio.load(html);
  const text = $('body').text().toLowerCase();
  const title = metadata.title?.toLowerCase() || '';
  const description = metadata.description?.toLowerCase() || '';
  const allText = `${title} ${description} ${text}`.toLowerCase();

  // Determine content category
  let category: ScrapedWebsiteData['contentType'] = 'other';
  
  if (allText.includes('shop') || allText.includes('buy') || allText.includes('cart') || 
      allText.includes('product') || allText.includes('store') || allText.includes('ecommerce')) {
    category = 'ecommerce';
  } else if (allText.includes('blog') || allText.includes('article') || allText.includes('post') ||
             $('article').length > 0 || $('.blog').length > 0) {
    category = 'blog';
  } else if (allText.includes('portfolio') || allText.includes('work') || allText.includes('project') ||
             $('.portfolio').length > 0 || $('.gallery').length > 0) {
    category = 'portfolio';
  } else if (allText.includes('saas') || allText.includes('software') || allText.includes('app') ||
             allText.includes('platform') || allText.includes('dashboard')) {
    category = 'saas';
  } else if (allText.includes('company') || allText.includes('business') || allText.includes('corporate') ||
             allText.includes('enterprise') || allText.includes('about us')) {
    category = 'corporate';
  } else if (allText.includes('design') || allText.includes('creative') || allText.includes('art') ||
             allText.includes('studio') || allText.includes('agency')) {
    category = 'creative';
  } else if (allText.includes('learn') || allText.includes('course') || allText.includes('education') ||
             allText.includes('tutorial') || allText.includes('school')) {
    category = 'educational';
  }

  // Determine visual style based on CSS classes and content
  let visualStyle: ScrapedWebsiteData['visualStyle'] = 'modern';
  
  const hasMinimalClasses = $('.minimal, .clean, .simple').length > 0;
  const hasBoldClasses = $('.bold, .strong, .vibrant').length > 0;
  const hasElegantClasses = $('.elegant, .luxury, .premium').length > 0;
  const hasPlayfulClasses = $('.fun, .playful, .colorful').length > 0;
  const hasCorporateClasses = $('.corporate, .professional, .business').length > 0;

  if (hasMinimalClasses || allText.includes('minimal') || allText.includes('clean')) {
    visualStyle = 'minimalist';
  } else if (hasBoldClasses || allText.includes('bold') || allText.includes('vibrant')) {
    visualStyle = 'bold';
  } else if (hasElegantClasses || allText.includes('elegant') || allText.includes('luxury')) {
    visualStyle = 'elegant';
  } else if (hasPlayfulClasses || allText.includes('fun') || allText.includes('playful')) {
    visualStyle = 'playful';
  } else if (hasCorporateClasses || category === 'corporate') {
    visualStyle = 'corporate';
  } else if (allText.includes('classic') || allText.includes('traditional')) {
    visualStyle = 'classic';
  }

  return { category, visualStyle };
}

/**
 * Extracts primary colors from CSS (simplified approach)
 */
export function extractColors(html: string): string[] {
  const $ = cheerio.load(html);
  const colors: string[] = [];
  
  // Extract colors from inline styles and CSS
  $('*').each((_, element) => {
    const style = $(element).attr('style');
    if (style) {
      const colorMatches = style.match(/(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\([^)]+\)|rgba\([^)]+\))/g);
      if (colorMatches) {
        colors.push(...colorMatches);
      }
    }
  });

  // Extract from style tags
  $('style').each((_, element) => {
    const css = $(element).html() || '';
    const colorMatches = css.match(/(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\([^)]+\)|rgba\([^)]+\))/g);
    if (colorMatches) {
      colors.push(...colorMatches);
    }
  });

  // Remove duplicates and filter out common colors
  const uniqueColors = [...new Set(colors)]
    .filter(color => !['#000000', '#ffffff', '#000', '#fff', 'rgb(0,0,0)', 'rgb(255,255,255)'].includes(color.toLowerCase()))
    .slice(0, 5); // Limit to 5 colors

  return uniqueColors;
}

/**
 * Main function to scrape and analyze a website
 */
export async function scrapeWebsite(url: string): Promise<ScrapedWebsiteData> {
  const validation = validateUrl(url);
  if (!validation.isValid) {
    throw new Error(validation.error || 'Invalid URL');
  }

  const normalizedUrl = validation.normalizedUrl!;

  try {
    // Fetch the website content
    const response = await fetch(normalizedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    
    // Extract metadata
    const metadata = extractMetadata(html, normalizedUrl);
    
    // Analyze content
    const { category, visualStyle } = analyzeWebsiteContent(html, metadata);
    
    // Extract colors
    const primaryColors = extractColors(html);

    return {
      url: normalizedUrl,
      title: metadata.title,
      description: metadata.description,
      siteName: metadata.siteName,
      keywords: metadata.keywords,
      primaryColors,
      category,
      visualStyle,
      favicon: metadata.favicon,
      ogImage: metadata.ogImage,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to scrape website: ${error.message}`);
    }
    throw new Error('Failed to scrape website: Unknown error');
  }
}
