import * as cheerio from 'cheerio';
import { ScrapedWebsiteData } from '@/types';

/**
 * Validates if a URL is properly formatted and accessible
 */
export function validateUrl(url: string): { isValid: boolean; normalizedUrl?: string; error?: string } {
  try {
    // Add protocol if missing
    let normalizedUrl = url.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = 'https://' + normalizedUrl;
    }

    const urlObj = new URL(normalizedUrl);
    
    // Basic validation
    if (!urlObj.hostname || urlObj.hostname.length < 3) {
      return { isValid: false, error: 'Invalid hostname' };
    }

    return { isValid: true, normalizedUrl };
  } catch (error) {
    return { isValid: false, error: 'Invalid URL format' };
  }
}

/**
 * Extracts metadata from HTML content using Cheerio
 */
export function extractMetadata(html: string, url: string): Partial<ScrapedWebsiteData> {
  const $ = cheerio.load(html);
  
  // Extract basic metadata
  const title = $('title').text().trim() || 
                $('meta[property="og:title"]').attr('content') || 
                $('meta[name="twitter:title"]').attr('content') || '';

  const description = $('meta[name="description"]').attr('content') || 
                     $('meta[property="og:description"]').attr('content') || 
                     $('meta[name="twitter:description"]').attr('content') || '';

  const siteName = $('meta[property="og:site_name"]').attr('content') || '';

  const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];

  const favicon = $('link[rel="icon"]').attr('href') || 
                 $('link[rel="shortcut icon"]').attr('href') || 
                 $('link[rel="apple-touch-icon"]').attr('href') || '';

  const ogImage = $('meta[property="og:image"]').attr('content') || 
                 $('meta[name="twitter:image"]').attr('content') || '';

  return {
    url,
    title,
    description,
    siteName,
    keywords,
    favicon: favicon ? new URL(favicon, url).href : undefined,
    ogImage: ogImage ? new URL(ogImage, url).href : undefined,
  };
}

/**
 * Performs AI-powered analysis of website content using Gemini directly
 */
async function performAIAnalysis(html: string, metadata: Partial<ScrapedWebsiteData>): Promise<ScrapedWebsiteData['aiAnalysis']> {
  try {
    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const geminiModel = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';

    if (!apiKey) {
      console.warn('Google AI API key not configured, skipping AI analysis');
      return undefined;
    }

    // Extract text content from HTML (simplified)
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .slice(0, 8000); // Limit text for API efficiency

    const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.

Website Information:
- URL: ${metadata.url || 'Not provided'}
- Title: ${metadata.title || 'Not provided'}
- Description: ${metadata.description || 'Not provided'}
- Site Name: ${metadata.siteName || 'Not provided'}
- Keywords: ${metadata.keywords?.join(', ') || 'Not provided'}

Website Content (first 8000 characters):
${textContent}

Please analyze this website and respond with a JSON object containing the following fields:

{
  "category": "one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other",
  "visualStyle": "one of: modern, minimalist, corporate, playful, elegant, bold, classic",
  "brandPersonality": "brief description of the brand's personality and tone",
  "targetAudience": "description of the primary target audience",
  "primaryPurpose": "main purpose or function of the website",
  "keyFeatures": ["array", "of", "key", "features", "or", "services"],
  "industryContext": "industry or sector this website operates in",
  "designCharacteristics": ["array", "of", "visual", "design", "characteristics"]
}

Base your analysis on:
1. The actual content and purpose of the website
2. The language, tone, and messaging used
3. The apparent target audience and use cases
4. The industry context and competitive landscape
5. Visual and functional characteristics that would inform icon design

Respond ONLY with the JSON object, no additional text.`;

    const chatHistory = [{
      role: "user",
      parts: [{ text: analysisPrompt }]
    }];

    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      console.warn('Gemini API request failed:', response.status, response.statusText);
      return undefined;
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {

      let analysisText = result.candidates[0].content.parts[0].text.trim();

      // Clean up the response to extract JSON
      analysisText = analysisText.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();

      try {
        const analysis = JSON.parse(analysisText);

        // Validate the response structure
        if (!analysis.category || !analysis.visualStyle) {
          console.warn('Invalid AI analysis structure received');
          return undefined;
        }

        return analysis;
      } catch (parseError) {
        console.warn('Failed to parse AI analysis JSON:', parseError);
        return undefined;
      }
    } else {
      console.warn('No valid response from Gemini API');
      return undefined;
    }
  } catch (error) {
    console.warn('AI analysis error:', error);
    return undefined;
  }
}

/**
 * Basic fallback analysis when AI analysis fails
 */
function basicFallbackAnalysis(html: string, metadata: Partial<ScrapedWebsiteData>): {
  category: ScrapedWebsiteData['contentType'];
  visualStyle: ScrapedWebsiteData['visualStyle'];
} {
  const $ = cheerio.load(html);
  const text = $('body').text().toLowerCase();
  const title = metadata.title?.toLowerCase() || '';
  const description = metadata.description?.toLowerCase() || '';
  const allText = `${title} ${description} ${text}`.toLowerCase();

  // Basic category determination
  let category: ScrapedWebsiteData['contentType'] = 'other';

  if (allText.includes('shop') || allText.includes('buy') || allText.includes('cart')) {
    category = 'ecommerce';
  } else if (allText.includes('blog') || allText.includes('article')) {
    category = 'blog';
  } else if (allText.includes('portfolio') || allText.includes('work')) {
    category = 'portfolio';
  } else if (allText.includes('saas') || allText.includes('software')) {
    category = 'saas';
  } else if (allText.includes('company') || allText.includes('business')) {
    category = 'corporate';
  }

  // Basic style determination
  let visualStyle: ScrapedWebsiteData['visualStyle'] = 'modern';

  if (allText.includes('minimal') || allText.includes('clean')) {
    visualStyle = 'minimalist';
  } else if (allText.includes('corporate') || allText.includes('professional')) {
    visualStyle = 'corporate';
  }

  return { category, visualStyle };
}

/**
 * Extracts primary colors from CSS with improved detection
 */
export function extractColors(html: string): string[] {
  const $ = cheerio.load(html);
  const colorCounts: { [color: string]: number } = {};

  // Color regex for better matching
  const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)|rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\))/gi;

  // Extract colors from inline styles with priority weighting
  $('*').each((_, element) => {
    const style = $(element).attr('style');
    if (style) {
      const colorMatches = style.match(colorRegex);
      if (colorMatches) {
        colorMatches.forEach(color => {
          const normalized = normalizeColor(color);
          if (normalized && !isCommonColor(normalized)) {
            // Give higher weight to background colors and brand elements
            const weight = style.includes('background') ? 3 :
                          style.includes('color') ? 2 : 1;
            colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;
          }
        });
      }
    }

    // Check for brand-related elements (headers, logos, buttons)
    const tagName = $(element).prop('tagName')?.toLowerCase();
    const className = $(element).attr('class') || '';
    const id = $(element).attr('id') || '';

    if (tagName === 'header' || tagName === 'nav' ||
        className.includes('logo') || className.includes('brand') ||
        className.includes('primary') || className.includes('accent') ||
        id.includes('logo') || id.includes('brand')) {

      const computedStyle = $(element).attr('style');
      if (computedStyle) {
        const brandColors = computedStyle.match(colorRegex);
        if (brandColors) {
          brandColors.forEach(color => {
            const normalized = normalizeColor(color);
            if (normalized && !isCommonColor(normalized)) {
              colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors
            }
          });
        }
      }
    }
  });

  // Extract from style tags and CSS
  $('style, link[rel="stylesheet"]').each((_, element) => {
    const css = $(element).html() || '';
    const colorMatches = css.match(colorRegex);
    if (colorMatches) {
      colorMatches.forEach(color => {
        const normalized = normalizeColor(color);
        if (normalized && !isCommonColor(normalized)) {
          // Check if it's in important CSS rules
          const weight = css.includes(':root') || css.includes('--') ? 4 : // CSS variables
                        css.includes('.primary') || css.includes('.brand') ? 3 : // Brand classes
                        css.includes('background') ? 2 : 1;
          colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;
        }
      });
    }
  });

  // Sort by frequency/importance and return top colors
  const sortedColors = Object.entries(colorCounts)
    .sort(([,a], [,b]) => b - a)
    .map(([color]) => color)
    .slice(0, 5);

  return sortedColors;
}

/**
 * Normalizes color format to hex
 */
function normalizeColor(color: string): string | null {
  const trimmed = color.trim().toLowerCase();

  // Already hex
  if (trimmed.match(/^#[0-9a-f]{6}$/)) {
    return trimmed;
  }

  // 3-digit hex
  if (trimmed.match(/^#[0-9a-f]{3}$/)) {
    return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;
  }

  // RGB
  const rgbMatch = trimmed.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
  if (rgbMatch) {
    const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0');
    const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0');
    const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  }

  // RGBA (ignore alpha for now)
  const rgbaMatch = trimmed.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)/);
  if (rgbaMatch) {
    const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, '0');
    const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, '0');
    const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  }

  return null;
}

/**
 * Checks if a color is too common/generic to be useful
 */
function isCommonColor(color: string): boolean {
  const commonColors = [
    '#000000', '#ffffff', '#000', '#fff',
    '#f0f0f0', '#e0e0e0', '#d0d0d0', '#c0c0c0',
    '#808080', '#404040', '#202020',
    '#f8f8f8', '#f5f5f5', '#eeeeee', '#dddddd'
  ];

  return commonColors.includes(color.toLowerCase());
}

/**
 * Main function to scrape and analyze a website
 */
export async function scrapeWebsite(url: string): Promise<ScrapedWebsiteData> {
  const validation = validateUrl(url);
  if (!validation.isValid) {
    throw new Error(validation.error || 'Invalid URL');
  }

  const normalizedUrl = validation.normalizedUrl!;

  try {
    // Fetch the website content
    const response = await fetch(normalizedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)',
      },
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    
    // Extract metadata
    const metadata = extractMetadata(html, normalizedUrl);

    // Perform AI-powered analysis
    const aiAnalysis = await performAIAnalysis(html, metadata);

    // Extract colors
    const primaryColors = extractColors(html);

    // Use AI analysis if available, otherwise fall back to basic analysis
    let category: ScrapedWebsiteData['contentType'] = 'other';
    let visualStyle: ScrapedWebsiteData['visualStyle'] = 'modern';

    if (aiAnalysis) {
      category = aiAnalysis.category;
      visualStyle = aiAnalysis.visualStyle;
    } else {
      // Fallback to basic analysis
      const fallback = basicFallbackAnalysis(html, metadata);
      category = fallback.category;
      visualStyle = fallback.visualStyle;
    }

    return {
      url: normalizedUrl,
      title: metadata.title,
      description: metadata.description,
      siteName: metadata.siteName,
      keywords: metadata.keywords,
      primaryColors,
      category,
      visualStyle,
      favicon: metadata.favicon,
      ogImage: metadata.ogImage,
      aiAnalysis,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to scrape website: ${error.message}`);
    }
    throw new Error('Failed to scrape website: Unknown error');
  }
}
