import { ScrapedWebsiteData } from '@/types';

/**
 * Maps content categories to icon style descriptions
 */
const CATEGORY_STYLES = {
  ecommerce: {
    elements: ['shopping bag', 'cart', 'storefront', 'package', 'credit card'],
    style: 'commercial and trustworthy',
    mood: 'professional yet approachable'
  },
  blog: {
    elements: ['pen', 'notebook', 'speech bubble', 'document', 'quill'],
    style: 'editorial and readable',
    mood: 'informative and engaging'
  },
  portfolio: {
    elements: ['easel', 'brush', 'camera', 'frame', 'gallery'],
    style: 'creative and artistic',
    mood: 'inspiring and professional'
  },
  saas: {
    elements: ['cloud', 'dashboard', 'graph', 'network', 'gear'],
    style: 'tech-forward and modern',
    mood: 'innovative and reliable'
  },
  corporate: {
    elements: ['building', 'handshake', 'briefcase', 'chart', 'globe'],
    style: 'professional and established',
    mood: 'trustworthy and authoritative'
  },
  creative: {
    elements: ['palette', 'lightbulb', 'star', 'magic wand', 'rainbow'],
    style: 'artistic and expressive',
    mood: 'imaginative and vibrant'
  },
  educational: {
    elements: ['book', 'graduation cap', 'apple', 'chalkboard', 'lightbulb'],
    style: 'educational and clear',
    mood: 'knowledgeable and approachable'
  },
  other: {
    elements: ['circle', 'square', 'diamond', 'star', 'hexagon'],
    style: 'versatile and clean',
    mood: 'neutral and professional'
  }
};

/**
 * Maps visual styles to design descriptions
 */
const VISUAL_STYLE_DESCRIPTIONS = {
  modern: 'sleek, contemporary design with clean lines and subtle gradients',
  minimalist: 'ultra-clean, simple design with plenty of white space and minimal elements',
  corporate: 'professional, business-like appearance with structured layout and conservative colors',
  playful: 'fun, energetic design with rounded corners and vibrant, cheerful elements',
  elegant: 'sophisticated, refined design with premium feel and subtle luxury touches',
  bold: 'strong, impactful design with high contrast and dynamic visual elements',
  classic: 'timeless, traditional design with balanced proportions and established conventions'
};

/**
 * Converts hex/rgb colors to descriptive color names with better analysis
 */
function colorToDescription(color: string): string {
  // Normalize the color
  let normalizedColor = color.toLowerCase().trim();

  // Handle rgb/rgba colors
  if (normalizedColor.startsWith('rgb')) {
    const rgbMatch = normalizedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
      const r = parseInt(rgbMatch[1]);
      const g = parseInt(rgbMatch[2]);
      const b = parseInt(rgbMatch[3]);
      normalizedColor = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
  }

  // Handle 3-digit hex colors
  if (normalizedColor.match(/^#[0-9a-f]{3}$/)) {
    normalizedColor = `#${normalizedColor[1]}${normalizedColor[1]}${normalizedColor[2]}${normalizedColor[2]}${normalizedColor[3]}${normalizedColor[3]}`;
  }

  // Extract RGB values for analysis
  const hexMatch = normalizedColor.match(/^#([0-9a-f]{6})$/);
  if (hexMatch) {
    const r = parseInt(hexMatch[1].slice(0, 2), 16);
    const g = parseInt(hexMatch[1].slice(2, 4), 16);
    const b = parseInt(hexMatch[1].slice(4, 6), 16);

    // Calculate HSL for better color description
    const max = Math.max(r, g, b) / 255;
    const min = Math.min(r, g, b) / 255;
    const diff = max - min;
    const lightness = (max + min) / 2;
    const saturation = diff === 0 ? 0 : diff / (1 - Math.abs(2 * lightness - 1));

    // Determine hue
    let hue = 0;
    if (diff !== 0) {
      if (max === r / 255) hue = ((g - b) / 255 / diff) % 6;
      else if (max === g / 255) hue = (b - r) / 255 / diff + 2;
      else hue = (r - g) / 255 / diff + 4;
    }
    hue = Math.round(hue * 60);
    if (hue < 0) hue += 360;

    // Generate descriptive name based on HSL
    const satDesc = saturation > 0.7 ? 'vibrant' : saturation > 0.4 ? 'rich' : 'muted';
    const lightDesc = lightness > 0.8 ? 'light' : lightness > 0.6 ? 'bright' : lightness > 0.4 ? 'medium' : 'dark';

    let colorName = '';
    if (hue >= 0 && hue < 15) colorName = 'red';
    else if (hue < 45) colorName = 'orange';
    else if (hue < 75) colorName = 'yellow';
    else if (hue < 105) colorName = 'yellow-green';
    else if (hue < 135) colorName = 'green';
    else if (hue < 165) colorName = 'teal';
    else if (hue < 195) colorName = 'cyan';
    else if (hue < 225) colorName = 'blue';
    else if (hue < 255) colorName = 'blue-purple';
    else if (hue < 285) colorName = 'purple';
    else if (hue < 315) colorName = 'magenta';
    else if (hue < 345) colorName = 'pink';
    else colorName = 'red';

    // Handle grayscale
    if (saturation < 0.1) {
      if (lightness > 0.9) return 'white';
      if (lightness < 0.1) return 'black';
      return `${lightDesc} gray`;
    }

    return `${satDesc} ${lightDesc} ${colorName}`;
  }

  // Fallback for unrecognized formats
  return normalizedColor;
}

/**
 * Generates a detailed prompt for icon generation based on website data
 */
export function generateIconPrompt(websiteData: ScrapedWebsiteData): string {
  const category = websiteData.category || 'other';
  const visualStyle = websiteData.visualStyle || 'modern';
  const categoryInfo = CATEGORY_STYLES[category as keyof typeof CATEGORY_STYLES];
  const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle as keyof typeof VISUAL_STYLE_DESCRIPTIONS];

  // Build the prompt components
  const components = [];

  // 1. Basic description
  const siteName = websiteData.siteName || websiteData.title || 'website';
  components.push(`Create a professional app icon for "${siteName}"`);

  // 2. Category-specific elements
  const suggestedElement = categoryInfo.elements[Math.floor(Math.random() * categoryInfo.elements.length)];
  components.push(`incorporating a ${suggestedElement} as the main element`);

  // 3. Visual style
  components.push(`designed in a ${visualStyle} style with ${styleDescription}`);

  // 4. Color scheme
  if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {
    const colorDescriptions = websiteData.primaryColors
      .slice(0, 3) // Use up to 3 colors
      .map(colorToDescription)
      .join(', ');
    components.push(`using a color palette featuring ${colorDescriptions}`);
  } else {
    // Default color suggestions based on category
    const defaultColors = {
      ecommerce: 'blue and green tones for trust and growth',
      blog: 'warm orange and blue tones for readability',
      portfolio: 'creative purple and teal tones',
      saas: 'modern blue and gray tones',
      corporate: 'professional navy and silver tones',
      creative: 'vibrant rainbow or artistic color combinations',
      educational: 'friendly blue and yellow tones',
      other: 'balanced blue and gray tones'
    };
    components.push(`using ${defaultColors[category as keyof typeof defaultColors]}`);
  }

  // 5. Technical specifications
  components.push('The icon should be suitable for app stores with a square format');
  components.push('featuring clear, recognizable imagery that works well at small sizes');
  components.push(`conveying a ${categoryInfo.mood} feeling`);

  // 6. Additional context from website content
  if (websiteData.description) {
    const shortDesc = websiteData.description.slice(0, 100);
    components.push(`The website is described as: "${shortDesc}"`);
  }

  // 7. Style refinements
  components.push('Ensure the design is scalable, memorable, and stands out among other app icons');
  components.push(`with a ${categoryInfo.style} aesthetic`);

  return components.join(', ') + '.';
}

/**
 * Creates a structured prompt for the AI to generate an even better icon prompt
 */
export function createPromptEnhancementRequest(websiteData: ScrapedWebsiteData): string {
  const basicPrompt = generateIconPrompt(websiteData);
  
  return `Based on the following website analysis, create an enhanced, detailed prompt for generating a perfect app icon:

Website Information:
- Name: ${websiteData.siteName || websiteData.title || 'Unknown'}
- URL: ${websiteData.url}
- Category: ${websiteData.category}
- Visual Style: ${websiteData.visualStyle}
- Description: ${websiteData.description || 'No description available'}
- Primary Colors: ${websiteData.primaryColors?.join(', ') || 'Not detected'}

Initial Prompt: ${basicPrompt}

Please enhance this prompt to create a more detailed, creative, and specific description for an AI image generator. Focus on:
1. Specific visual elements that represent the website's purpose
2. Appropriate color schemes and styling
3. Professional app icon design principles
4. Unique characteristics that make it memorable
5. Technical requirements for app store submission

Enhanced Prompt:`;
}

/**
 * Validates and cleans generated prompts
 */
export function validatePrompt(prompt: string): { isValid: boolean; cleanedPrompt?: string; issues?: string[] } {
  const issues: string[] = [];
  let cleanedPrompt = prompt.trim();

  // Check minimum length
  if (cleanedPrompt.length < 20) {
    issues.push('Prompt is too short');
  }

  // Check maximum length (most AI models have token limits)
  if (cleanedPrompt.length > 20000) {
    issues.push('Prompt is too long');
    cleanedPrompt = cleanedPrompt.slice(0, 2000) + '...';
  }

  // Remove potentially problematic content
  const problematicTerms = ['nsfw', 'explicit', 'violent', 'inappropriate'];
  const lowerPrompt = cleanedPrompt.toLowerCase();
  
  for (const term of problematicTerms) {
    if (lowerPrompt.includes(term)) {
      issues.push(`Contains potentially inappropriate content: ${term}`);
    }
  }

  // Ensure it mentions "icon" or "app icon"
  if (!lowerPrompt.includes('icon') && !lowerPrompt.includes('logo')) {
    cleanedPrompt = 'App icon: ' + cleanedPrompt;
  }

  return {
    isValid: issues.length === 0,
    cleanedPrompt,
    issues: issues.length > 0 ? issues : undefined
  };
}
