import { NextRequest, NextResponse } from 'next/server';
import { GenerateIconRequest, GenerateIconResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: GenerateIconRequest = await request.json();
    const { prompt } = body;

    if (!prompt || prompt.trim() === '') {
      return NextResponse.json<GenerateIconResponse>(
        { success: false, error: 'Please enter a description for the app icon.' },
        { status: 400 }
      );
    }

    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const imagenModel = process.env.IMAGEN_MODEL || 'imagen-4.0-generate-preview-06-06';

    if (!apiKey) {
      return NextResponse.json<GenerateIconResponse>(
        { success: false, error: 'API key not configured. Please set GOOGLE_AI_API_KEY environment variable.' },
        { status: 500 }
      );
    }

    // Construct the payload for the API call
    const payload = {
      instances: { prompt: prompt.trim() },
      parameters: { sampleCount: 1 }
    };

    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${imagenModel}:predict?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Image generation failed.');
    }

    const result = await response.json();

    if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {
      const imageUrl = `data:image/png;base64,${result.predictions[0].bytesBase64Encoded}`;
      return NextResponse.json<GenerateIconResponse>({
        success: true,
        imageUrl
      });
    } else {
      return NextResponse.json<GenerateIconResponse>(
        { success: false, error: 'No image data received. Please try again with a different prompt.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error generating image:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json<GenerateIconResponse>(
      { success: false, error: `Failed to generate icon: ${errorMessage}` },
      { status: 500 }
    );
  }
}
