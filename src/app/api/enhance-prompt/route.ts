import { NextRequest, NextResponse } from 'next/server';
import { EnhancePromptRequest, EnhancePromptResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: EnhancePromptRequest = await request.json();
    let { userIdea } = body;

    // Use default idea if none provided
    if (!userIdea || userIdea.trim() === '') {
      userIdea = "fun and engaging app icon for attention span training";
    }

    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const geminiModel = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';

    if (!apiKey) {
      return NextResponse.json<EnhancePromptResponse>(
        { success: false, error: 'API key not configured. Please set GOOGLE_AI_API_KEY environment variable.' },
        { status: 500 }
      );
    }

    const chatHistory = [{
      role: "user",
      parts: [{ 
        text: `Given the following brief idea for an attention span training app icon, elaborate it into a detailed, creative, and visually descriptive prompt suitable for an image generation AI. Focus on making it fun, engaging, and relevant to attention span improvement.
        Idea: ${userIdea.trim()}
        Elaborated Prompt:` 
      }]
    }];

    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Prompt enhancement failed.');
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
      const enhancedPrompt = result.candidates[0].content.parts[0].text;
      return NextResponse.json<EnhancePromptResponse>({
        success: true,
        enhancedPrompt
      });
    } else {
      return NextResponse.json<EnhancePromptResponse>(
        { success: false, error: 'Could not enhance prompt. Please try again.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error enhancing prompt:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json<EnhancePromptResponse>(
      { success: false, error: `Failed to enhance prompt: ${errorMessage}` },
      { status: 500 }
    );
  }
}
