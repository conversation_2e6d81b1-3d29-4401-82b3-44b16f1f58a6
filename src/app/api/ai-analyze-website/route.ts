import { NextRequest, NextResponse } from 'next/server';

interface AIAnalysisRequest {
  html: string;
  metadata: {
    title?: string;
    description?: string;
    siteName?: string;
    keywords?: string[];
    url: string;
  };
}

interface AIAnalysisResponse {
  success: boolean;
  analysis?: {
    category: 'ecommerce' | 'blog' | 'portfolio' | 'saas' | 'corporate' | 'creative' | 'educational' | 'other';
    visualStyle: 'modern' | 'minimalist' | 'corporate' | 'playful' | 'elegant' | 'bold' | 'classic';
    brandPersonality: string;
    targetAudience: string;
    primaryPurpose: string;
    keyFeatures: string[];
    industryContext: string;
    designCharacteristics: string[];
  };
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: AIAnalysisRequest = await request.json();
    const { html, metadata } = body;

    if (!html || !metadata.url) {
      return NextResponse.json<AIAnalysisResponse>(
        { success: false, error: 'HTML content and URL are required.' },
        { status: 400 }
      );
    }

    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const geminiModel = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';
    
    if (!apiKey) {
      return NextResponse.json<AIAnalysisResponse>(
        { success: false, error: 'API key not configured. Please set GOOGLE_AI_API_KEY environment variable.' },
        { status: 500 }
      );
    }

    // Extract text content from HTML (simplified)
    const textContent = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .slice(0, 8000); // Limit text for API efficiency

    const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.

Website Information:
- URL: ${metadata.url}
- Title: ${metadata.title || 'Not provided'}
- Description: ${metadata.description || 'Not provided'}
- Site Name: ${metadata.siteName || 'Not provided'}
- Keywords: ${metadata.keywords?.join(', ') || 'Not provided'}

Website Content (first 8000 characters):
${textContent}

Please analyze this website and respond with a JSON object containing the following fields:

{
  "category": "one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other",
  "visualStyle": "one of: modern, minimalist, corporate, playful, elegant, bold, classic",
  "brandPersonality": "brief description of the brand's personality and tone",
  "targetAudience": "description of the primary target audience",
  "primaryPurpose": "main purpose or function of the website",
  "keyFeatures": ["array", "of", "key", "features", "or", "services"],
  "industryContext": "industry or sector this website operates in",
  "designCharacteristics": ["array", "of", "visual", "design", "characteristics"]
}

Base your analysis on:
1. The actual content and purpose of the website
2. The language, tone, and messaging used
3. The apparent target audience and use cases
4. The industry context and competitive landscape
5. Visual and functional characteristics that would inform icon design

Respond ONLY with the JSON object, no additional text.`;

    const chatHistory = [{
      role: "user",
      parts: [{ text: analysisPrompt }]
    }];

    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'AI analysis failed.');
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
      
      let analysisText = result.candidates[0].content.parts[0].text.trim();
      
      // Clean up the response to extract JSON
      analysisText = analysisText.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
      
      try {
        const analysis = JSON.parse(analysisText);
        
        // Validate the response structure
        if (!analysis.category || !analysis.visualStyle) {
          throw new Error('Invalid analysis structure');
        }

        return NextResponse.json<AIAnalysisResponse>({
          success: true,
          analysis
        });
      } catch (parseError) {
        console.error('Failed to parse AI analysis:', parseError);
        return NextResponse.json<AIAnalysisResponse>(
          { success: false, error: 'Failed to parse AI analysis response.' },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json<AIAnalysisResponse>(
        { success: false, error: 'No analysis received from AI.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in AI website analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json<AIAnalysisResponse>(
      { success: false, error: `AI analysis failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}
