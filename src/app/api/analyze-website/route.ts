import { NextRequest, NextResponse } from 'next/server';
import { WebsiteAnalysisRequest, WebsiteAnalysisResponse } from '@/types';
import { scrapeWebsite } from '@/utils/websiteAnalyzer';

export async function POST(request: NextRequest) {
  try {
    const body: WebsiteAnalysisRequest = await request.json();
    const { url } = body;

    if (!url || url.trim() === '') {
      return NextResponse.json<WebsiteAnalysisResponse>(
        { success: false, error: 'Please provide a valid URL.' },
        { status: 400 }
      );
    }

    // Scrape and analyze the website
    const websiteData = await scrapeWebsite(url.trim());

    return NextResponse.json<WebsiteAnalysisResponse>({
      success: true,
      data: websiteData
    });

  } catch (error) {
    console.error('Error analyzing website:', error);
    
    // Handle specific error types
    let errorMessage = 'Failed to analyze website';
    
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = 'Unable to access the website. Please check the URL and try again.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Website took too long to respond. Please try again.';
      } else if (error.message.includes('Invalid URL')) {
        errorMessage = 'Please provide a valid URL (e.g., https://example.com).';
      } else {
        errorMessage = `Analysis failed: ${error.message}`;
      }
    }

    return NextResponse.json<WebsiteAnalysisResponse>(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
