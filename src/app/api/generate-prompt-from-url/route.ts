import { NextRequest, NextResponse } from 'next/server';
import { GeneratePromptFromUrlRequest, GeneratePromptFromUrlResponse } from '@/types';
import { createPromptEnhancementRequest, validatePrompt } from '@/utils/promptGenerator';

export async function POST(request: NextRequest) {
  try {
    const body: GeneratePromptFromUrlRequest = await request.json();
    const { websiteData } = body;

    if (!websiteData || !websiteData.url) {
      return NextResponse.json<GeneratePromptFromUrlResponse>(
        { success: false, error: 'Website data is required.' },
        { status: 400 }
      );
    }

    // Get API key and model from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const geminiModel = process.env.GEMINI_MODEL || 'gemini-2.5-flash-preview-05-20';
    
    if (!apiKey) {
      return NextResponse.json<GeneratePromptFromUrlResponse>(
        { success: false, error: 'API key not configured. Please set GOOGLE_AI_API_KEY environment variable.' },
        { status: 500 }
      );
    }

    // Create the enhancement request
    const enhancementRequest = createPromptEnhancementRequest(websiteData);

    const chatHistory = [{
      role: "user",
      parts: [{ 
        text: enhancementRequest
      }]
    }];

    const payload = { contents: chatHistory };
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Prompt generation failed.');
    }

    const result = await response.json();

    if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
      
      let generatedPrompt = result.candidates[0].content.parts[0].text;
      
      // Clean up the prompt (remove "Enhanced Prompt:" prefix if present)
      generatedPrompt = generatedPrompt.replace(/^Enhanced Prompt:\s*/i, '').trim();
      
      // Validate the generated prompt
      const validation = validatePrompt(generatedPrompt);
      
      if (!validation.isValid && validation.issues) {
        console.warn('Generated prompt has issues:', validation.issues);
      }
      
      const finalPrompt = validation.cleanedPrompt || generatedPrompt;

      return NextResponse.json<GeneratePromptFromUrlResponse>({
        success: true,
        prompt: finalPrompt
      });
    } else {
      return NextResponse.json<GeneratePromptFromUrlResponse>(
        { success: false, error: 'Could not generate prompt from website data. Please try again.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error generating prompt from URL:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json<GeneratePromptFromUrlResponse>(
      { success: false, error: `Failed to generate prompt: ${errorMessage}` },
      { status: 500 }
    );
  }
}
