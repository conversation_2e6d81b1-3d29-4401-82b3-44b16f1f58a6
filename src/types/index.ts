export interface GenerateIconRequest {
  prompt: string;
}

export interface GenerateIconResponse {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

export interface EnhancePromptRequest {
  userIdea: string;
}

export interface EnhancePromptResponse {
  success: boolean;
  enhancedPrompt?: string;
  error?: string;
}

export interface MessageState {
  text: string;
  type: 'info' | 'error' | 'success';
  visible: boolean;
}

// URL-to-Icon Feature Types
export interface WebsiteAnalysisRequest {
  url: string;
}

export interface ScrapedWebsiteData {
  url: string;
  title?: string;
  description?: string;
  siteName?: string;
  keywords?: string[];
  primaryColors?: string[];
  category?: string;
  contentType?: 'ecommerce' | 'blog' | 'portfolio' | 'saas' | 'corporate' | 'creative' | 'educational' | 'other';
  visualStyle?: 'modern' | 'minimalist' | 'corporate' | 'playful' | 'elegant' | 'bold' | 'classic';
  favicon?: string;
  ogImage?: string;
}

export interface WebsiteAnalysisResponse {
  success: boolean;
  data?: ScrapedWebsiteData;
  error?: string;
}

export interface GeneratePromptFromUrlRequest {
  websiteData: ScrapedWebsiteData;
}

export interface GeneratePromptFromUrlResponse {
  success: boolean;
  prompt?: string;
  error?: string;
}

export interface UrlToIconRequest {
  url: string;
}
